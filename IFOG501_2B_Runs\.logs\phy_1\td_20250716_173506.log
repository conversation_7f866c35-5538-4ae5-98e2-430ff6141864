============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 16 17:35:07 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1578 instances
RUN-0007 : 368 luts, 963 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2110 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1576 instances, 368 luts, 963 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7522, tnet num: 2108, tinst num: 1576, tnode num: 10688, tedge num: 12752.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.254716s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (104.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 531947
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1576.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 459203, overlap = 20.25
PHY-3002 : Step(2): len = 440529, overlap = 18
PHY-3002 : Step(3): len = 425205, overlap = 20.25
PHY-3002 : Step(4): len = 416198, overlap = 15.75
PHY-3002 : Step(5): len = 398954, overlap = 15.75
PHY-3002 : Step(6): len = 386461, overlap = 18
PHY-3002 : Step(7): len = 377475, overlap = 13.5
PHY-3002 : Step(8): len = 367728, overlap = 13.5
PHY-3002 : Step(9): len = 355025, overlap = 13.5
PHY-3002 : Step(10): len = 346937, overlap = 13.5
PHY-3002 : Step(11): len = 339459, overlap = 13.5
PHY-3002 : Step(12): len = 327090, overlap = 11.25
PHY-3002 : Step(13): len = 316848, overlap = 11.25
PHY-3002 : Step(14): len = 310659, overlap = 11.25
PHY-3002 : Step(15): len = 301931, overlap = 13.5
PHY-3002 : Step(16): len = 294290, overlap = 13.5
PHY-3002 : Step(17): len = 288902, overlap = 13.5
PHY-3002 : Step(18): len = 280828, overlap = 13.5
PHY-3002 : Step(19): len = 273353, overlap = 13.5
PHY-3002 : Step(20): len = 267950, overlap = 13.5
PHY-3002 : Step(21): len = 263120, overlap = 13.5
PHY-3002 : Step(22): len = 256081, overlap = 13.5
PHY-3002 : Step(23): len = 249736, overlap = 13.5
PHY-3002 : Step(24): len = 245093, overlap = 11.25
PHY-3002 : Step(25): len = 239681, overlap = 11.25
PHY-3002 : Step(26): len = 234716, overlap = 11.25
PHY-3002 : Step(27): len = 230023, overlap = 11.25
PHY-3002 : Step(28): len = 225597, overlap = 13.5
PHY-3002 : Step(29): len = 221661, overlap = 13.5
PHY-3002 : Step(30): len = 215304, overlap = 18
PHY-3002 : Step(31): len = 210915, overlap = 18
PHY-3002 : Step(32): len = 206604, overlap = 20.25
PHY-3002 : Step(33): len = 201850, overlap = 20.25
PHY-3002 : Step(34): len = 196645, overlap = 20.25
PHY-3002 : Step(35): len = 191934, overlap = 20.25
PHY-3002 : Step(36): len = 187666, overlap = 20.25
PHY-3002 : Step(37): len = 183885, overlap = 20.25
PHY-3002 : Step(38): len = 178853, overlap = 20.25
PHY-3002 : Step(39): len = 174069, overlap = 20.25
PHY-3002 : Step(40): len = 170978, overlap = 20.25
PHY-3002 : Step(41): len = 164908, overlap = 20.25
PHY-3002 : Step(42): len = 157720, overlap = 20.25
PHY-3002 : Step(43): len = 155707, overlap = 20.25
PHY-3002 : Step(44): len = 151293, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.84757e-05
PHY-3002 : Step(45): len = 153038, overlap = 18
PHY-3002 : Step(46): len = 152343, overlap = 15.75
PHY-3002 : Step(47): len = 150377, overlap = 15.75
PHY-3002 : Step(48): len = 148691, overlap = 13.5
PHY-3002 : Step(49): len = 143749, overlap = 13.5
PHY-3002 : Step(50): len = 141361, overlap = 11.25
PHY-3002 : Step(51): len = 138876, overlap = 11.25
PHY-3002 : Step(52): len = 137300, overlap = 11.25
PHY-3002 : Step(53): len = 133764, overlap = 11.25
PHY-3002 : Step(54): len = 129130, overlap = 9
PHY-3002 : Step(55): len = 126206, overlap = 9
PHY-3002 : Step(56): len = 125849, overlap = 9
PHY-3002 : Step(57): len = 122194, overlap = 13.5
PHY-3002 : Step(58): len = 120632, overlap = 13.5
PHY-3002 : Step(59): len = 118572, overlap = 13.5
PHY-3002 : Step(60): len = 117060, overlap = 13.5
PHY-3002 : Step(61): len = 110582, overlap = 11.25
PHY-3002 : Step(62): len = 109768, overlap = 11.25
PHY-3002 : Step(63): len = 107860, overlap = 11.25
PHY-3002 : Step(64): len = 106827, overlap = 15.75
PHY-3002 : Step(65): len = 105202, overlap = 13.5
PHY-3002 : Step(66): len = 103878, overlap = 15.75
PHY-3002 : Step(67): len = 101047, overlap = 15.75
PHY-3002 : Step(68): len = 97558.4, overlap = 11.25
PHY-3002 : Step(69): len = 95744.9, overlap = 13.5
PHY-3002 : Step(70): len = 94087.9, overlap = 15.75
PHY-3002 : Step(71): len = 92264.3, overlap = 13.5
PHY-3002 : Step(72): len = 91304, overlap = 15.75
PHY-3002 : Step(73): len = 87834.2, overlap = 13.75
PHY-3002 : Step(74): len = 86416.1, overlap = 13.875
PHY-3002 : Step(75): len = 86579.9, overlap = 16
PHY-3002 : Step(76): len = 85807.2, overlap = 13.75
PHY-3002 : Step(77): len = 84958, overlap = 13.875
PHY-3002 : Step(78): len = 83758.3, overlap = 11.625
PHY-3002 : Step(79): len = 80658.3, overlap = 14.375
PHY-3002 : Step(80): len = 78582, overlap = 14.625
PHY-3002 : Step(81): len = 77724.6, overlap = 16.75
PHY-3002 : Step(82): len = 77564.7, overlap = 16.75
PHY-3002 : Step(83): len = 76230.9, overlap = 14.4375
PHY-3002 : Step(84): len = 74625, overlap = 14.125
PHY-3002 : Step(85): len = 74094.2, overlap = 9.625
PHY-3002 : Step(86): len = 73028.3, overlap = 9.625
PHY-3002 : Step(87): len = 71873.3, overlap = 11.75
PHY-3002 : Step(88): len = 71574.4, overlap = 9.25
PHY-3002 : Step(89): len = 71072.8, overlap = 11.25
PHY-3002 : Step(90): len = 70644, overlap = 9
PHY-3002 : Step(91): len = 70432, overlap = 9
PHY-3002 : Step(92): len = 69689.8, overlap = 9
PHY-3002 : Step(93): len = 68617.5, overlap = 9
PHY-3002 : Step(94): len = 67331.3, overlap = 9
PHY-3002 : Step(95): len = 66681.1, overlap = 6.75
PHY-3002 : Step(96): len = 66236, overlap = 6.75
PHY-3002 : Step(97): len = 65614.2, overlap = 4.5
PHY-3002 : Step(98): len = 65088.7, overlap = 2.25
PHY-3002 : Step(99): len = 63872.1, overlap = 4.5
PHY-3002 : Step(100): len = 63182.8, overlap = 2.25
PHY-3002 : Step(101): len = 62582.5, overlap = 4.5
PHY-3002 : Step(102): len = 62072.4, overlap = 6.75
PHY-3002 : Step(103): len = 61494, overlap = 6.75
PHY-3002 : Step(104): len = 60805.6, overlap = 4.5
PHY-3002 : Step(105): len = 60113.8, overlap = 6.75
PHY-3002 : Step(106): len = 59321.6, overlap = 6.75
PHY-3002 : Step(107): len = 59149.8, overlap = 2.25
PHY-3002 : Step(108): len = 57664.4, overlap = 2.25
PHY-3002 : Step(109): len = 57334.9, overlap = 2.25
PHY-3002 : Step(110): len = 56377.6, overlap = 2.3125
PHY-3002 : Step(111): len = 55111.1, overlap = 4.5
PHY-3002 : Step(112): len = 54839.2, overlap = 4.5
PHY-3002 : Step(113): len = 54217.6, overlap = 6.75
PHY-3002 : Step(114): len = 49635.1, overlap = 9.75
PHY-3002 : Step(115): len = 48818.9, overlap = 9.75
PHY-3002 : Step(116): len = 48443.5, overlap = 9.6875
PHY-3002 : Step(117): len = 48408.9, overlap = 9.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000196951
PHY-3002 : Step(118): len = 48522.3, overlap = 9.6875
PHY-3002 : Step(119): len = 48666.3, overlap = 9.6875
PHY-3002 : Step(120): len = 48718.9, overlap = 9.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000393903
PHY-3002 : Step(121): len = 48477.9, overlap = 9.6875
PHY-3002 : Step(122): len = 48426.8, overlap = 9.6875
PHY-3001 : Before Legalized: Len = 48426.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009215s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 48706.2, Over = 5.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065591s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (95.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000133773
PHY-3002 : Step(123): len = 49013.5, overlap = 23.9062
PHY-3002 : Step(124): len = 49040, overlap = 21.5312
PHY-3002 : Step(125): len = 48627.9, overlap = 21.4375
PHY-3002 : Step(126): len = 47988.5, overlap = 21.25
PHY-3002 : Step(127): len = 47611.8, overlap = 21.5312
PHY-3002 : Step(128): len = 47327, overlap = 22.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000267545
PHY-3002 : Step(129): len = 47024.6, overlap = 22.0312
PHY-3002 : Step(130): len = 46838.1, overlap = 21.9688
PHY-3002 : Step(131): len = 46640.1, overlap = 22.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000535091
PHY-3002 : Step(132): len = 46416.5, overlap = 22.0312
PHY-3002 : Step(133): len = 46366.4, overlap = 22.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063124s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.39063e-05
PHY-3002 : Step(134): len = 46414.3, overlap = 71.7188
PHY-3002 : Step(135): len = 46565.3, overlap = 70.6562
PHY-3002 : Step(136): len = 47073.9, overlap = 67.5
PHY-3002 : Step(137): len = 47809.1, overlap = 65.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127813
PHY-3002 : Step(138): len = 47642.3, overlap = 60.7188
PHY-3002 : Step(139): len = 47893.9, overlap = 55.4688
PHY-3002 : Step(140): len = 48045.1, overlap = 55.4062
PHY-3002 : Step(141): len = 48291.8, overlap = 54.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000255625
PHY-3002 : Step(142): len = 48318.2, overlap = 54.25
PHY-3002 : Step(143): len = 48919.5, overlap = 52.8438
PHY-3002 : Step(144): len = 49597.8, overlap = 44.9375
PHY-3002 : Step(145): len = 50063.3, overlap = 42.8438
PHY-3002 : Step(146): len = 50237.2, overlap = 41.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7522, tnet num: 2108, tinst num: 1576, tnode num: 10688, tedge num: 12752.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 104.66 peak overflow 3.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2110.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53656, over cnt = 225(0%), over = 971, worst = 29
PHY-1001 : End global iterations;  0.076315s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (143.3%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 25.57, top10 = 15.87, top15 = 11.34.
PHY-1001 : End incremental global routing;  0.129487s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (120.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074845s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.233634s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1600/2110.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53656, over cnt = 225(0%), over = 971, worst = 29
PHY-1002 : len = 61152, over cnt = 186(0%), over = 445, worst = 14
PHY-1002 : len = 66152, over cnt = 30(0%), over = 32, worst = 2
PHY-1002 : len = 66616, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 67256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107291s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (116.5%)

PHY-1001 : Congestion index: top1 = 41.75, top5 = 25.70, top10 = 18.06, top15 = 13.35.
OPT-1001 : End congestion update;  0.153426s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060451s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217104s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.0%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.702157s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (106.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 74 SEQ with LUT/SLICE
SYN-4006 : 121 single LUT's are left
SYN-4006 : 701 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1069/1380 primitive instances ...
PHY-3001 : End packing;  0.053207s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 806 instances, 757 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49942, Over = 66.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6310, tnet num: 1928, tinst num: 806, tnode num: 8608, tedge num: 11147.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294792s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.07476e-05
PHY-3002 : Step(147): len = 49382, overlap = 63.5
PHY-3002 : Step(148): len = 48896.9, overlap = 63.5
PHY-3002 : Step(149): len = 48251.8, overlap = 63.75
PHY-3002 : Step(150): len = 47984.4, overlap = 67.75
PHY-3002 : Step(151): len = 47882.2, overlap = 65.75
PHY-3002 : Step(152): len = 47449, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.14951e-05
PHY-3002 : Step(153): len = 47985.3, overlap = 63.25
PHY-3002 : Step(154): len = 48295.3, overlap = 61
PHY-3002 : Step(155): len = 49438.6, overlap = 57.75
PHY-3002 : Step(156): len = 50218.5, overlap = 58.5
PHY-3002 : Step(157): len = 50423.4, overlap = 56.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00012299
PHY-3002 : Step(158): len = 50716.8, overlap = 56
PHY-3002 : Step(159): len = 51223.5, overlap = 51.75
PHY-3001 : Before Legalized: Len = 51223.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075383s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (207.3%)

PHY-3001 : After Legalized: Len = 64804.1, Over = 0
PHY-3001 : Trial Legalized: Len = 64804.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056456s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000552288
PHY-3002 : Step(160): len = 61361.9, overlap = 12.5
PHY-3002 : Step(161): len = 59412.6, overlap = 13
PHY-3002 : Step(162): len = 57371, overlap = 22
PHY-3002 : Step(163): len = 56295.6, overlap = 26.25
PHY-3002 : Step(164): len = 55700.7, overlap = 27.5
PHY-3002 : Step(165): len = 55443.5, overlap = 28.75
PHY-3002 : Step(166): len = 55093.7, overlap = 29.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00110458
PHY-3002 : Step(167): len = 55664, overlap = 29
PHY-3002 : Step(168): len = 55786.1, overlap = 29.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00220915
PHY-3002 : Step(169): len = 55936.7, overlap = 29.5
PHY-3002 : Step(170): len = 56078.1, overlap = 30
PHY-3001 : Before Legalized: Len = 56078.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005182s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 61121.5, Over = 0
PHY-3001 : Legalized: Len = 61121.5, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005378s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 3, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 61227.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6310, tnet num: 1928, tinst num: 806, tnode num: 8608, tedge num: 11147.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 29/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68056, over cnt = 149(0%), over = 258, worst = 8
PHY-1002 : len = 69320, over cnt = 91(0%), over = 126, worst = 5
PHY-1002 : len = 70592, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 70688, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 70816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.142803s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (131.3%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 23.05, top10 = 17.80, top15 = 13.89.
PHY-1001 : End incremental global routing;  0.197948s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (118.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061566s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.289417s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 211, reserve = 179, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005531s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (282.5%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 23.05, top10 = 17.80, top15 = 13.89.
OPT-1001 : End congestion update;  0.051739s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059929s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.113756s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (109.9%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056513s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005675s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 23.05, top10 = 17.80, top15 = 13.89.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058203s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.848393s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (105.0%)

RUN-1003 : finish command "place" in  5.671680s wall, 8.281250s user + 3.046875s system = 11.328125s CPU (199.7%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 215 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6310, tnet num: 1928, tinst num: 806, tnode num: 8608, tedge num: 11147.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67384, over cnt = 149(0%), over = 257, worst = 8
PHY-1002 : len = 68624, over cnt = 95(0%), over = 132, worst = 5
PHY-1002 : len = 70024, over cnt = 7(0%), over = 9, worst = 3
PHY-1002 : len = 70160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140670s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (133.3%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.90, top10 = 17.69, top15 = 13.78.
PHY-1001 : End global routing;  0.191057s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (122.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.208702s wall, 3.031250s user + 0.140625s system = 3.171875s CPU (98.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34256, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.083513s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (98.1%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.089686s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180920, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.370717s wall, 2.656250s user + 0.171875s system = 2.828125s CPU (206.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.638   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.701  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.335605s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.8%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.706412s wall, 2.984375s user + 0.171875s system = 3.156250s CPU (185.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180920, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015060s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180928, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023803s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (196.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020077s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.638   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.701  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338987s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.168588s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.9%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.681793s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (103.1%)

PHY-1003 : Routed, final wirelength = 180984
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.012287s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (127.2%)

PHY-1001 : End detail routing;  6.877523s wall, 7.984375s user + 0.312500s system = 8.296875s CPU (120.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6310, tnet num: 1928, tinst num: 806, tnode num: 8608, tedge num: 11147.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_20.sr slack -81ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_61.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_65.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_68.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2791ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2777ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_80.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/tx_data_dy_b[2]_syn_23.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[7]_syn_23.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6420, tnet num: 1983, tinst num: 861, tnode num: 8718, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[2]_syn_23_mi[0] slack -663ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -839ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -311ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -506ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -784ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[7]_syn_23_mi[0] slack -2ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -773ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -224ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -619ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -639ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -1129ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -536ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -578ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -473ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -583ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -538ps
RUN-1001 : End hold fix;  3.111429s wall, 3.171875s user + 0.140625s system = 3.312500s CPU (106.5%)

RUN-1003 : finish command "route" in  10.514422s wall, 11.703125s user + 0.468750s system = 12.171875s CPU (115.8%)

RUN-1004 : used memory is 498 MB, reserved memory is 469 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      876   out of  19600    4.47%
#reg                     1052   out of  19600    5.37%
#le                      1577
  #lut only               525   out of   1577   33.29%
  #reg only               701   out of   1577   44.45%
  #lut&reg                351   out of   1577   22.26%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         484
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1577   |680     |196     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1066   |277     |134     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |30      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |443    |91      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |146    |31      |15      |118     |0       |0       |
|    modu                    |Modulation                                       |95     |38      |14      |93      |0       |1       |
|    rs422                   |Rs422Output                                      |322    |71      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |184    |131     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |121    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1373  
    #2          2       335   
    #3          3       109   
    #4          4        25   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6420, tnet num: 1983, tinst num: 861, tnode num: 8718, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1983 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 861
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1985, pip num: 14641
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1276 valid insts, and 39060 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.421643s wall, 17.718750s user + 0.062500s system = 17.781250s CPU (519.7%)

RUN-1004 : used memory is 517 MB, reserved memory is 489 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_173506.log"

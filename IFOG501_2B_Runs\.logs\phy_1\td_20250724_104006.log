============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 24 10:40:06 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1552 instances
RUN-0007 : 368 luts, 944 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2067 nets
RUN-1001 : 1527 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1550 instances, 368 luts, 944 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7367, tnet num: 2065, tinst num: 1550, tnode num: 10456, tedge num: 12441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268225s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 524610
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1550.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 466058, overlap = 20.25
PHY-3002 : Step(2): len = 442742, overlap = 9
PHY-3002 : Step(3): len = 430829, overlap = 15.75
PHY-3002 : Step(4): len = 422037, overlap = 6.75
PHY-3002 : Step(5): len = 400025, overlap = 18
PHY-3002 : Step(6): len = 383262, overlap = 11.25
PHY-3002 : Step(7): len = 375978, overlap = 13.5
PHY-3002 : Step(8): len = 366564, overlap = 13.5
PHY-3002 : Step(9): len = 326396, overlap = 15.75
PHY-3002 : Step(10): len = 305562, overlap = 18
PHY-3002 : Step(11): len = 298439, overlap = 11.25
PHY-3002 : Step(12): len = 295430, overlap = 11.25
PHY-3002 : Step(13): len = 281793, overlap = 11.25
PHY-3002 : Step(14): len = 278227, overlap = 11.25
PHY-3002 : Step(15): len = 273314, overlap = 11.25
PHY-3002 : Step(16): len = 264490, overlap = 11.25
PHY-3002 : Step(17): len = 258742, overlap = 11.25
PHY-3002 : Step(18): len = 256082, overlap = 11.25
PHY-3002 : Step(19): len = 244938, overlap = 20.25
PHY-3002 : Step(20): len = 238031, overlap = 20.25
PHY-3002 : Step(21): len = 235141, overlap = 20.25
PHY-3002 : Step(22): len = 230348, overlap = 20.25
PHY-3002 : Step(23): len = 193592, overlap = 15.75
PHY-3002 : Step(24): len = 188909, overlap = 20.25
PHY-3002 : Step(25): len = 186384, overlap = 20.25
PHY-3002 : Step(26): len = 179484, overlap = 18
PHY-3002 : Step(27): len = 171229, overlap = 20.25
PHY-3002 : Step(28): len = 168682, overlap = 20.25
PHY-3002 : Step(29): len = 165889, overlap = 20.25
PHY-3002 : Step(30): len = 159901, overlap = 18
PHY-3002 : Step(31): len = 158116, overlap = 20.25
PHY-3002 : Step(32): len = 152481, overlap = 18
PHY-3002 : Step(33): len = 145643, overlap = 18
PHY-3002 : Step(34): len = 142786, overlap = 15.75
PHY-3002 : Step(35): len = 139387, overlap = 15.75
PHY-3002 : Step(36): len = 134742, overlap = 13.5
PHY-3002 : Step(37): len = 133253, overlap = 15.75
PHY-3002 : Step(38): len = 128637, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000107354
PHY-3002 : Step(39): len = 129289, overlap = 13.5
PHY-3002 : Step(40): len = 128173, overlap = 13.5
PHY-3002 : Step(41): len = 127131, overlap = 15.75
PHY-3002 : Step(42): len = 125791, overlap = 15.75
PHY-3002 : Step(43): len = 119878, overlap = 9
PHY-3002 : Step(44): len = 115902, overlap = 11.25
PHY-3002 : Step(45): len = 114203, overlap = 9
PHY-3002 : Step(46): len = 112738, overlap = 9
PHY-3002 : Step(47): len = 109943, overlap = 13.5
PHY-3002 : Step(48): len = 107842, overlap = 13.5
PHY-3002 : Step(49): len = 107144, overlap = 11.25
PHY-3002 : Step(50): len = 105894, overlap = 11.25
PHY-3002 : Step(51): len = 103657, overlap = 13.5
PHY-3002 : Step(52): len = 102711, overlap = 13.5
PHY-3002 : Step(53): len = 100772, overlap = 15.75
PHY-3002 : Step(54): len = 97825.7, overlap = 15.75
PHY-3002 : Step(55): len = 96029.9, overlap = 15.75
PHY-3002 : Step(56): len = 94634.7, overlap = 15.75
PHY-3002 : Step(57): len = 91571.7, overlap = 15.75
PHY-3002 : Step(58): len = 88633, overlap = 15.75
PHY-3002 : Step(59): len = 87875.9, overlap = 13.5
PHY-3002 : Step(60): len = 84744.8, overlap = 15.75
PHY-3002 : Step(61): len = 84082.9, overlap = 15.75
PHY-3002 : Step(62): len = 83376.5, overlap = 13.5
PHY-3002 : Step(63): len = 83026.4, overlap = 13.5
PHY-3002 : Step(64): len = 82473.7, overlap = 16.125
PHY-3002 : Step(65): len = 81994.9, overlap = 16.25
PHY-3002 : Step(66): len = 81047.6, overlap = 14.625
PHY-3002 : Step(67): len = 79215.3, overlap = 15.1875
PHY-3002 : Step(68): len = 77329.2, overlap = 15.5
PHY-3002 : Step(69): len = 76648.5, overlap = 15.6875
PHY-3002 : Step(70): len = 76035.6, overlap = 17.9375
PHY-3002 : Step(71): len = 75495.5, overlap = 18.0625
PHY-3002 : Step(72): len = 74726.2, overlap = 18.3125
PHY-3002 : Step(73): len = 73827.2, overlap = 16.125
PHY-3002 : Step(74): len = 72998.5, overlap = 16.3125
PHY-3002 : Step(75): len = 70665.5, overlap = 16.1875
PHY-3002 : Step(76): len = 69033.1, overlap = 16.375
PHY-3002 : Step(77): len = 68394.3, overlap = 16.1875
PHY-3002 : Step(78): len = 67823.9, overlap = 16.1875
PHY-3002 : Step(79): len = 67558.8, overlap = 16
PHY-3002 : Step(80): len = 66651, overlap = 18.25
PHY-3002 : Step(81): len = 65412.3, overlap = 15.875
PHY-3002 : Step(82): len = 64806.3, overlap = 17.8125
PHY-3002 : Step(83): len = 63931.1, overlap = 17.8125
PHY-3002 : Step(84): len = 63662.1, overlap = 17.6875
PHY-3002 : Step(85): len = 63460.5, overlap = 17.5
PHY-3002 : Step(86): len = 63061, overlap = 17.375
PHY-3002 : Step(87): len = 62829.4, overlap = 15.0625
PHY-3002 : Step(88): len = 62565, overlap = 14.75
PHY-3002 : Step(89): len = 62135.2, overlap = 14.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000214708
PHY-3002 : Step(90): len = 62449.1, overlap = 14.6875
PHY-3002 : Step(91): len = 62495.7, overlap = 16.9375
PHY-3002 : Step(92): len = 62506.4, overlap = 16.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000429415
PHY-3002 : Step(93): len = 62756.5, overlap = 16.9375
PHY-3002 : Step(94): len = 62870.9, overlap = 12.4375
PHY-3001 : Before Legalized: Len = 62870.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007880s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (198.3%)

PHY-3001 : After Legalized: Len = 66182.6, Over = 1.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068073s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(95): len = 66093.3, overlap = 9.875
PHY-3002 : Step(96): len = 65497.9, overlap = 7
PHY-3002 : Step(97): len = 64777.6, overlap = 6.46875
PHY-3002 : Step(98): len = 64223.8, overlap = 6.3125
PHY-3002 : Step(99): len = 63391.4, overlap = 6.375
PHY-3002 : Step(100): len = 62246.1, overlap = 5.5625
PHY-3002 : Step(101): len = 61737.4, overlap = 5.375
PHY-3002 : Step(102): len = 61347.7, overlap = 5.5
PHY-3002 : Step(103): len = 60911.6, overlap = 5.375
PHY-3002 : Step(104): len = 60325.4, overlap = 5.5
PHY-3002 : Step(105): len = 60296.1, overlap = 5.5
PHY-3002 : Step(106): len = 59814.7, overlap = 5.375
PHY-3002 : Step(107): len = 59285, overlap = 5.375
PHY-3002 : Step(108): len = 58619, overlap = 5.6875
PHY-3002 : Step(109): len = 58509.9, overlap = 5.75
PHY-3002 : Step(110): len = 58229.8, overlap = 5.8125
PHY-3002 : Step(111): len = 57467.3, overlap = 6.75
PHY-3002 : Step(112): len = 56540.5, overlap = 5.6875
PHY-3002 : Step(113): len = 55432.6, overlap = 10.75
PHY-3002 : Step(114): len = 54593.9, overlap = 10.8125
PHY-3002 : Step(115): len = 54020.6, overlap = 10.8125
PHY-3002 : Step(116): len = 53568.5, overlap = 11.0625
PHY-3002 : Step(117): len = 53378.1, overlap = 11.4688
PHY-3002 : Step(118): len = 53372.5, overlap = 11.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000851172
PHY-3002 : Step(119): len = 53313.2, overlap = 11.4375
PHY-3002 : Step(120): len = 53362.6, overlap = 11.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00170234
PHY-3002 : Step(121): len = 53267.6, overlap = 11.7188
PHY-3002 : Step(122): len = 53346.3, overlap = 11.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056221s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109471
PHY-3002 : Step(123): len = 53392.9, overlap = 46.7812
PHY-3002 : Step(124): len = 55046.4, overlap = 41.9688
PHY-3002 : Step(125): len = 55352, overlap = 38.125
PHY-3002 : Step(126): len = 54848.8, overlap = 37.6562
PHY-3002 : Step(127): len = 54416.8, overlap = 37.5625
PHY-3002 : Step(128): len = 54269.5, overlap = 38.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000218942
PHY-3002 : Step(129): len = 54181, overlap = 35.2812
PHY-3002 : Step(130): len = 54959.2, overlap = 37.75
PHY-3002 : Step(131): len = 55353.7, overlap = 35.375
PHY-3002 : Step(132): len = 55060.6, overlap = 35.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000437885
PHY-3002 : Step(133): len = 54967.5, overlap = 34.4062
PHY-3002 : Step(134): len = 54949.1, overlap = 33.1875
PHY-3002 : Step(135): len = 55355.8, overlap = 27.5938
PHY-3002 : Step(136): len = 55945.4, overlap = 26.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7367, tnet num: 2065, tinst num: 1550, tnode num: 10456, tedge num: 12441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.97 peak overflow 2.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58616, over cnt = 214(0%), over = 953, worst = 22
PHY-1001 : End global iterations;  0.062848s wall, 0.093750s user + 0.062500s system = 0.156250s CPU (248.6%)

PHY-1001 : Congestion index: top1 = 44.53, top5 = 25.02, top10 = 15.92, top15 = 11.40.
PHY-1001 : End incremental global routing;  0.111738s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (181.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062856s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.202416s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (146.7%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1579/2067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58616, over cnt = 214(0%), over = 953, worst = 22
PHY-1002 : len = 66600, over cnt = 134(0%), over = 261, worst = 22
PHY-1002 : len = 69136, over cnt = 37(0%), over = 53, worst = 5
PHY-1002 : len = 69600, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 69872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.086348s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (126.7%)

PHY-1001 : Congestion index: top1 = 37.26, top5 = 24.12, top10 = 17.37, top15 = 12.94.
OPT-1001 : End congestion update;  0.126995s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052915s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.182984s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.626871s wall, 0.656250s user + 0.078125s system = 0.734375s CPU (117.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 661 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1029/1333 primitive instances ...
PHY-3001 : End packing;  0.045731s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 775 instances
RUN-1001 : 362 mslices, 362 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1886 nets
RUN-1001 : 1353 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 773 instances, 724 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56148.6, Over = 49.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6128, tnet num: 1884, tinst num: 773, tnode num: 8336, tedge num: 10786.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.270037s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.55934e-05
PHY-3002 : Step(137): len = 55503.2, overlap = 52.25
PHY-3002 : Step(138): len = 54921.7, overlap = 52
PHY-3002 : Step(139): len = 54589.1, overlap = 49.75
PHY-3002 : Step(140): len = 54564.6, overlap = 47.75
PHY-3002 : Step(141): len = 54221.4, overlap = 50.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.11868e-05
PHY-3002 : Step(142): len = 54377.6, overlap = 48.5
PHY-3002 : Step(143): len = 55079.2, overlap = 47
PHY-3002 : Step(144): len = 55215.6, overlap = 47.75
PHY-3002 : Step(145): len = 55271.3, overlap = 45.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000182374
PHY-3002 : Step(146): len = 55504.8, overlap = 44.75
PHY-3002 : Step(147): len = 56090.7, overlap = 44.25
PHY-3001 : Before Legalized: Len = 56090.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071245s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (241.2%)

PHY-3001 : After Legalized: Len = 68248.5, Over = 0
PHY-3001 : Trial Legalized: Len = 68248.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047150s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00121631
PHY-3002 : Step(148): len = 64457.1, overlap = 5.25
PHY-3002 : Step(149): len = 62297.5, overlap = 14
PHY-3002 : Step(150): len = 61153.3, overlap = 14.25
PHY-3002 : Step(151): len = 59981.3, overlap = 19.25
PHY-3002 : Step(152): len = 59201.5, overlap = 22.75
PHY-3002 : Step(153): len = 58680.9, overlap = 23.5
PHY-3002 : Step(154): len = 58222.6, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00243263
PHY-3002 : Step(155): len = 58395, overlap = 23.5
PHY-3002 : Step(156): len = 58436.5, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00486525
PHY-3002 : Step(157): len = 58539.6, overlap = 24.25
PHY-3002 : Step(158): len = 58539.6, overlap = 24.25
PHY-3001 : Before Legalized: Len = 58539.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005132s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (304.5%)

PHY-3001 : After Legalized: Len = 63241.8, Over = 0
PHY-3001 : Legalized: Len = 63241.8, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005548s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 4, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 63325.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6128, tnet num: 1884, tinst num: 773, tnode num: 8336, tedge num: 10786.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 34/1886.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69728, over cnt = 143(0%), over = 228, worst = 6
PHY-1002 : len = 70912, over cnt = 64(0%), over = 80, worst = 4
PHY-1002 : len = 71848, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 72144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120462s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.8%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 23.33, top10 = 17.86, top15 = 13.88.
PHY-1001 : End incremental global routing;  0.172966s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (117.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061781s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (75.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.263955s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.6%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1680/1886.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005544s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.8%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 23.33, top10 = 17.86, top15 = 13.88.
OPT-1001 : End congestion update;  0.050510s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046635s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 733 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 773 instances, 724 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63336.6, Over = 0
PHY-3001 : End spreading;  0.004750s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (328.9%)

PHY-3001 : Final: Len = 63336.6, Over = 0
PHY-3001 : End incremental legalization;  0.033348s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (93.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.142831s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (109.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045254s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (69.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1676/1886.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011232s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.1%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 23.32, top10 = 17.88, top15 = 13.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046517s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.820326s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (99.0%)

RUN-1003 : finish command "place" in  4.941863s wall, 7.156250s user + 3.203125s system = 10.359375s CPU (209.6%)

RUN-1004 : used memory is 200 MB, reserved memory is 167 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 775 instances
RUN-1001 : 362 mslices, 362 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1886 nets
RUN-1001 : 1353 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6128, tnet num: 1884, tinst num: 773, tnode num: 8336, tedge num: 10786.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 362 mslices, 362 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1884 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69048, over cnt = 143(0%), over = 231, worst = 6
PHY-1002 : len = 70312, over cnt = 59(0%), over = 74, worst = 4
PHY-1002 : len = 71304, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124936s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (150.1%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.17, top10 = 17.79, top15 = 13.79.
PHY-1001 : End global routing;  0.181099s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (138.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.114275s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30136, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.011854s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 525, reserve = 499, peak = 525.
PHY-1001 : End phase 1; 1.018046s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 172408, over cnt = 41(0%), over = 41, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 500, peak = 527.
PHY-1001 : End initial routed; 1.410825s wall, 2.593750s user + 0.109375s system = 2.703125s CPU (191.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1678(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.940  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.330154s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.741068s wall, 2.921875s user + 0.109375s system = 3.031250s CPU (174.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 172408, over cnt = 41(0%), over = 41, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013819s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (113.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 172256, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.046628s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (167.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 172312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025132s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (186.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1678(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.940  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.317605s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.161694s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.687304s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (106.8%)

PHY-1003 : Routed, final wirelength = 172312
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.009826s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.736583s wall, 7.906250s user + 0.171875s system = 8.078125s CPU (119.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6128, tnet num: 1884, tinst num: 773, tnode num: 8336, tedge num: 10786.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_74.mi[0] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_74.mi[1] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_80.mi[0] slack -3083ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_80.mi[1] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2951ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_86.mi[0] slack -2951ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_86.mi[1] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_89.mi[0] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_89.mi[1] slack -3083ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_92.mi[0] slack -2963ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_92.mi[1] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[0]_syn_21.mi[0] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/tx_data_dy_b[2]_syn_18.mi[0] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1940, tinst num: 829, tnode num: 8448, tedge num: 10898.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[2]_syn_18_mi[0] slack -587ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[0]_syn_21_mi[0] slack -782ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -657ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -866ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -672ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -628ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -534ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -731ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -486ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -1024ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -648ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_92_mi[0] slack -710ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[0] slack -888ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -204ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_92_mi[1] slack -271ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[1] slack -532ps
RUN-1001 : End hold fix;  2.893969s wall, 2.937500s user + 0.265625s system = 3.203125s CPU (110.7%)

RUN-1003 : finish command "route" in  10.130412s wall, 11.359375s user + 0.484375s system = 11.843750s CPU (116.9%)

RUN-1004 : used memory is 535 MB, reserved memory is 509 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      861   out of  19600    4.39%
#reg                     1015   out of  19600    5.18%
#le                      1522
  #lut only               507   out of   1522   33.31%
  #reg only               661   out of   1522   43.43%
  #lut&reg                354   out of   1522   23.26%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         450
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1522   |672     |189     |1048    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1017   |270     |125     |824     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |445    |98      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |34      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |9       |0       |18      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |9       |0       |13      |0       |0       |
|    integ                   |Integration                                      |137    |24      |15      |109     |0       |0       |
|    modu                    |Modulation                                       |68     |41      |7       |66      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |67      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |21      |6       |16      |0       |0       |
|  u_uart                    |UART_Control                                     |179    |133     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |21      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |119    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |302    |257     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1370  
    #2          2       295   
    #3          3       108   
    #4          4        27   
    #5        5-10       66   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1940, tinst num: 829, tnode num: 8448, tedge num: 10898.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 829
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1942, pip num: 14270
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1280 valid insts, and 38195 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.915459s wall, 16.984375s user + 0.031250s system = 17.015625s CPU (583.6%)

RUN-1004 : used memory is 549 MB, reserved memory is 519 MB, peak memory is 676 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250724_104006.log"

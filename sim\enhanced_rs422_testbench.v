`timescale 1ns / 1ps

////////////////////////////////////////////////////////////////////////////////
// Testbench for Enhanced Rs422Output Module
// Tests temperature compensation and improved edge detection
////////////////////////////////////////////////////////////////////////////////

module enhanced_rs422_testbench;

    // Parameters
    parameter CLK_PERIOD = 8.33;  // 120MHz clock period in ns
    parameter FILTER_DEPTH = 16;
    parameter TEMP_COMP_EN = 1;
    
    // Inputs
    reg rst_n;
    reg clk;
    reg output_drive;
    reg RxTransmit;
    reg polarity;
    reg [55:0] din;
    reg [15:0] temp_data;
    
    // Outputs
    wire transmit;
    wire [31:0] dout;
    wire signal_quality_ok;
    
    // Test variables
    reg [31:0] test_counter;
    reg [15:0] temperature_sweep;
    
    // Instantiate the Unit Under Test (UUT)
    Rs422Output #(
        .bPOLAR(1),
        .iWID_IN(56),
        .iWID_OUT(32),
        .iDELAYED(120),
        .iOUTPUT_SCALE(1350),
        .FILTER_DEPTH(FILTER_DEPTH),
        .TEMP_COMP_EN(TEMP_COMP_EN)
    ) uut (
        .rst_n(rst_n),
        .clk(clk),
        .output_drive(output_drive),
        .RxTransmit(RxTransmit),
        .polarity(polarity),
        .din(din),
        .temp_data(temp_data),
        .transmit(transmit),
        .dout(dout),
        .signal_quality_ok(signal_quality_ok)
    );
    
    // Clock generation
    initial begin
        clk = 0;
        forever #(CLK_PERIOD/2) clk = ~clk;
    end
    
    // Test sequence
    initial begin
        // Initialize Inputs
        rst_n = 0;
        output_drive = 0;
        RxTransmit = 0;
        polarity = 0;
        din = 56'h0;
        temp_data = 16'd250; // 25.0°C
        test_counter = 0;
        temperature_sweep = 16'd200; // Start at 20.0°C
        
        // Wait for global reset
        #100;
        rst_n = 1;
        #100;
        
        $display("=== Enhanced Rs422Output Testbench Started ===");
        
        // Test 1: Basic functionality at room temperature
        $display("Test 1: Basic functionality at 25°C");
        test_basic_functionality();
        
        // Test 2: Temperature sweep test
        $display("Test 2: Temperature compensation test");
        test_temperature_compensation();
        
        // Test 3: Noise immunity test
        $display("Test 3: Noise immunity test");
        test_noise_immunity();
        
        // Test 4: Edge detection robustness
        $display("Test 4: Edge detection robustness");
        test_edge_detection();
        
        $display("=== All tests completed ===");
        $finish;
    end
    
    // Task: Basic functionality test
    task test_basic_functionality;
        begin
            temp_data = 16'd250; // 25.0°C
            din = 56'h123456789ABCDEF;
            
            // Generate normal trigger sequence
            repeat(10) begin
                generate_trigger_sequence();
                #1000;
            end
            
            // Check if signal quality is good
            if(signal_quality_ok)
                $display("✓ Signal quality OK at room temperature");
            else
                $display("✗ Signal quality poor at room temperature");
        end
    endtask
    
    // Task: Temperature compensation test
    task test_temperature_compensation;
        integer temp_idx;
        reg [15:0] test_temps [0:6];
        begin
            // Test temperatures: -40°C, -20°C, 0°C, 25°C, 50°C, 70°C, 85°C
            test_temps[0] = 16'd160; // -40.0°C
            test_temps[1] = 16'd200; // -20.0°C
            test_temps[2] = 16'd273; //   0.0°C
            test_temps[3] = 16'd250; //  25.0°C
            test_temps[4] = 16'd500; //  50.0°C
            test_temps[5] = 16'd700; //  70.0°C
            test_temps[6] = 16'd850; //  85.0°C
            
            for(temp_idx = 0; temp_idx < 7; temp_idx = temp_idx + 1) begin
                temp_data = test_temps[temp_idx];
                $display("Testing at temperature: %d.%d°C", 
                        (temp_data/10), (temp_data%10));
                
                // Allow temperature compensation to settle
                #1000;
                
                // Generate trigger sequences
                repeat(5) begin
                    generate_trigger_sequence();
                    #2000;
                end
                
                // Monitor signal quality
                if(signal_quality_ok)
                    $display("✓ Signal quality OK at %d.%d°C", 
                            (temp_data/10), (temp_data%10));
                else
                    $display("✗ Signal quality poor at %d.%d°C", 
                            (temp_data/10), (temp_data%10));
            end
        end
    endtask
    
    // Task: Noise immunity test
    task test_noise_immunity;
        integer noise_idx;
        begin
            temp_data = 16'd250; // 25.0°C
            
            // Test with various noise levels
            for(noise_idx = 0; noise_idx < 5; noise_idx = noise_idx + 1) begin
                $display("Noise test level %d", noise_idx);
                
                // Generate noisy trigger signal
                generate_noisy_trigger(noise_idx);
                #5000;
                
                // Check signal quality response
                $display("Signal quality: %b", signal_quality_ok);
            end
        end
    endtask
    
    // Task: Edge detection robustness test
    task test_edge_detection;
        begin
            temp_data = 16'd250; // 25.0°C
            
            // Test slow rising edge
            $display("Testing slow rising edge");
            generate_slow_edge();
            #2000;
            
            // Test fast rising edge
            $display("Testing fast rising edge");
            generate_fast_edge();
            #2000;
            
            // Test multiple edges
            $display("Testing multiple edges");
            generate_multiple_edges();
            #2000;
        end
    endtask
    
    // Task: Generate normal trigger sequence
    task generate_trigger_sequence;
        begin
            // Generate polarity signal
            polarity = 1;
            #100;
            
            // Generate RxTransmit rising edge
            RxTransmit = 1;
            #(CLK_PERIOD * 50); // Hold high for 50 clock cycles
            RxTransmit = 0;
            
            // Wait for processing
            #(CLK_PERIOD * 200);
            
            // Generate output_drive signal
            output_drive = 1;
            #(CLK_PERIOD * 10);
            output_drive = 0;
            
            polarity = 0;
            #100;
        end
    endtask
    
    // Task: Generate noisy trigger signal
    task generate_noisy_trigger;
        input integer noise_level;
        integer i;
        begin
            polarity = 1;
            
            for(i = 0; i < 100; i = i + 1) begin
                if(i < 20) begin
                    // Clean rising edge
                    RxTransmit = 1;
                end else if(i < 80) begin
                    // Add noise based on noise level
                    if($random % (10 - noise_level) == 0)
                        RxTransmit = ~RxTransmit;
                end else begin
                    // Clean falling edge
                    RxTransmit = 0;
                end
                #CLK_PERIOD;
            end
            
            polarity = 0;
        end
    endtask
    
    // Task: Generate slow rising edge
    task generate_slow_edge;
        integer i;
        begin
            polarity = 1;
            
            // Slow rising edge over 20 clock cycles
            for(i = 0; i < 20; i = i + 1) begin
                if(i > 10)
                    RxTransmit = 1;
                else
                    RxTransmit = 0;
                #CLK_PERIOD;
            end
            
            #(CLK_PERIOD * 50);
            RxTransmit = 0;
            polarity = 0;
        end
    endtask
    
    // Task: Generate fast rising edge
    task generate_fast_edge;
        begin
            polarity = 1;
            
            // Very fast edge (1 clock cycle)
            RxTransmit = 1;
            #CLK_PERIOD;
            #(CLK_PERIOD * 50);
            RxTransmit = 0;
            
            polarity = 0;
        end
    endtask
    
    // Task: Generate multiple edges
    task generate_multiple_edges;
        integer i;
        begin
            polarity = 1;
            
            // Multiple rising edges
            for(i = 0; i < 5; i = i + 1) begin
                RxTransmit = 1;
                #(CLK_PERIOD * 5);
                RxTransmit = 0;
                #(CLK_PERIOD * 5);
            end
            
            polarity = 0;
        end
    endtask
    
    // Monitor important signals
    always @(posedge clk) begin
        test_counter <= test_counter + 1;
        
        // Log important events
        if(transmit) begin
            $display("Time %t: Data transmitted: 0x%08X, Temp: %d.%d°C, Quality: %b", 
                    $time, dout, (temp_data/10), (temp_data%10), signal_quality_ok);
        end
    end

endmodule

eagle_20
13 3248 33 1402 3060 53982 0 0
0.582 0.034 IFOG501_2B eagle_20 EG4X20BG256 Detail NA 17 8
clock: clk_in
15 0 0 0

clock: CLK120/pll_inst.clkc[0]
23 40218 2274 5
Setup check
33 3
Endpoint: signal_process/demodu/reg2_syn_207
33 0.582000 106 3
Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
35 0.582000 10.429000 9.847000 2 15
signal_process/demodu/fifo/ram_inst/dob_tmp[1] signal_process/demodu/sub0_syn_284.a[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[52] signal_process/demodu/reg2_syn_207.mi[1]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
97 0.693000 10.429000 9.736000 2 15
signal_process/demodu/fifo/ram_inst/dob_tmp[2] signal_process/demodu/sub0_syn_284.b[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[52] signal_process/demodu/reg2_syn_207.mi[1]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
159 0.702000 10.429000 9.727000 2 13
signal_process/demodu/fifo/ram_inst/dob_tmp[9] signal_process/demodu/sub0_syn_286.a[1]
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[52] signal_process/demodu/reg2_syn_207.mi[1]


Endpoint: signal_process/demodu/reg2_syn_213
217 0.697000 98 3
Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_213
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_213
219 0.697000 10.429000 9.732000 2 14
signal_process/demodu/fifo/ram_inst/dob_tmp[1] signal_process/demodu/sub0_syn_284.a[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/INS_dout_b2[48] signal_process/demodu/reg2_syn_213.mi[1]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_213
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_213
279 0.808000 10.429000 9.621000 2 14
signal_process/demodu/fifo/ram_inst/dob_tmp[2] signal_process/demodu/sub0_syn_284.b[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/INS_dout_b2[48] signal_process/demodu/reg2_syn_213.mi[1]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_213
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_213
339 0.817000 10.429000 9.612000 2 12
signal_process/demodu/fifo/ram_inst/dob_tmp[9] signal_process/demodu/sub0_syn_286.a[1]
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/INS_dout_b2[48] signal_process/demodu/reg2_syn_213.mi[1]


Endpoint: signal_process/demodu/reg2_syn_207
395 0.793000 110 3
Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
397 0.793000 10.429000 9.636000 2 15
signal_process/demodu/fifo/ram_inst/dob_tmp[1] signal_process/demodu/sub0_syn_284.a[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[54] signal_process/demodu/reg2_syn_207.mi[0]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
459 0.904000 10.429000 9.525000 2 15
signal_process/demodu/fifo/ram_inst/dob_tmp[2] signal_process/demodu/sub0_syn_284.b[1]
signal_process/demodu/sub0_syn_230 signal_process/demodu/sub0_syn_285.fci
signal_process/demodu/sub0_syn_234 signal_process/demodu/sub0_syn_286.fci
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[54] signal_process/demodu/reg2_syn_207.mi[0]

Timing path: signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb->signal_process/demodu/reg2_syn_207
signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb
signal_process/demodu/reg2_syn_207
521 0.913000 10.429000 9.516000 2 13
signal_process/demodu/fifo/ram_inst/dob_tmp[9] signal_process/demodu/sub0_syn_286.a[1]
signal_process/demodu/sub0_syn_238 signal_process/demodu/sub0_syn_287.fci
signal_process/demodu/sub0_syn_242 signal_process/demodu/sub0_syn_288.fci
signal_process/demodu/sub0_syn_246 signal_process/demodu/sub0_syn_289.fci
signal_process/demodu/sub0_syn_250 signal_process/demodu/sub0_syn_290.fci
signal_process/demodu/sub0_syn_254 signal_process/demodu/sub0_syn_291.fci
signal_process/demodu/sub0_syn_258 signal_process/demodu/sub0_syn_292.fci
signal_process/demodu/sub0_syn_262 signal_process/demodu/sub0_syn_293.fci
signal_process/demodu/sub0_syn_266 signal_process/demodu/sub0_syn_294.fci
signal_process/demodu/sub0_syn_270 signal_process/demodu/sub0_syn_295.fci
signal_process/demodu/sub0_syn_274 signal_process/demodu/sub0_syn_296.fci
signal_process/demodu/sub0_syn_278 signal_process/demodu/sub0_syn_297.fci
signal_process/demodu/INS_dout_b2[54] signal_process/demodu/reg2_syn_207.mi[0]



Hold check
579 3
Endpoint: signal_process/rs422/reg12_syn_47
581 0.260000 1 1
Timing path: signal_process/rs422/reg12_syn_43.clk->signal_process/rs422/reg12_syn_47
signal_process/rs422/reg12_syn_43.clk
signal_process/rs422/reg12_syn_47
583 0.260000 2.107000 2.367000 0 1
signal_process/rs422/output_filter[0] signal_process/rs422/reg12_syn_47.mi[0]


Endpoint: signal_process/demodu/reg9_syn_208
617 0.266000 1 1
Timing path: signal_process/demodu/reg11_syn_210.clk->signal_process/demodu/reg9_syn_208
signal_process/demodu/reg11_syn_210.clk
signal_process/demodu/reg9_syn_208
619 0.266000 2.191000 2.457000 0 1
signal_process/demodu/sample_sum[52] signal_process/demodu/reg9_syn_208.mi[0]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_28
653 0.269000 4 3
Timing path: signal_process/demodu/fifo/reg0_syn_47.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_28
signal_process/demodu/fifo/reg0_syn_47.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_28
655 0.269000 2.183000 2.452000 1 1
signal_process/demodu/fifo/rd_addr[0] signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[4]

Timing path: signal_process/demodu/fifo/reg0_syn_47.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_28
signal_process/demodu/fifo/reg0_syn_47.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_28
689 0.366000 2.183000 2.549000 1 1
signal_process/demodu/fifo/rd_addr[1] signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[5]

Timing path: signal_process/demodu/fifo/reg0_syn_44.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_28
signal_process/demodu/fifo/reg0_syn_44.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_28
723 0.394000 2.183000 2.577000 1 1
signal_process/demodu/fifo/rd_addr[2] signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[6]



Recovery check
757 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_66
759 6.370000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_66
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_66
761 6.370000 10.295000 3.925000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/ramread0_syn_66.rstb


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_47
795 6.532000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_47
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_47
797 6.532000 10.295000 3.763000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.rstb


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_28
831 6.633000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_28
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_28
833 6.633000 10.295000 3.662000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.rstb



Removal check
867 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
869 0.362000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
871 0.362000 2.236000 2.598000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.sr


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
905 0.419000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
907 0.419000 2.236000 2.655000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.sr


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22
941 0.419000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22
943 0.419000 2.236000 2.655000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22.sr



Period check
977 1
Endpoint: signal_process/modu/mult0_syn_2.clk
981 4.919000 1 0



clock: CLK120/pll_inst.clkc[3]
982 5606 546 4
Setup check
992 3
Endpoint: signal_process/demodu/reg0_syn_13
992 6.686000 1 1
Timing path: signal_process/ctrl_signal/AD_valid_reg_syn_31.clk->signal_process/demodu/reg0_syn_13
signal_process/ctrl_signal/AD_valid_reg_syn_31.clk
signal_process/demodu/reg0_syn_13
994 6.686000 10.183000 3.497000 0 1
signal_process/ctrl_signal/AD_valid signal_process/demodu/reg0_syn_13.mi[0]


Endpoint: signal_process/demodu/reg11_syn_210
1028 11.496000 65 3
Timing path: signal_process/demodu/reg6_syn_56.clk->signal_process/demodu/reg11_syn_210
signal_process/demodu/reg6_syn_56.clk
signal_process/demodu/reg11_syn_210
1030 11.496000 18.696000 7.200000 2 15
signal_process/demodu/din_reg1[1] signal_process/demodu/add1_syn_327.d[1]
signal_process/demodu/add1_syn_273 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_210.mi[0]

Timing path: signal_process/demodu/reg6_syn_56.clk->signal_process/demodu/reg11_syn_210
signal_process/demodu/reg6_syn_56.clk
signal_process/demodu/reg11_syn_210
1092 11.501000 18.696000 7.195000 2 15
signal_process/demodu/din_reg1[2] signal_process/demodu/add1_syn_327.e[1]
signal_process/demodu/add1_syn_273 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_210.mi[0]

Timing path: signal_process/demodu/reg6_syn_47.clk->signal_process/demodu/reg11_syn_210
signal_process/demodu/reg6_syn_47.clk
signal_process/demodu/reg11_syn_210
1154 11.540000 18.696000 7.156000 2 14
signal_process/demodu/din_reg1[3] signal_process/demodu/add1_syn_328.d[0]
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_210.mi[0]


Endpoint: signal_process/demodu/reg11_syn_207
1214 11.779000 67 3
Timing path: signal_process/demodu/reg6_syn_56.clk->signal_process/demodu/reg11_syn_207
signal_process/demodu/reg6_syn_56.clk
signal_process/demodu/reg11_syn_207
1216 11.779000 18.696000 6.917000 2 15
signal_process/demodu/din_reg1[1] signal_process/demodu/add1_syn_327.d[1]
signal_process/demodu/add1_syn_273 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_207.mi[0]

Timing path: signal_process/demodu/reg6_syn_56.clk->signal_process/demodu/reg11_syn_207
signal_process/demodu/reg6_syn_56.clk
signal_process/demodu/reg11_syn_207
1278 11.784000 18.696000 6.912000 2 15
signal_process/demodu/din_reg1[2] signal_process/demodu/add1_syn_327.e[1]
signal_process/demodu/add1_syn_273 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_207.mi[0]

Timing path: signal_process/demodu/reg6_syn_47.clk->signal_process/demodu/reg11_syn_207
signal_process/demodu/reg6_syn_47.clk
signal_process/demodu/reg11_syn_207
1340 11.823000 18.696000 6.873000 2 14
signal_process/demodu/din_reg1[3] signal_process/demodu/add1_syn_328.d[0]
signal_process/demodu/add1_syn_277 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_281 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_285 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_289 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_293 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_297 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_301 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_305 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/add1_syn_309 signal_process/demodu/add1_syn_337.fci
signal_process/demodu/add1_syn_313 signal_process/demodu/add1_syn_338.fci
signal_process/demodu/add1_syn_317 signal_process/demodu/add1_syn_339.fci
signal_process/demodu/add1_syn_321 signal_process/demodu/add1_syn_340.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_207.mi[0]



Hold check
1400 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1402 0.034000 9 3
Timing path: signal_process/demodu/reg8_syn_190.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_190.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1404 0.034000 2.474000 2.508000 1 1
signal_process/demodu/latch_sample_sum[9] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0]

Timing path: signal_process/demodu/reg8_syn_187.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_187.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1438 0.075000 2.474000 2.549000 1 1
signal_process/demodu/latch_sample_sum[12] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3]

Timing path: signal_process/demodu/reg8_syn_178.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_178.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1472 0.101000 2.474000 2.575000 1 1
signal_process/demodu/latch_sample_sum[15] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1506 0.075000 9 3
Timing path: signal_process/demodu/reg8_syn_202.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_202.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1508 0.075000 2.474000 2.549000 1 1
signal_process/demodu/latch_sample_sum[2] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2]

Timing path: signal_process/demodu/reg8_syn_181.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_181.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1542 0.111000 2.474000 2.585000 1 1
signal_process/demodu/latch_sample_sum[7] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7]

Timing path: signal_process/demodu/reg8_syn_196.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_9
signal_process/demodu/reg8_syn_196.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_9
1576 0.117000 2.474000 2.591000 1 1
signal_process/demodu/latch_sample_sum[3] signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_47
1610 0.092000 9 3
Timing path: signal_process/demodu/reg8_syn_211.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_47
signal_process/demodu/reg8_syn_211.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_47
1612 0.092000 2.474000 2.566000 1 1
signal_process/demodu/latch_sample_sum[52] signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7]

Timing path: signal_process/demodu/reg8_syn_220.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_47
signal_process/demodu/reg8_syn_220.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_47
1646 0.490000 2.474000 2.964000 2 2
signal_process/demodu/latch_sample_sum[46] signal_process/demodu/latch_sample_sum[46]_syn_3.a[0]
signal_process/demodu/latch_sample_sum[46]_holdbuf signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1]

Timing path: signal_process/demodu/reg8_syn_211.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_47
signal_process/demodu/reg8_syn_211.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_47
1682 0.713000 2.474000 3.187000 2 2
signal_process/demodu/latch_sample_sum[51] signal_process/demodu/latch_sample_sum[51]_syn_3.a[0]
signal_process/demodu/latch_sample_sum[51]_holdbuf signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6]



Recovery check
1718 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24
1720 14.614000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24
1722 14.614000 18.512000 3.898000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
1756 14.769000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
1758 14.769000 18.512000 3.743000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.sr


Endpoint: signal_process/demodu/fifo/reg1_syn_42
1792 14.769000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/reg1_syn_42
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/reg1_syn_42
1794 14.769000 18.512000 3.743000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/reg1_syn_42.sr



Removal check
1828 3
Endpoint: signal_process/demodu/fifo/sub0_syn_36
1830 0.097000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/sub0_syn_36
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/sub0_syn_36
1832 0.097000 2.527000 2.624000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/sub0_syn_36.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24
1866 0.097000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24
1868 0.097000 2.527000 2.624000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15
1902 0.201000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15
1904 0.201000 2.527000 2.728000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr




clock: CLK120/pll_inst.clkc[4]
1940 0 0 0

clock: clk_us
1948 8126 208 2
Setup check
1958 3
Endpoint: dq_syn_2
1958 991.271000 174 3
Timing path: wendu/cur_state[0]_syn_3772.clk->dq_syn_2
wendu/cur_state[0]_syn_3772.clk
dq_syn_2
1960 991.271000 1003.183000 11.912000 5 13
wendu/cnt_us[3] wendu/lt2_syn_96.a[0]
wendu/lt2_syn_13 wendu/lt2_syn_99.fci
wendu/lt2_syn_17 wendu/lt2_syn_102.fci
wendu/lt2_syn_21 wendu/lt2_syn_105.fci
wendu/lt2_syn_25 wendu/lt2_syn_108.fci
wendu/lt2_syn_29 wendu/lt2_syn_111.fci
wendu/lt2_syn_33 wendu/lt2_syn_114.fci
wendu/lt2_syn_37 wendu/lt2_syn_117.fci
wendu/lt2_syn_41 wendu/lt2_syn_120.fci
wendu/dq_en_n9 wendu/cur_state[0]_syn_3764.b[1]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3818.a[1]
wendu/cur_state[0]_syn_3588 wendu/cur_state[0]_syn_3774.c[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/cur_state[0]_syn_3772.clk->dq_syn_2
wendu/cur_state[0]_syn_3772.clk
dq_syn_2
2022 991.271000 1003.183000 11.912000 5 13
wendu/cnt_us[3] wendu/lt2_syn_96.a[0]
wendu/lt2_syn_13 wendu/lt2_syn_99.fci
wendu/lt2_syn_17 wendu/lt2_syn_102.fci
wendu/lt2_syn_21 wendu/lt2_syn_105.fci
wendu/lt2_syn_25 wendu/lt2_syn_108.fci
wendu/lt2_syn_29 wendu/lt2_syn_111.fci
wendu/lt2_syn_33 wendu/lt2_syn_114.fci
wendu/lt2_syn_37 wendu/lt2_syn_117.fci
wendu/lt2_syn_41 wendu/lt2_syn_120.fci
wendu/dq_en_n9 wendu/cur_state[0]_syn_3764.b[0]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3818.a[1]
wendu/cur_state[0]_syn_3588 wendu/cur_state[0]_syn_3774.c[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/cur_state[0]_syn_3792.clk->dq_syn_2
wendu/cur_state[0]_syn_3792.clk
dq_syn_2
2084 991.342000 1003.183000 11.841000 5 14
wendu/cnt_us[1] wendu/lt2_syn_93.a[0]
wendu/lt2_syn_9 wendu/lt2_syn_96.fci
wendu/lt2_syn_13 wendu/lt2_syn_99.fci
wendu/lt2_syn_17 wendu/lt2_syn_102.fci
wendu/lt2_syn_21 wendu/lt2_syn_105.fci
wendu/lt2_syn_25 wendu/lt2_syn_108.fci
wendu/lt2_syn_29 wendu/lt2_syn_111.fci
wendu/lt2_syn_33 wendu/lt2_syn_114.fci
wendu/lt2_syn_37 wendu/lt2_syn_117.fci
wendu/lt2_syn_41 wendu/lt2_syn_120.fci
wendu/dq_en_n9 wendu/cur_state[0]_syn_3764.b[1]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3818.a[1]
wendu/cur_state[0]_syn_3588 wendu/cur_state[0]_syn_3774.c[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts


Endpoint: dq_syn_2
2148 992.945000 15 3
Timing path: wendu/reg3_syn_39.clk->dq_syn_2
wendu/reg3_syn_39.clk
dq_syn_2
2150 992.945000 1003.300000 10.355000 4 5
wendu/cur_state[4] wendu/next_state[0]_syn_52.b[1]
wendu/next_state[0]_syn_2 wendu/next_state[0]_syn_60.d[1]
wendu/next_state[0]_syn_4 wendu/next_state[0]_syn_62.d[1]
wendu/next_state[0]_syn_8 wendu/next_state[0]_syn_48.a[1]
wendu/bit_cnt_b_n7 dq_syn_2.ce

Timing path: wendu/reg3_syn_39.clk->dq_syn_2
wendu/reg3_syn_39.clk
dq_syn_2
2196 993.110000 1003.300000 10.190000 4 5
wendu/cur_state[1] wendu/next_state[0]_syn_52.d[1]
wendu/next_state[0]_syn_2 wendu/next_state[0]_syn_60.d[1]
wendu/next_state[0]_syn_4 wendu/next_state[0]_syn_62.d[1]
wendu/next_state[0]_syn_8 wendu/next_state[0]_syn_48.a[1]
wendu/bit_cnt_b_n7 dq_syn_2.ce

Timing path: wendu/reg3_syn_43.clk->dq_syn_2
wendu/reg3_syn_43.clk
dq_syn_2
2242 993.603000 1003.300000 9.697000 4 5
wendu/cur_state[5] wendu/next_state[0]_syn_52.c[1]
wendu/next_state[0]_syn_2 wendu/next_state[0]_syn_60.d[1]
wendu/next_state[0]_syn_4 wendu/next_state[0]_syn_62.d[1]
wendu/next_state[0]_syn_8 wendu/next_state[0]_syn_48.a[1]
wendu/bit_cnt_b_n7 dq_syn_2.ce


Endpoint: wendu/reg2_syn_139
2288 993.810000 74 3
Timing path: wendu/cur_state[0]_syn_3780.clk->wendu/reg2_syn_139
wendu/cur_state[0]_syn_3780.clk
wendu/reg2_syn_139
2290 993.810000 1003.327000 9.517000 7 7
wendu/cnt_us[16] wendu/cur_state[0]_syn_3814.a[1]
wendu/cur_state[0]_syn_3503 wendu/cur_state[0]_syn_3822.a[0]
wendu/cur_state[0]_syn_3507 wendu/cur_state[0]_syn_3772.d[1]
wendu/cur_state[0]_syn_3513 wendu/cur_state[0]_syn_3780.d[1]
wendu/bit_cnt_b1_n wendu/cur_state[0]_syn_3782.a[1]
wendu/cur_state[0]_syn_3581 wendu/cur_state[0]_syn_3766.d[1]
wendu/cur_state[0]_syn_3583 wendu/reg2_syn_139.d[1]

Timing path: wendu/cur_state[0]_syn_3770.clk->wendu/reg2_syn_139
wendu/cur_state[0]_syn_3770.clk
wendu/reg2_syn_139
2340 993.837000 1003.327000 9.490000 7 7
wendu/cnt_us[18] wendu/cur_state[0]_syn_3814.c[1]
wendu/cur_state[0]_syn_3503 wendu/cur_state[0]_syn_3822.a[0]
wendu/cur_state[0]_syn_3507 wendu/cur_state[0]_syn_3772.d[1]
wendu/cur_state[0]_syn_3513 wendu/cur_state[0]_syn_3780.d[1]
wendu/bit_cnt_b1_n wendu/cur_state[0]_syn_3782.a[1]
wendu/cur_state[0]_syn_3581 wendu/cur_state[0]_syn_3766.d[1]
wendu/cur_state[0]_syn_3583 wendu/reg2_syn_139.d[1]

Timing path: wendu/cur_state[0]_syn_3782.clk->wendu/reg2_syn_139
wendu/cur_state[0]_syn_3782.clk
wendu/reg2_syn_139
2390 994.018000 1003.327000 9.309000 7 7
wendu/cnt_us[19] wendu/cur_state[0]_syn_3814.d[1]
wendu/cur_state[0]_syn_3503 wendu/cur_state[0]_syn_3822.a[0]
wendu/cur_state[0]_syn_3507 wendu/cur_state[0]_syn_3772.d[1]
wendu/cur_state[0]_syn_3513 wendu/cur_state[0]_syn_3780.d[1]
wendu/bit_cnt_b1_n wendu/cur_state[0]_syn_3782.a[1]
wendu/cur_state[0]_syn_3581 wendu/cur_state[0]_syn_3766.d[1]
wendu/cur_state[0]_syn_3583 wendu/reg2_syn_139.d[1]



Hold check
2440 3
Endpoint: wendu/reg5_syn_115
2442 0.375000 49 3
Timing path: wendu/reg5_syn_115.clk->wendu/reg5_syn_115
wendu/reg5_syn_115.clk
wendu/reg5_syn_115
2444 0.375000 2.867000 3.242000 1 1
wendu/data_temp[4] wendu/reg5_syn_115.d[1]

Timing path: wendu/reg5_syn_115.clk->wendu/reg5_syn_115
wendu/reg5_syn_115.clk
wendu/reg5_syn_115
2482 0.619000 2.867000 3.486000 1 1
wendu/data_temp[3] wendu/reg5_syn_115.c[1]

Timing path: wendu/reg3_syn_43.clk->wendu/reg5_syn_115
wendu/reg3_syn_43.clk
wendu/reg5_syn_115
2520 1.256000 2.867000 4.123000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3800.c[1]
wendu/cur_state[0]_syn_3562 wendu/reg5_syn_115.b[1]


Endpoint: wendu/reg5_syn_112
2560 0.381000 49 3
Timing path: wendu/reg5_syn_112.clk->wendu/reg5_syn_112
wendu/reg5_syn_112.clk
wendu/reg5_syn_112
2562 0.381000 2.867000 3.248000 1 1
wendu/data_temp[6] wendu/reg5_syn_112.d[1]

Timing path: wendu/reg5_syn_112.clk->wendu/reg5_syn_112
wendu/reg5_syn_112.clk
wendu/reg5_syn_112
2600 0.628000 2.867000 3.495000 1 1
wendu/data_temp[5] wendu/reg5_syn_112.c[1]

Timing path: wendu/reg3_syn_43.clk->wendu/reg5_syn_112
wendu/reg3_syn_43.clk
wendu/reg5_syn_112
2638 1.105000 2.867000 3.972000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3809.c[0]
wendu/cur_state[0]_syn_3560 wendu/reg5_syn_112.a[1]


Endpoint: wendu/reg5_syn_103
2678 0.381000 49 3
Timing path: wendu/reg5_syn_103.clk->wendu/reg5_syn_103
wendu/reg5_syn_103.clk
wendu/reg5_syn_103
2680 0.381000 2.867000 3.248000 1 1
wendu/data_temp[13] wendu/reg5_syn_103.d[1]

Timing path: wendu/reg5_syn_103.clk->wendu/reg5_syn_103
wendu/reg5_syn_103.clk
wendu/reg5_syn_103
2718 0.814000 2.867000 3.681000 1 1
wendu/data_temp[12] wendu/reg5_syn_103.c[1]

Timing path: wendu/reg3_syn_43.clk->wendu/reg5_syn_103
wendu/reg3_syn_43.clk
wendu/reg5_syn_103
2756 1.105000 2.867000 3.972000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3809.c[0]
wendu/cur_state[0]_syn_3560 wendu/reg5_syn_103.a[1]




Set input delay: 14.5ns max, and 14.5ns min. 
2796 24 24 2
Setup check
2806 3
Endpoint: AD_DATA[0]_syn_4
2806 2.880000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2808 2.880000 18.604000 15.724000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2840 2.880000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2842 2.880000 18.604000 15.724000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2874 2.880000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2876 2.880000 18.604000 15.724000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad



Hold check
2908 3
Endpoint: AD_DATA[0]_syn_4
2910 12.926000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2912 12.926000 2.426000 15.352000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2944 12.926000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2946 12.926000 2.426000 15.352000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2978 12.926000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2980 12.926000 2.426000 15.352000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad




Path delay: 8.032ns max
3012 4 4 1
Max path
3022 3
Endpoint: signal_process/demodu/fifo/sub0_syn_36
3022 6.931000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_25.clk->signal_process/demodu/fifo/sub0_syn_36
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_25.clk
signal_process/demodu/fifo/sub0_syn_36
3024 6.931000 10.192000 3.261000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[3] signal_process/demodu/fifo/sub0_syn_36.mi[0]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
3057 7.004000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_23.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_23.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16
3059 7.004000 10.192000 3.188000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[2] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.mi[1]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17
3092 7.148000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_28.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_28.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17
3094 7.148000 10.192000 3.044000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17.mi[0]




Path delay: 8.032ns max
3129 4 4 1
Max path
3139 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
3139 6.930000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
3141 6.930000 10.326000 3.396000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[1] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[1]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
3174 7.080000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_26.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_26.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26
3176 7.080000 10.326000 3.246000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[3] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[0]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
3209 7.085000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23
3211 7.085000 10.326000 3.241000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.mi[1]





Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (120.0MHz)             7.751ns     129.000MHz        0.480ns       525        0.000ns
	  CLK120/pll_inst.clkc[3] (60.0MHz)             13.786ns      72.537MHz        0.326ns       103        0.000ns
	  clk_us (1000.0KHz)                             8.729ns     114.561MHz        0.326ns        44        0.000ns
	Minimum input arrival time before clock: 1.224ns
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              4     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              4     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]


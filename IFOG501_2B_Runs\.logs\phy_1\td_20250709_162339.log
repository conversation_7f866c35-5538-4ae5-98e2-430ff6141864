============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul  9 16:23:39 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1520 instances
RUN-0007 : 376 luts, 897 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2049 nets
RUN-1001 : 1503 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     120     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1518 instances, 376 luts, 897 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7315, tnet num: 2047, tinst num: 1518, tnode num: 10286, tedge num: 12452.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2047 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.256856s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 530703
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1518.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 466742, overlap = 20.25
PHY-3002 : Step(2): len = 436491, overlap = 9
PHY-3002 : Step(3): len = 421447, overlap = 15.75
PHY-3002 : Step(4): len = 410021, overlap = 11.25
PHY-3002 : Step(5): len = 397310, overlap = 9
PHY-3002 : Step(6): len = 383719, overlap = 13.5
PHY-3002 : Step(7): len = 368738, overlap = 13.5
PHY-3002 : Step(8): len = 360949, overlap = 9
PHY-3002 : Step(9): len = 311596, overlap = 15.75
PHY-3002 : Step(10): len = 292402, overlap = 13.5
PHY-3002 : Step(11): len = 278828, overlap = 11.25
PHY-3002 : Step(12): len = 275438, overlap = 11.25
PHY-3002 : Step(13): len = 270306, overlap = 11.25
PHY-3002 : Step(14): len = 254992, overlap = 15.75
PHY-3002 : Step(15): len = 250835, overlap = 13.5
PHY-3002 : Step(16): len = 246326, overlap = 15.75
PHY-3002 : Step(17): len = 229269, overlap = 20.25
PHY-3002 : Step(18): len = 225645, overlap = 20.25
PHY-3002 : Step(19): len = 222239, overlap = 20.25
PHY-3002 : Step(20): len = 216292, overlap = 20.25
PHY-3002 : Step(21): len = 212675, overlap = 20.25
PHY-3002 : Step(22): len = 209563, overlap = 20.25
PHY-3002 : Step(23): len = 197614, overlap = 20.25
PHY-3002 : Step(24): len = 193326, overlap = 20.25
PHY-3002 : Step(25): len = 190672, overlap = 20.25
PHY-3002 : Step(26): len = 186324, overlap = 20.25
PHY-3002 : Step(27): len = 180730, overlap = 20.25
PHY-3002 : Step(28): len = 178168, overlap = 20.25
PHY-3002 : Step(29): len = 173079, overlap = 20.25
PHY-3002 : Step(30): len = 169611, overlap = 20.25
PHY-3002 : Step(31): len = 165963, overlap = 20.25
PHY-3002 : Step(32): len = 156525, overlap = 20.25
PHY-3002 : Step(33): len = 151044, overlap = 20.25
PHY-3002 : Step(34): len = 149513, overlap = 20.25
PHY-3002 : Step(35): len = 137069, overlap = 20.25
PHY-3002 : Step(36): len = 129508, overlap = 20.25
PHY-3002 : Step(37): len = 127193, overlap = 20.25
PHY-3002 : Step(38): len = 124504, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.39317e-05
PHY-3002 : Step(39): len = 126270, overlap = 20.25
PHY-3002 : Step(40): len = 125877, overlap = 15.75
PHY-3002 : Step(41): len = 124189, overlap = 20.25
PHY-3002 : Step(42): len = 123690, overlap = 18
PHY-3002 : Step(43): len = 119806, overlap = 18
PHY-3002 : Step(44): len = 116289, overlap = 11.25
PHY-3002 : Step(45): len = 114214, overlap = 11.25
PHY-3002 : Step(46): len = 113217, overlap = 11.25
PHY-3002 : Step(47): len = 108522, overlap = 15.75
PHY-3002 : Step(48): len = 107313, overlap = 13.5
PHY-3002 : Step(49): len = 105399, overlap = 15.75
PHY-3002 : Step(50): len = 103977, overlap = 13.5
PHY-3002 : Step(51): len = 101719, overlap = 15.75
PHY-3002 : Step(52): len = 100984, overlap = 13.5
PHY-3002 : Step(53): len = 98908.3, overlap = 15.75
PHY-3002 : Step(54): len = 97072.2, overlap = 15.75
PHY-3002 : Step(55): len = 93702, overlap = 15.75
PHY-3002 : Step(56): len = 93055.2, overlap = 15.75
PHY-3002 : Step(57): len = 91955, overlap = 15.75
PHY-3002 : Step(58): len = 87889.2, overlap = 13.5
PHY-3002 : Step(59): len = 85616.4, overlap = 15.75
PHY-3002 : Step(60): len = 83646.4, overlap = 15.75
PHY-3002 : Step(61): len = 82834.7, overlap = 15.75
PHY-3002 : Step(62): len = 81678.1, overlap = 18
PHY-3002 : Step(63): len = 80559.6, overlap = 15.75
PHY-3002 : Step(64): len = 80225.8, overlap = 15.75
PHY-3002 : Step(65): len = 79307.9, overlap = 13.5
PHY-3002 : Step(66): len = 78308, overlap = 13.5
PHY-3002 : Step(67): len = 77688.2, overlap = 15.75
PHY-3002 : Step(68): len = 77669.5, overlap = 15.75
PHY-3002 : Step(69): len = 76803.3, overlap = 13.5
PHY-3002 : Step(70): len = 75775.1, overlap = 13.5
PHY-3002 : Step(71): len = 75580.5, overlap = 15.75
PHY-3002 : Step(72): len = 74416.7, overlap = 16.0625
PHY-3002 : Step(73): len = 72998.7, overlap = 16.0625
PHY-3002 : Step(74): len = 72200.3, overlap = 15.9375
PHY-3002 : Step(75): len = 70838.9, overlap = 16
PHY-3002 : Step(76): len = 70226.6, overlap = 16
PHY-3002 : Step(77): len = 68469.9, overlap = 18.25
PHY-3002 : Step(78): len = 67424.8, overlap = 16
PHY-3002 : Step(79): len = 67034.7, overlap = 16.125
PHY-3002 : Step(80): len = 66385.6, overlap = 16.1875
PHY-3002 : Step(81): len = 65721.7, overlap = 16.1875
PHY-3002 : Step(82): len = 65050.8, overlap = 13.75
PHY-3002 : Step(83): len = 64142.4, overlap = 13.75
PHY-3002 : Step(84): len = 63778.5, overlap = 13.9062
PHY-3002 : Step(85): len = 63054.3, overlap = 16.4062
PHY-3002 : Step(86): len = 62464.1, overlap = 12
PHY-3002 : Step(87): len = 61863.6, overlap = 16.5
PHY-3002 : Step(88): len = 61336.2, overlap = 14.25
PHY-3002 : Step(89): len = 60741, overlap = 14.25
PHY-3002 : Step(90): len = 60275.9, overlap = 14.25
PHY-3002 : Step(91): len = 59855.6, overlap = 12
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000167863
PHY-3002 : Step(92): len = 60391.7, overlap = 14.25
PHY-3002 : Step(93): len = 60471.3, overlap = 14.25
PHY-3002 : Step(94): len = 60313.3, overlap = 14.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000335727
PHY-3002 : Step(95): len = 60515, overlap = 14.25
PHY-3002 : Step(96): len = 60398.5, overlap = 14.25
PHY-3001 : Before Legalized: Len = 60398.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008947s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (174.6%)

PHY-3001 : After Legalized: Len = 64862.5, Over = 0.75
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2047 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061321s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(97): len = 64599.8, overlap = 15.4375
PHY-3002 : Step(98): len = 64608.6, overlap = 14.6875
PHY-3002 : Step(99): len = 64220.6, overlap = 14.9375
PHY-3002 : Step(100): len = 63955.1, overlap = 14.6562
PHY-3002 : Step(101): len = 64195.8, overlap = 14.6562
PHY-3002 : Step(102): len = 62909.4, overlap = 14.25
PHY-3002 : Step(103): len = 62291.2, overlap = 14.1562
PHY-3002 : Step(104): len = 60641.5, overlap = 9.46875
PHY-3002 : Step(105): len = 60033.2, overlap = 9.34375
PHY-3002 : Step(106): len = 59116.7, overlap = 8.875
PHY-3002 : Step(107): len = 58149, overlap = 8.1875
PHY-3002 : Step(108): len = 56981.8, overlap = 8.0625
PHY-3002 : Step(109): len = 56359.9, overlap = 10.0625
PHY-3002 : Step(110): len = 55583.4, overlap = 9.03125
PHY-3002 : Step(111): len = 54639, overlap = 14.125
PHY-3002 : Step(112): len = 54240.5, overlap = 14.3125
PHY-3002 : Step(113): len = 53740.9, overlap = 14.5938
PHY-3002 : Step(114): len = 53286.6, overlap = 14.4688
PHY-3002 : Step(115): len = 53171.8, overlap = 14.5938
PHY-3002 : Step(116): len = 52595.7, overlap = 14.375
PHY-3002 : Step(117): len = 52379.1, overlap = 11.4062
PHY-3002 : Step(118): len = 51987.2, overlap = 11.0938
PHY-3002 : Step(119): len = 51769.3, overlap = 11.3438
PHY-3002 : Step(120): len = 51785.3, overlap = 11.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0103088
PHY-3002 : Step(121): len = 51576.4, overlap = 11.1562
PHY-3002 : Step(122): len = 51584.2, overlap = 11.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2047 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059730s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.3379e-05
PHY-3002 : Step(123): len = 51966.1, overlap = 40.0938
PHY-3002 : Step(124): len = 53008.1, overlap = 34.75
PHY-3002 : Step(125): len = 52547.9, overlap = 37.3125
PHY-3002 : Step(126): len = 51687.8, overlap = 36.9062
PHY-3002 : Step(127): len = 51591.3, overlap = 37.2812
PHY-3002 : Step(128): len = 51753.6, overlap = 37.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000126758
PHY-3002 : Step(129): len = 51887.6, overlap = 36.3125
PHY-3002 : Step(130): len = 52990.4, overlap = 35.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000253516
PHY-3002 : Step(131): len = 53492.1, overlap = 37.5625
PHY-3002 : Step(132): len = 53874, overlap = 38.1562
PHY-3002 : Step(133): len = 55257.9, overlap = 34.8438
PHY-3002 : Step(134): len = 55746.3, overlap = 31.5625
PHY-3002 : Step(135): len = 55510.3, overlap = 30.0312
PHY-3002 : Step(136): len = 55114.4, overlap = 23.0938
PHY-3002 : Step(137): len = 54753.1, overlap = 22.5938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7315, tnet num: 2047, tinst num: 1518, tnode num: 10286, tedge num: 12452.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 77.22 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2049.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57928, over cnt = 209(0%), over = 907, worst = 24
PHY-1001 : End global iterations;  0.086108s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (199.6%)

PHY-1001 : Congestion index: top1 = 42.89, top5 = 24.82, top10 = 15.84, top15 = 11.34.
PHY-1001 : End incremental global routing;  0.135452s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (161.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2047 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.232649s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (134.3%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1494/2049.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57928, over cnt = 209(0%), over = 907, worst = 24
PHY-1002 : len = 62464, over cnt = 130(0%), over = 359, worst = 15
PHY-1002 : len = 66328, over cnt = 36(0%), over = 89, worst = 15
PHY-1002 : len = 66920, over cnt = 18(0%), over = 51, worst = 14
PHY-1002 : len = 67664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100051s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (109.3%)

PHY-1001 : Congestion index: top1 = 36.79, top5 = 24.01, top10 = 16.99, top15 = 12.69.
OPT-1001 : End congestion update;  0.153000s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2047 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061344s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.218071s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (107.5%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : End physical optimization;  0.703067s wall, 0.750000s user + 0.046875s system = 0.796875s CPU (113.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 179 SEQ to BLE.
SYN-4003 : Packing 718 remaining SEQ's ...
SYN-4005 : Packed 87 SEQ with LUT/SLICE
SYN-4006 : 127 single LUT's are left
SYN-4006 : 631 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1007/1318 primitive instances ...
PHY-3001 : End packing;  0.049829s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 779 instances
RUN-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1878 nets
RUN-1001 : 1336 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 777 instances, 728 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55000.6, Over = 52.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6176, tnet num: 1876, tinst num: 777, tnode num: 8342, tedge num: 10943.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.293355s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (95.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.46441e-05
PHY-3002 : Step(138): len = 54253.2, overlap = 52
PHY-3002 : Step(139): len = 53814.9, overlap = 50.5
PHY-3002 : Step(140): len = 53539.7, overlap = 49.75
PHY-3002 : Step(141): len = 53102.4, overlap = 50.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.92883e-05
PHY-3002 : Step(142): len = 53496.1, overlap = 50.5
PHY-3002 : Step(143): len = 54069.4, overlap = 50
PHY-3002 : Step(144): len = 54405.8, overlap = 49.25
PHY-3002 : Step(145): len = 54754.7, overlap = 49.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000138577
PHY-3002 : Step(146): len = 54724.1, overlap = 48.5
PHY-3002 : Step(147): len = 54690.9, overlap = 47
PHY-3001 : Before Legalized: Len = 54690.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.064713s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (265.6%)

PHY-3001 : After Legalized: Len = 70088.8, Over = 0
PHY-3001 : Trial Legalized: Len = 70088.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049542s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000562009
PHY-3002 : Step(148): len = 65278.2, overlap = 10
PHY-3002 : Step(149): len = 63655.5, overlap = 10
PHY-3002 : Step(150): len = 61820.4, overlap = 13.25
PHY-3002 : Step(151): len = 60584.9, overlap = 17.75
PHY-3002 : Step(152): len = 60008.8, overlap = 19.25
PHY-3002 : Step(153): len = 59688.3, overlap = 18
PHY-3002 : Step(154): len = 59268, overlap = 18.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00112402
PHY-3002 : Step(155): len = 59552.1, overlap = 18.25
PHY-3002 : Step(156): len = 59677.3, overlap = 18
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00224803
PHY-3002 : Step(157): len = 59688.3, overlap = 16.75
PHY-3002 : Step(158): len = 59745.3, overlap = 16.25
PHY-3001 : Before Legalized: Len = 59745.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006460s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64554.8, Over = 0
PHY-3001 : Legalized: Len = 64554.8, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006360s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (245.7%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 64562.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6176, tnet num: 1876, tinst num: 777, tnode num: 8342, tedge num: 10943.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/1878.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71216, over cnt = 155(0%), over = 231, worst = 4
PHY-1002 : len = 71960, over cnt = 83(0%), over = 119, worst = 4
PHY-1002 : len = 73160, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 73240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141841s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (132.2%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.44, top10 = 18.08, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.194286s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (120.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059990s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.284475s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1670/1878.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006356s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.44, top10 = 18.08, top15 = 14.06.
OPT-1001 : End congestion update;  0.054757s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064066s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.120966s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063405s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1670/1878.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006273s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.44, top10 = 18.08, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065329s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.880226s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (104.7%)

RUN-1003 : finish command "place" in  5.281897s wall, 6.937500s user + 3.203125s system = 10.140625s CPU (192.0%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 779 instances
RUN-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1878 nets
RUN-1001 : 1336 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6176, tnet num: 1876, tinst num: 777, tnode num: 8342, tedge num: 10943.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70632, over cnt = 148(0%), over = 219, worst = 4
PHY-1002 : len = 71360, over cnt = 88(0%), over = 122, worst = 3
PHY-1002 : len = 72832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141982s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (143.1%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 23.21, top10 = 17.90, top15 = 13.95.
PHY-1001 : End global routing;  0.201031s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (124.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 491, reserve = 463, peak = 491.
PHY-1001 : End build detailed router design. 3.365760s wall, 3.281250s user + 0.078125s system = 3.359375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32904, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.111834s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 523, reserve = 497, peak = 523.
PHY-1001 : End phase 1; 1.118143s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181560, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End initial routed; 1.789698s wall, 2.484375s user + 0.125000s system = 2.609375s CPU (145.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1663(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.965   |  -12.237  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.348666s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 2; 2.138453s wall, 2.828125s user + 0.125000s system = 2.953125s CPU (138.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181560, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015161s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181336, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029901s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023910s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (130.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1663(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.965   |  -12.237  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.356520s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.175931s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.7%)

PHY-1001 : Current memory(MB): used = 542, reserve = 513, peak = 542.
PHY-1001 : End phase 3; 0.725217s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (101.3%)

PHY-1003 : Routed, final wirelength = 181368
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.011220s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.3%)

PHY-1001 : End detail routing;  7.548324s wall, 8.140625s user + 0.218750s system = 8.359375s CPU (110.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6176, tnet num: 1876, tinst num: 777, tnode num: 8342, tedge num: 10943.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.485138s wall, 9.093750s user + 0.234375s system = 9.328125s CPU (109.9%)

RUN-1004 : used memory is 494 MB, reserved memory is 465 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      772   out of  19600    3.94%
#reg                      984   out of  19600    5.02%
#le                      1403
  #lut only               419   out of   1403   29.86%
  #reg only               631   out of   1403   44.98%
  #lut&reg                353   out of   1403   25.16%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         443
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1403   |576     |196     |1017    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1037   |275     |133     |854     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |18      |7       |19      |0       |0       |
|    demodu                  |Demodulation                                     |428    |85      |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |37      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |13      |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |136    |19      |15      |108     |0       |0       |
|    modu                    |Modulation                                       |105    |42      |14      |103     |0       |1       |
|    rs422                   |Rs422Output                                      |311    |85      |46      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |26      |6       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |132    |123     |7       |63      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |25      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |22      |0       |19      |0       |0       |
|    U2                      |Ctrl_Data                                        |76     |76      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |66      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1297  
    #2          2       288   
    #3          3       125   
    #4          4        19   
    #5        5-10       73   
    #6        11-50      31   
    #7       101-500     1    
  Average     2.00            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6176, tnet num: 1876, tinst num: 777, tnode num: 8342, tedge num: 10943.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1876 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 777
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1878, pip num: 14308
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1276 valid insts, and 37681 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.427687s wall, 18.078125s user + 0.062500s system = 18.140625s CPU (529.2%)

RUN-1004 : used memory is 533 MB, reserved memory is 504 MB, peak memory is 658 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250709_162339.log"

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul  9 16:08:12 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1499 instances
RUN-0007 : 381 luts, 878 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2018 nets
RUN-1001 : 1502 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1497 instances, 381 luts, 878 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7182, tnet num: 2016, tinst num: 1497, tnode num: 10095, tedge num: 12180.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.402030s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (97.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 528566
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1497.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 457940, overlap = 18
PHY-3002 : Step(2): len = 431420, overlap = 15.75
PHY-3002 : Step(3): len = 419413, overlap = 18
PHY-3002 : Step(4): len = 401089, overlap = 15.75
PHY-3002 : Step(5): len = 373465, overlap = 18
PHY-3002 : Step(6): len = 334354, overlap = 9
PHY-3002 : Step(7): len = 320735, overlap = 11.25
PHY-3002 : Step(8): len = 314246, overlap = 11.25
PHY-3002 : Step(9): len = 309086, overlap = 13.5
PHY-3002 : Step(10): len = 297757, overlap = 11.25
PHY-3002 : Step(11): len = 291086, overlap = 15.75
PHY-3002 : Step(12): len = 285862, overlap = 15.75
PHY-3002 : Step(13): len = 277441, overlap = 13.5
PHY-3002 : Step(14): len = 268311, overlap = 13.5
PHY-3002 : Step(15): len = 264879, overlap = 13.5
PHY-3002 : Step(16): len = 256515, overlap = 15.75
PHY-3002 : Step(17): len = 249141, overlap = 20.25
PHY-3002 : Step(18): len = 244746, overlap = 20.25
PHY-3002 : Step(19): len = 239242, overlap = 20.25
PHY-3002 : Step(20): len = 229188, overlap = 20.25
PHY-3002 : Step(21): len = 226151, overlap = 20.25
PHY-3002 : Step(22): len = 220870, overlap = 20.25
PHY-3002 : Step(23): len = 213647, overlap = 20.25
PHY-3002 : Step(24): len = 206328, overlap = 20.25
PHY-3002 : Step(25): len = 204266, overlap = 20.25
PHY-3002 : Step(26): len = 192764, overlap = 20.25
PHY-3002 : Step(27): len = 177298, overlap = 20.25
PHY-3002 : Step(28): len = 173503, overlap = 20.25
PHY-3002 : Step(29): len = 170593, overlap = 20.25
PHY-3002 : Step(30): len = 132775, overlap = 18
PHY-3002 : Step(31): len = 125844, overlap = 20.25
PHY-3002 : Step(32): len = 123870, overlap = 20.25
PHY-3002 : Step(33): len = 118109, overlap = 20.25
PHY-3002 : Step(34): len = 115701, overlap = 20.25
PHY-3002 : Step(35): len = 113044, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.42895e-05
PHY-3002 : Step(36): len = 114243, overlap = 20.25
PHY-3002 : Step(37): len = 113999, overlap = 15.75
PHY-3002 : Step(38): len = 111906, overlap = 20.25
PHY-3002 : Step(39): len = 111171, overlap = 20.25
PHY-3002 : Step(40): len = 107010, overlap = 15.75
PHY-3002 : Step(41): len = 103153, overlap = 13.5
PHY-3002 : Step(42): len = 99963.6, overlap = 13.5
PHY-3002 : Step(43): len = 98924.5, overlap = 11.25
PHY-3002 : Step(44): len = 95712, overlap = 15.75
PHY-3002 : Step(45): len = 95701.3, overlap = 15.75
PHY-3002 : Step(46): len = 92977.6, overlap = 15.75
PHY-3002 : Step(47): len = 89563.1, overlap = 18
PHY-3002 : Step(48): len = 87846.5, overlap = 13.5
PHY-3002 : Step(49): len = 87385.9, overlap = 15.75
PHY-3002 : Step(50): len = 84189.1, overlap = 15.75
PHY-3002 : Step(51): len = 82241.8, overlap = 18
PHY-3002 : Step(52): len = 80106.5, overlap = 18
PHY-3002 : Step(53): len = 79705.1, overlap = 15.75
PHY-3002 : Step(54): len = 75826.1, overlap = 13.5
PHY-3002 : Step(55): len = 74683.9, overlap = 13.5
PHY-3002 : Step(56): len = 73401.5, overlap = 19.125
PHY-3002 : Step(57): len = 71917.6, overlap = 16.625
PHY-3002 : Step(58): len = 71596, overlap = 16.625
PHY-3002 : Step(59): len = 70569.8, overlap = 16.875
PHY-3002 : Step(60): len = 68821.2, overlap = 14.625
PHY-3002 : Step(61): len = 66411.5, overlap = 16.875
PHY-3002 : Step(62): len = 65334.2, overlap = 16.625
PHY-3002 : Step(63): len = 64233, overlap = 16.5
PHY-3002 : Step(64): len = 64122.5, overlap = 16.375
PHY-3002 : Step(65): len = 63696.3, overlap = 16.3125
PHY-3002 : Step(66): len = 62253.8, overlap = 14.0625
PHY-3002 : Step(67): len = 61452.5, overlap = 13.9375
PHY-3002 : Step(68): len = 60439.9, overlap = 13.9375
PHY-3002 : Step(69): len = 59899.3, overlap = 16.125
PHY-3002 : Step(70): len = 59200.1, overlap = 16.0625
PHY-3002 : Step(71): len = 58644.2, overlap = 16.0625
PHY-3002 : Step(72): len = 58425.3, overlap = 16
PHY-3002 : Step(73): len = 58504.3, overlap = 16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000168579
PHY-3002 : Step(74): len = 58841.8, overlap = 16
PHY-3002 : Step(75): len = 58950.7, overlap = 16.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000337158
PHY-3002 : Step(76): len = 59268.9, overlap = 16.0625
PHY-3002 : Step(77): len = 59357, overlap = 16.0625
PHY-3001 : Before Legalized: Len = 59357
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009502s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63034.9, Over = 0.3125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.093933s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 62933.5, overlap = 9
PHY-3002 : Step(79): len = 63006.5, overlap = 9.03125
PHY-3002 : Step(80): len = 62513.6, overlap = 8.21875
PHY-3002 : Step(81): len = 62329.5, overlap = 7.59375
PHY-3002 : Step(82): len = 62522.6, overlap = 7.6875
PHY-3002 : Step(83): len = 61018.1, overlap = 9.1875
PHY-3002 : Step(84): len = 60434.9, overlap = 10.375
PHY-3002 : Step(85): len = 59364.5, overlap = 11.8438
PHY-3002 : Step(86): len = 58515.3, overlap = 11.875
PHY-3002 : Step(87): len = 57929.6, overlap = 10.75
PHY-3002 : Step(88): len = 57097.6, overlap = 11.8438
PHY-3002 : Step(89): len = 56507.1, overlap = 11.9375
PHY-3002 : Step(90): len = 55294.7, overlap = 13.2188
PHY-3002 : Step(91): len = 54321.3, overlap = 13.125
PHY-3002 : Step(92): len = 53780.8, overlap = 13.2812
PHY-3002 : Step(93): len = 53567.7, overlap = 14.0312
PHY-3002 : Step(94): len = 53436.1, overlap = 17.9375
PHY-3002 : Step(95): len = 52649.9, overlap = 18.4062
PHY-3002 : Step(96): len = 52406.8, overlap = 18.5
PHY-3002 : Step(97): len = 51958.5, overlap = 19.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00753924
PHY-3002 : Step(98): len = 51637.3, overlap = 19.875
PHY-3002 : Step(99): len = 51757.6, overlap = 19.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.146257s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000113668
PHY-3002 : Step(100): len = 52054.9, overlap = 41.625
PHY-3002 : Step(101): len = 52843.2, overlap = 44.0938
PHY-3002 : Step(102): len = 53688, overlap = 42.5
PHY-3002 : Step(103): len = 53772, overlap = 43.0625
PHY-3002 : Step(104): len = 53677.2, overlap = 42.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000227337
PHY-3002 : Step(105): len = 53782.1, overlap = 42.1562
PHY-3002 : Step(106): len = 54014.2, overlap = 39.0625
PHY-3002 : Step(107): len = 54101.9, overlap = 38.375
PHY-3002 : Step(108): len = 53910.4, overlap = 37.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000454673
PHY-3002 : Step(109): len = 53991, overlap = 37.2812
PHY-3002 : Step(110): len = 54574.6, overlap = 37.1875
PHY-3002 : Step(111): len = 54981.6, overlap = 36
PHY-3002 : Step(112): len = 55528, overlap = 35.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7182, tnet num: 2016, tinst num: 1497, tnode num: 10095, tedge num: 12180.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.78 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2018.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59032, over cnt = 241(0%), over = 1084, worst = 18
PHY-1001 : End global iterations;  0.128468s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (182.4%)

PHY-1001 : Congestion index: top1 = 45.00, top5 = 26.80, top10 = 16.70, top15 = 11.80.
PHY-1001 : End incremental global routing;  0.217359s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (143.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.123795s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (101.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.392122s wall, 0.421875s user + 0.062500s system = 0.484375s CPU (123.5%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1542/2018.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59032, over cnt = 241(0%), over = 1084, worst = 18
PHY-1002 : len = 65840, over cnt = 180(0%), over = 484, worst = 18
PHY-1002 : len = 70728, over cnt = 31(0%), over = 34, worst = 3
PHY-1002 : len = 71232, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.172061s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (118.1%)

PHY-1001 : Congestion index: top1 = 37.44, top5 = 25.57, top10 = 18.36, top15 = 13.52.
OPT-1001 : End congestion update;  0.239142s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (111.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.086752s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.330631s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  1.299767s wall, 1.453125s user + 0.078125s system = 1.531250s CPU (117.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 381 LUT to BLE ...
SYN-4008 : Packed 381 LUT and 180 SEQ to BLE.
SYN-4003 : Packing 698 remaining SEQ's ...
SYN-4005 : Packed 89 SEQ with LUT/SLICE
SYN-4006 : 128 single LUT's are left
SYN-4006 : 609 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 990/1294 primitive instances ...
PHY-3001 : End packing;  0.090739s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (86.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 757 instances
RUN-1001 : 353 mslices, 353 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1846 nets
RUN-1001 : 1328 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 755 instances, 706 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55680.6, Over = 55
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6044, tnet num: 1844, tinst num: 755, tnode num: 8155, tedge num: 10668.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.508919s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.90216e-05
PHY-3002 : Step(113): len = 54578.6, overlap = 57.75
PHY-3002 : Step(114): len = 54053.4, overlap = 56.75
PHY-3002 : Step(115): len = 53491.4, overlap = 55.75
PHY-3002 : Step(116): len = 53423.7, overlap = 54.5
PHY-3002 : Step(117): len = 53325.3, overlap = 49.5
PHY-3002 : Step(118): len = 53295, overlap = 51.25
PHY-3002 : Step(119): len = 53212.9, overlap = 52
PHY-3002 : Step(120): len = 53422.3, overlap = 50.75
PHY-3002 : Step(121): len = 53463.2, overlap = 50.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118043
PHY-3002 : Step(122): len = 53701.9, overlap = 49.75
PHY-3002 : Step(123): len = 54216.2, overlap = 47.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000233074
PHY-3002 : Step(124): len = 55396.1, overlap = 43.5
PHY-3002 : Step(125): len = 55701.7, overlap = 39
PHY-3002 : Step(126): len = 55895.4, overlap = 40
PHY-3001 : Before Legalized: Len = 55895.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.142911s wall, 0.109375s user + 0.234375s system = 0.343750s CPU (240.5%)

PHY-3001 : After Legalized: Len = 67387, Over = 0
PHY-3001 : Trial Legalized: Len = 67387
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.100069s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00172257
PHY-3002 : Step(127): len = 64181.8, overlap = 3.5
PHY-3002 : Step(128): len = 62086, overlap = 10
PHY-3002 : Step(129): len = 60397.8, overlap = 11.25
PHY-3002 : Step(130): len = 59529.2, overlap = 12.5
PHY-3002 : Step(131): len = 58917.6, overlap = 13.5
PHY-3002 : Step(132): len = 58385.9, overlap = 13.75
PHY-3002 : Step(133): len = 57962.3, overlap = 16
PHY-3002 : Step(134): len = 57556.5, overlap = 16.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00344515
PHY-3002 : Step(135): len = 57737.8, overlap = 16.25
PHY-3002 : Step(136): len = 57802.1, overlap = 16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00689029
PHY-3002 : Step(137): len = 57811.4, overlap = 15.5
PHY-3002 : Step(138): len = 57818.2, overlap = 15.25
PHY-3001 : Before Legalized: Len = 57818.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012085s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 61564.3, Over = 0
PHY-3001 : Legalized: Len = 61564.3, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.011104s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 2, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 61618.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6044, tnet num: 1844, tinst num: 755, tnode num: 8155, tedge num: 10668.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 79/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68352, over cnt = 149(0%), over = 233, worst = 6
PHY-1002 : len = 69304, over cnt = 108(0%), over = 129, worst = 4
PHY-1002 : len = 70600, over cnt = 20(0%), over = 26, worst = 3
PHY-1002 : len = 70792, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.204595s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (160.4%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 22.70, top10 = 17.61, top15 = 13.50.
PHY-1001 : End incremental global routing;  0.284494s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (148.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085095s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.421613s wall, 0.515625s user + 0.031250s system = 0.546875s CPU (129.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1616/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70792, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.013884s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 22.70, top10 = 17.61, top15 = 13.50.
OPT-1001 : End congestion update;  0.086976s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077886s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 715 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 755 instances, 706 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 61621.6, Over = 0
PHY-3001 : End spreading;  0.008830s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 61621.6, Over = 0
PHY-3001 : End incremental legalization;  0.051738s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (241.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.238621s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (131.0%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.078863s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1612/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.013673s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (114.3%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.72, top10 = 17.60, top15 = 13.49.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080975s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.390691s wall, 1.468750s user + 0.093750s system = 1.562500s CPU (112.4%)

RUN-1003 : finish command "place" in  9.186223s wall, 11.265625s user + 5.203125s system = 16.468750s CPU (179.3%)

RUN-1004 : used memory is 193 MB, reserved memory is 160 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 757 instances
RUN-1001 : 353 mslices, 353 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1846 nets
RUN-1001 : 1328 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6044, tnet num: 1844, tinst num: 755, tnode num: 8155, tedge num: 10668.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 353 mslices, 353 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67400, over cnt = 150(0%), over = 235, worst = 6
PHY-1002 : len = 68344, over cnt = 108(0%), over = 131, worst = 4
PHY-1002 : len = 69776, over cnt = 8(0%), over = 10, worst = 2
PHY-1002 : len = 69944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.285108s wall, 0.359375s user + 0.078125s system = 0.437500s CPU (153.5%)

PHY-1001 : Congestion index: top1 = 30.88, top5 = 22.63, top10 = 17.39, top15 = 13.31.
PHY-1001 : End global routing;  0.406521s wall, 0.468750s user + 0.078125s system = 0.546875s CPU (134.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 200, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 466, peak = 495.
PHY-1001 : End build detailed router design. 5.869808s wall, 5.750000s user + 0.140625s system = 5.890625s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30848, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.765515s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (94.7%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 1; 1.776775s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (95.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176848, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 3.177582s wall, 4.000000s user + 0.281250s system = 4.281250s CPU (134.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1638(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.273   |  -13.551  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.616245s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (96.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 504, peak = 532.
PHY-1001 : End phase 2; 3.794051s wall, 4.578125s user + 0.296875s system = 4.875000s CPU (128.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176848, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.028645s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176640, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.040963s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (114.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176632, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.037780s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1638(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.273   |  -13.551  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.875602s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (98.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 12 nets
PHY-1001 : End commit to database; 0.503624s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (96.2%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 1.759194s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (98.6%)

PHY-1003 : Routed, final wirelength = 176632
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.015412s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.4%)

PHY-1001 : End detail routing;  13.732974s wall, 14.265625s user + 0.453125s system = 14.718750s CPU (107.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6044, tnet num: 1844, tinst num: 755, tnode num: 8155, tedge num: 10668.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[6]_syn_20 endpin u_uart/U2/reg0_syn_51.a[1] slack -961ps
RUN-1001 : 2 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[6]_syn_8 endpin u_uart/U2/reg0_syn_51.b[1] slack -1382ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[6]_syn_12 endpin u_uart/U2/reg0_syn_51.d[1] slack -2273ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[0]_syn_10 endpin u_uart/U2/tx_data_dy_b[0]_syn_49.a[0] slack -1209ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[0]_syn_16 endpin u_uart/U2/tx_data_dy_b[0]_syn_49.b[0] slack -843ps
RUN-1001 : 2 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[1]_syn_16 endpin u_uart/U2/tx_data_dy_b[1]_syn_38.a[0] slack -1185ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[1]_syn_4 endpin u_uart/U2/tx_data_dy_b[1]_syn_38.b[0] slack -1142ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[1]_syn_8 endpin u_uart/U2/tx_data_dy_b[1]_syn_38.d[0] slack -1819ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[2]_syn_16 endpin u_uart/U2/tx_data_dy_b[2]_syn_48.a[0] slack -1449ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[2]_syn_6 endpin u_uart/U2/tx_data_dy_b[2]_syn_48.b[0] slack -979ps
RUN-1001 : 2 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[5]_syn_18 endpin u_uart/U2/tx_data_dy_b[3]_syn_48.a[0] slack -1254ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[5]_syn_8 endpin u_uart/U2/tx_data_dy_b[3]_syn_48.b[0] slack -1259ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[4]_syn_16 endpin u_uart/U2/tx_data_dy_b[4]_syn_42.a[0] slack -1176ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[4]_syn_4 endpin u_uart/U2/tx_data_dy_b[4]_syn_42.b[0] slack -1082ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[4]_syn_8 endpin u_uart/U2/tx_data_dy_b[4]_syn_42.d[0] slack -2002ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[3]_syn_16 endpin u_uart/U2/tx_data_dy_b[5]_syn_46.a[0] slack -1195ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[3]_syn_4 endpin u_uart/U2/tx_data_dy_b[5]_syn_46.b[0] slack -1329ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[3]_syn_8 endpin u_uart/U2/tx_data_dy_b[5]_syn_46.d[0] slack -1891ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[7]_syn_20 endpin u_uart/U2/tx_data_dy_b[7]_syn_42.a[0] slack -1501ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net u_uart/U2/tx_data_dy_b[7]_syn_10 endpin u_uart/U2/tx_data_dy_b[7]_syn_42.b[0] slack -1515ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6174, tnet num: 1909, tinst num: 820, tnode num: 8285, tedge num: 10798.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  5.994956s wall, 6.171875s user + 0.343750s system = 6.515625s CPU (108.7%)

RUN-1003 : finish command "route" in  20.851321s wall, 21.609375s user + 0.890625s system = 22.500000s CPU (107.9%)

RUN-1004 : used memory is 524 MB, reserved memory is 496 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      893   out of  19600    4.56%
#reg                      954   out of  19600    4.87%
#le                      1502
  #lut only               548   out of   1502   36.48%
  #reg only               609   out of   1502   40.55%
  #lut&reg                345   out of   1502   22.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       425
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       102
#3        wendu/clk_us                    GCLK               mslice             signal_process/trans/clk_out_n_syn_57.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1502   |704     |189     |987     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1028   |293     |128     |828     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |23      |7       |23      |0       |0       |
|    demodu                  |Demodulation                                     |441    |102     |45      |349     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |37      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |12      |0       |12      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |8       |0       |17      |0       |0       |
|    integ                   |Integration                                      |139    |21      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |70     |34      |7       |68      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |85      |46      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |249    |240     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |21      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |193    |193     |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1354  
    #2          2       272   
    #3          3       120   
    #4          4        19   
    #5        5-10       72   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6174, tnet num: 1909, tinst num: 820, tnode num: 8285, tedge num: 10798.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1909 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 820
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1911, pip num: 14249
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 15
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1292 valid insts, and 38333 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.972188s wall, 24.375000s user + 0.125000s system = 24.500000s CPU (492.7%)

RUN-1004 : used memory is 543 MB, reserved memory is 513 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250709_160812.log"

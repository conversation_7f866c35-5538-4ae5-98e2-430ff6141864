============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 23 18:45:47 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1562 instances
RUN-0007 : 375 luts, 946 seqs, 120 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2080 nets
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1560 instances, 375 luts, 946 seqs, 190 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7416, tnet num: 2078, tinst num: 1560, tnode num: 10516, tedge num: 12520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.248426s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 531080
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1560.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 466844, overlap = 18
PHY-3002 : Step(2): len = 439767, overlap = 11.25
PHY-3002 : Step(3): len = 426108, overlap = 20.25
PHY-3002 : Step(4): len = 413412, overlap = 13.5
PHY-3002 : Step(5): len = 401442, overlap = 20.25
PHY-3002 : Step(6): len = 382423, overlap = 13.5
PHY-3002 : Step(7): len = 373463, overlap = 15.75
PHY-3002 : Step(8): len = 366716, overlap = 15.75
PHY-3002 : Step(9): len = 351125, overlap = 15.75
PHY-3002 : Step(10): len = 342070, overlap = 15.75
PHY-3002 : Step(11): len = 336980, overlap = 13.5
PHY-3002 : Step(12): len = 324214, overlap = 13.5
PHY-3002 : Step(13): len = 317210, overlap = 13.5
PHY-3002 : Step(14): len = 312217, overlap = 13.5
PHY-3002 : Step(15): len = 301629, overlap = 15.75
PHY-3002 : Step(16): len = 294918, overlap = 15.75
PHY-3002 : Step(17): len = 290202, overlap = 15.75
PHY-3002 : Step(18): len = 281714, overlap = 15.75
PHY-3002 : Step(19): len = 276087, overlap = 15.75
PHY-3002 : Step(20): len = 272260, overlap = 15.75
PHY-3002 : Step(21): len = 262548, overlap = 13.5
PHY-3002 : Step(22): len = 255181, overlap = 13.5
PHY-3002 : Step(23): len = 252194, overlap = 13.5
PHY-3002 : Step(24): len = 243962, overlap = 13.5
PHY-3002 : Step(25): len = 225782, overlap = 20.25
PHY-3002 : Step(26): len = 221622, overlap = 20.25
PHY-3002 : Step(27): len = 218824, overlap = 20.25
PHY-3002 : Step(28): len = 174944, overlap = 18
PHY-3002 : Step(29): len = 167828, overlap = 20.25
PHY-3002 : Step(30): len = 165466, overlap = 20.25
PHY-3002 : Step(31): len = 162197, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011095
PHY-3002 : Step(32): len = 162580, overlap = 15.75
PHY-3002 : Step(33): len = 161328, overlap = 15.75
PHY-3002 : Step(34): len = 160241, overlap = 15.75
PHY-3002 : Step(35): len = 158718, overlap = 15.75
PHY-3002 : Step(36): len = 155382, overlap = 9
PHY-3002 : Step(37): len = 153160, overlap = 11.25
PHY-3002 : Step(38): len = 144248, overlap = 11.25
PHY-3002 : Step(39): len = 138471, overlap = 13.5
PHY-3002 : Step(40): len = 137661, overlap = 13.5
PHY-3002 : Step(41): len = 136099, overlap = 11.25
PHY-3002 : Step(42): len = 133643, overlap = 6.75
PHY-3002 : Step(43): len = 131561, overlap = 9
PHY-3002 : Step(44): len = 128268, overlap = 9
PHY-3002 : Step(45): len = 126004, overlap = 13.5
PHY-3002 : Step(46): len = 124690, overlap = 13.5
PHY-3002 : Step(47): len = 120494, overlap = 9
PHY-3002 : Step(48): len = 116635, overlap = 9
PHY-3002 : Step(49): len = 115015, overlap = 11.25
PHY-3002 : Step(50): len = 113866, overlap = 13.5
PHY-3002 : Step(51): len = 109902, overlap = 9
PHY-3002 : Step(52): len = 107735, overlap = 13.5
PHY-3002 : Step(53): len = 105997, overlap = 11.25
PHY-3002 : Step(54): len = 104267, overlap = 13.5
PHY-3002 : Step(55): len = 101671, overlap = 11.25
PHY-3002 : Step(56): len = 97695.4, overlap = 9
PHY-3002 : Step(57): len = 95341.6, overlap = 13.5
PHY-3002 : Step(58): len = 94011.2, overlap = 15.75
PHY-3002 : Step(59): len = 92498.3, overlap = 15.75
PHY-3002 : Step(60): len = 91620.2, overlap = 13.5
PHY-3002 : Step(61): len = 88363.5, overlap = 9
PHY-3002 : Step(62): len = 85334.4, overlap = 9.0625
PHY-3002 : Step(63): len = 83743.7, overlap = 9.25
PHY-3002 : Step(64): len = 82822.8, overlap = 8.25
PHY-3002 : Step(65): len = 78599.7, overlap = 9.125
PHY-3002 : Step(66): len = 77757.7, overlap = 9.375
PHY-3002 : Step(67): len = 76911.3, overlap = 18.5625
PHY-3002 : Step(68): len = 76958.5, overlap = 14.1875
PHY-3002 : Step(69): len = 76811, overlap = 9.6875
PHY-3002 : Step(70): len = 76424, overlap = 9.6875
PHY-3002 : Step(71): len = 75694.8, overlap = 12.125
PHY-3002 : Step(72): len = 75331, overlap = 12.3125
PHY-3002 : Step(73): len = 74658.2, overlap = 12.5
PHY-3002 : Step(74): len = 73456.5, overlap = 10.1875
PHY-3002 : Step(75): len = 71476.4, overlap = 8
PHY-3002 : Step(76): len = 70064.5, overlap = 8.5625
PHY-3002 : Step(77): len = 68705.4, overlap = 12.75
PHY-3002 : Step(78): len = 68487.8, overlap = 12.9375
PHY-3002 : Step(79): len = 67226.8, overlap = 10.6875
PHY-3002 : Step(80): len = 66038.1, overlap = 10.5
PHY-3002 : Step(81): len = 65643.1, overlap = 10.3125
PHY-3002 : Step(82): len = 63976.6, overlap = 14.5625
PHY-3002 : Step(83): len = 63107.7, overlap = 14.625
PHY-3002 : Step(84): len = 62850.2, overlap = 7.6875
PHY-3002 : Step(85): len = 62352.4, overlap = 7.5
PHY-3002 : Step(86): len = 61359.7, overlap = 7.5
PHY-3002 : Step(87): len = 60791.7, overlap = 7.25
PHY-3002 : Step(88): len = 60271.3, overlap = 7.25
PHY-3002 : Step(89): len = 59438, overlap = 9.4375
PHY-3002 : Step(90): len = 59215.1, overlap = 11.6875
PHY-3002 : Step(91): len = 58784.1, overlap = 9.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0002219
PHY-3002 : Step(92): len = 59182.8, overlap = 11.6875
PHY-3002 : Step(93): len = 59219.6, overlap = 11.6875
PHY-3002 : Step(94): len = 59310.5, overlap = 11.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.0004438
PHY-3002 : Step(95): len = 59417.2, overlap = 11.5625
PHY-3002 : Step(96): len = 59347.4, overlap = 11.5625
PHY-3001 : Before Legalized: Len = 59347.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008335s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 61136.8, Over = 2.5625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060367s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00399697
PHY-3002 : Step(97): len = 61408.7, overlap = 8.375
PHY-3002 : Step(98): len = 60375.4, overlap = 8.9375
PHY-3002 : Step(99): len = 59761.9, overlap = 8.40625
PHY-3002 : Step(100): len = 58449, overlap = 7.875
PHY-3002 : Step(101): len = 57346.4, overlap = 8.25
PHY-3002 : Step(102): len = 56713, overlap = 8.53125
PHY-3002 : Step(103): len = 55199.3, overlap = 8.625
PHY-3002 : Step(104): len = 54052.5, overlap = 8.875
PHY-3002 : Step(105): len = 53169, overlap = 9.25
PHY-3002 : Step(106): len = 52147.4, overlap = 9.75
PHY-3002 : Step(107): len = 51049.3, overlap = 11.6875
PHY-3002 : Step(108): len = 50176.5, overlap = 12.9375
PHY-3002 : Step(109): len = 49954.8, overlap = 13.0625
PHY-3002 : Step(110): len = 49821, overlap = 13.5625
PHY-3002 : Step(111): len = 49524.9, overlap = 14.4375
PHY-3002 : Step(112): len = 49321.2, overlap = 14.75
PHY-3002 : Step(113): len = 48310.1, overlap = 15.4688
PHY-3002 : Step(114): len = 47818.5, overlap = 15.6562
PHY-3002 : Step(115): len = 47703, overlap = 15.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00799394
PHY-3002 : Step(116): len = 47759, overlap = 15.8438
PHY-3002 : Step(117): len = 47861, overlap = 15.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057785s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.93293e-05
PHY-3002 : Step(118): len = 47800.3, overlap = 60.625
PHY-3002 : Step(119): len = 48743.5, overlap = 59.2812
PHY-3002 : Step(120): len = 50171.7, overlap = 57.375
PHY-3002 : Step(121): len = 50658.7, overlap = 52.8438
PHY-3002 : Step(122): len = 50376.9, overlap = 53.0938
PHY-3002 : Step(123): len = 49761.8, overlap = 52.125
PHY-3002 : Step(124): len = 49226.3, overlap = 51.4062
PHY-3002 : Step(125): len = 48776, overlap = 49.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000138659
PHY-3002 : Step(126): len = 48705.5, overlap = 49.4688
PHY-3002 : Step(127): len = 48800.5, overlap = 48.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000275314
PHY-3002 : Step(128): len = 49298.7, overlap = 48.6562
PHY-3002 : Step(129): len = 50388.1, overlap = 46.4062
PHY-3002 : Step(130): len = 51371.3, overlap = 41.5312
PHY-3002 : Step(131): len = 52741.2, overlap = 36.625
PHY-3002 : Step(132): len = 52894.9, overlap = 35.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7416, tnet num: 2078, tinst num: 1560, tnode num: 10516, tedge num: 12520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.53 peak overflow 3.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56112, over cnt = 214(0%), over = 1113, worst = 29
PHY-1001 : End global iterations;  0.062801s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (223.9%)

PHY-1001 : Congestion index: top1 = 50.78, top5 = 26.73, top10 = 16.64, top15 = 11.83.
PHY-1001 : End incremental global routing;  0.111057s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (168.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065747s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (95.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.205637s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (136.8%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1637/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56112, over cnt = 214(0%), over = 1113, worst = 29
PHY-1002 : len = 64504, over cnt = 178(0%), over = 490, worst = 20
PHY-1002 : len = 69336, over cnt = 40(0%), over = 71, worst = 12
PHY-1002 : len = 70344, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 71016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104529s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (119.6%)

PHY-1001 : Congestion index: top1 = 43.56, top5 = 26.25, top10 = 18.72, top15 = 13.83.
OPT-1001 : End congestion update;  0.148202s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (116.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055163s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.207600s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (112.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.660554s wall, 0.718750s user + 0.046875s system = 0.765625s CPU (115.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 191 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 663 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1038/1343 primitive instances ...
PHY-3001 : End packing;  0.046606s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 785 instances
RUN-1001 : 367 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1897 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 783 instances, 734 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52569.6, Over = 60.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6180, tnet num: 1895, tinst num: 783, tnode num: 8406, tedge num: 10870.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.275801s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.04375e-05
PHY-3002 : Step(133): len = 51678.3, overlap = 58.5
PHY-3002 : Step(134): len = 50964.4, overlap = 57
PHY-3002 : Step(135): len = 50144.3, overlap = 58.25
PHY-3002 : Step(136): len = 49856.9, overlap = 64
PHY-3002 : Step(137): len = 49637.5, overlap = 60.5
PHY-3002 : Step(138): len = 49356.2, overlap = 59.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.0875e-05
PHY-3002 : Step(139): len = 49952, overlap = 55.25
PHY-3002 : Step(140): len = 50458.5, overlap = 50.75
PHY-3002 : Step(141): len = 51735.7, overlap = 46
PHY-3002 : Step(142): len = 52214.1, overlap = 47.75
PHY-3002 : Step(143): len = 52301.8, overlap = 44.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00016175
PHY-3002 : Step(144): len = 52600.8, overlap = 46.5
PHY-3002 : Step(145): len = 53053.4, overlap = 44.5
PHY-3001 : Before Legalized: Len = 53053.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.079885s wall, 0.093750s user + 0.093750s system = 0.187500s CPU (234.7%)

PHY-3001 : After Legalized: Len = 66426.7, Over = 0
PHY-3001 : Trial Legalized: Len = 66426.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046395s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000819892
PHY-3002 : Step(146): len = 62986.3, overlap = 12.25
PHY-3002 : Step(147): len = 62293, overlap = 11.75
PHY-3002 : Step(148): len = 60313.5, overlap = 12.5
PHY-3002 : Step(149): len = 59154, overlap = 15.5
PHY-3002 : Step(150): len = 58512.5, overlap = 20
PHY-3002 : Step(151): len = 58017.3, overlap = 23
PHY-3002 : Step(152): len = 57708.3, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00163978
PHY-3002 : Step(153): len = 58075.1, overlap = 22.75
PHY-3002 : Step(154): len = 58202.1, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00327957
PHY-3002 : Step(155): len = 58306.1, overlap = 22.75
PHY-3002 : Step(156): len = 58306.1, overlap = 22.75
PHY-3001 : Before Legalized: Len = 58306.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005179s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62546.4, Over = 0
PHY-3001 : Legalized: Len = 62546.4, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005066s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (308.4%)

PHY-3001 : 5 instances has been re-located, deltaX = 6, deltaY = 1, maxDist = 2.
PHY-3001 : Final: Len = 62646.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6180, tnet num: 1895, tinst num: 783, tnode num: 8406, tedge num: 10870.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 31/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68848, over cnt = 140(0%), over = 185, worst = 4
PHY-1002 : len = 69552, over cnt = 64(0%), over = 75, worst = 3
PHY-1002 : len = 70392, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 70456, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130732s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.5%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.29, top10 = 17.55, top15 = 13.61.
PHY-1001 : End incremental global routing;  0.182188s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056412s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265323s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (111.9%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1686/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007071s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.29, top10 = 17.55, top15 = 13.61.
OPT-1001 : End congestion update;  0.053229s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049769s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 743 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 783 instances, 734 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62517.6, Over = 0
PHY-3001 : End spreading;  0.004791s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (326.1%)

PHY-3001 : Final: Len = 62517.6, Over = 0
PHY-3001 : End incremental legalization;  0.036000s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (86.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152704s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (102.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045124s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (69.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007448s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (209.8%)

PHY-1001 : Congestion index: top1 = 32.35, top5 = 23.31, top10 = 17.53, top15 = 13.58.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046825s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.824321s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (104.3%)

RUN-1003 : finish command "place" in  5.058985s wall, 7.125000s user + 3.203125s system = 10.328125s CPU (204.2%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 785 instances
RUN-1001 : 367 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1897 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6180, tnet num: 1895, tinst num: 783, tnode num: 8406, tedge num: 10870.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 367 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68136, over cnt = 147(0%), over = 192, worst = 4
PHY-1002 : len = 68848, over cnt = 66(0%), over = 78, worst = 3
PHY-1002 : len = 69560, over cnt = 22(0%), over = 27, worst = 2
PHY-1002 : len = 69840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113385s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (110.2%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.24, top10 = 17.48, top15 = 13.53.
PHY-1001 : End global routing;  0.164820s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (113.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 464, peak = 493.
PHY-1001 : End build detailed router design. 3.155286s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.070072s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.075988s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180448, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 529.
PHY-1001 : End initial routed; 1.377878s wall, 2.218750s user + 0.156250s system = 2.375000s CPU (172.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1688(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.572   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.251  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.341165s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 2; 1.719145s wall, 2.562500s user + 0.156250s system = 2.718750s CPU (158.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180448, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016620s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180432, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025201s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020810s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (75.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1688(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.572   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.251  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.324513s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (101.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.169386s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End phase 3; 0.681828s wall, 0.671875s user + 0.031250s system = 0.703125s CPU (103.1%)

PHY-1003 : Routed, final wirelength = 180440
PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End export database. 0.009980s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.6%)

PHY-1001 : End detail routing;  6.823346s wall, 7.515625s user + 0.312500s system = 7.828125s CPU (114.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6180, tnet num: 1895, tinst num: 783, tnode num: 8406, tedge num: 10870.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.addra[4] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_102.mi[0] slack -2791ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_102.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_82.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_85.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_85.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_88.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_90.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_93.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_93.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_96.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_96.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_99.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_99.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin wendu/lt0_syn_87.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6294, tnet num: 1952, tinst num: 840, tnode num: 8520, tedge num: 10984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin wendu/lt0_syn_87_mi[0] slack -171ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_99_mi[0] slack -680ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_99_mi[1] slack -96ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_102_mi[1] slack -568ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_102_mi[0] slack -602ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -242ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -503ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -224ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -754ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -226ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -53ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_96_mi[0] slack -223ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_96_mi[1] slack -105ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -399ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -54ps
RUN-1001 : End hold fix;  3.092492s wall, 3.187500s user + 0.390625s system = 3.578125s CPU (115.7%)

RUN-1003 : finish command "route" in  10.414456s wall, 11.203125s user + 0.734375s system = 11.937500s CPU (114.6%)

RUN-1004 : used memory is 503 MB, reserved memory is 480 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      872   out of  19600    4.45%
#reg                     1018   out of  19600    5.19%
#le                      1535
  #lut only               517   out of   1535   33.68%
  #reg only               663   out of   1535   43.19%
  #lut&reg                355   out of   1535   23.13%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         461
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1535   |682     |190     |1051    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1033   |274     |126     |826     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |26      |4       |21      |0       |0       |
|    demodu                  |Demodulation                                     |446    |98      |46      |349     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|    integ                   |Integration                                      |144    |21      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |62     |36      |7       |60      |0       |1       |
|    rs422                   |Rs422Output                                      |326    |76      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |17      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |173    |136     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |28     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |109    |89      |0       |83      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1375  
    #2          2       299   
    #3          3       110   
    #4          4        25   
    #5        5-10       70   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6294, tnet num: 1952, tinst num: 840, tnode num: 8520, tedge num: 10984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 840
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1954, pip num: 14540
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1332 valid insts, and 38734 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.227972s wall, 18.203125s user + 0.093750s system = 18.296875s CPU (566.8%)

RUN-1004 : used memory is 516 MB, reserved memory is 486 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250723_184547.log"

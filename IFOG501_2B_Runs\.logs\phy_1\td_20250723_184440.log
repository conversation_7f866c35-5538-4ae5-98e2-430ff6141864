============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 23 18:44:40 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1559 instances
RUN-0007 : 373 luts, 945 seqs, 120 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2077 nets
RUN-1001 : 1532 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1557 instances, 373 luts, 945 seqs, 190 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7405, tnet num: 2075, tinst num: 1557, tnode num: 10505, tedge num: 12504.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.263674s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (100.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 521250
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1557.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 456728, overlap = 18
PHY-3002 : Step(2): len = 429466, overlap = 11.25
PHY-3002 : Step(3): len = 416111, overlap = 20.25
PHY-3002 : Step(4): len = 403070, overlap = 13.5
PHY-3002 : Step(5): len = 391598, overlap = 18
PHY-3002 : Step(6): len = 370249, overlap = 15.75
PHY-3002 : Step(7): len = 361824, overlap = 15.75
PHY-3002 : Step(8): len = 354979, overlap = 18
PHY-3002 : Step(9): len = 340989, overlap = 15.75
PHY-3002 : Step(10): len = 332278, overlap = 13.5
PHY-3002 : Step(11): len = 327446, overlap = 11.25
PHY-3002 : Step(12): len = 316619, overlap = 11.25
PHY-3002 : Step(13): len = 307434, overlap = 11.25
PHY-3002 : Step(14): len = 302255, overlap = 13.5
PHY-3002 : Step(15): len = 295909, overlap = 13.5
PHY-3002 : Step(16): len = 286095, overlap = 15.75
PHY-3002 : Step(17): len = 279628, overlap = 15.75
PHY-3002 : Step(18): len = 276274, overlap = 15.75
PHY-3002 : Step(19): len = 266468, overlap = 15.75
PHY-3002 : Step(20): len = 255604, overlap = 15.75
PHY-3002 : Step(21): len = 252228, overlap = 15.75
PHY-3002 : Step(22): len = 247078, overlap = 13.5
PHY-3002 : Step(23): len = 230288, overlap = 20.25
PHY-3002 : Step(24): len = 224066, overlap = 20.25
PHY-3002 : Step(25): len = 221988, overlap = 20.25
PHY-3002 : Step(26): len = 198536, overlap = 18
PHY-3002 : Step(27): len = 182201, overlap = 20.25
PHY-3002 : Step(28): len = 180118, overlap = 20.25
PHY-3002 : Step(29): len = 175895, overlap = 20.25
PHY-3002 : Step(30): len = 159844, overlap = 20.25
PHY-3002 : Step(31): len = 155448, overlap = 20.25
PHY-3002 : Step(32): len = 153644, overlap = 20.25
PHY-3002 : Step(33): len = 149021, overlap = 20.25
PHY-3002 : Step(34): len = 146441, overlap = 20.25
PHY-3002 : Step(35): len = 142953, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109049
PHY-3002 : Step(36): len = 143294, overlap = 15.75
PHY-3002 : Step(37): len = 141943, overlap = 15.75
PHY-3002 : Step(38): len = 140825, overlap = 15.75
PHY-3002 : Step(39): len = 138085, overlap = 11.25
PHY-3002 : Step(40): len = 133384, overlap = 11.25
PHY-3002 : Step(41): len = 132368, overlap = 13.5
PHY-3002 : Step(42): len = 129068, overlap = 11.25
PHY-3002 : Step(43): len = 126260, overlap = 11.25
PHY-3002 : Step(44): len = 125281, overlap = 11.25
PHY-3002 : Step(45): len = 122695, overlap = 11.25
PHY-3002 : Step(46): len = 118317, overlap = 11.25
PHY-3002 : Step(47): len = 116846, overlap = 13.5
PHY-3002 : Step(48): len = 115627, overlap = 15.75
PHY-3002 : Step(49): len = 112281, overlap = 13.5
PHY-3002 : Step(50): len = 108918, overlap = 11.25
PHY-3002 : Step(51): len = 107334, overlap = 13.5
PHY-3002 : Step(52): len = 106280, overlap = 13.5
PHY-3002 : Step(53): len = 104445, overlap = 13.5
PHY-3002 : Step(54): len = 103474, overlap = 15.75
PHY-3002 : Step(55): len = 101451, overlap = 13.5
PHY-3002 : Step(56): len = 98944.3, overlap = 13.5
PHY-3002 : Step(57): len = 97170.9, overlap = 13.5
PHY-3002 : Step(58): len = 95565.2, overlap = 15.75
PHY-3002 : Step(59): len = 93117, overlap = 15.75
PHY-3002 : Step(60): len = 91633.4, overlap = 13.5
PHY-3002 : Step(61): len = 89773.1, overlap = 15.75
PHY-3002 : Step(62): len = 88125.7, overlap = 18
PHY-3002 : Step(63): len = 83908.7, overlap = 15.75
PHY-3002 : Step(64): len = 82291.6, overlap = 15.75
PHY-3002 : Step(65): len = 81474.3, overlap = 15.75
PHY-3002 : Step(66): len = 78512.3, overlap = 14.3125
PHY-3002 : Step(67): len = 74336.7, overlap = 13.25
PHY-3002 : Step(68): len = 72998.9, overlap = 18.125
PHY-3002 : Step(69): len = 72039.7, overlap = 15.9375
PHY-3002 : Step(70): len = 71477.1, overlap = 13.6875
PHY-3002 : Step(71): len = 71049.2, overlap = 13.9375
PHY-3002 : Step(72): len = 70268.8, overlap = 13.875
PHY-3002 : Step(73): len = 68648.1, overlap = 12.0625
PHY-3002 : Step(74): len = 67482.6, overlap = 9.8125
PHY-3002 : Step(75): len = 66729.9, overlap = 12.0625
PHY-3002 : Step(76): len = 65873.4, overlap = 9.75
PHY-3002 : Step(77): len = 65522.1, overlap = 9.625
PHY-3002 : Step(78): len = 64704.8, overlap = 11.75
PHY-3002 : Step(79): len = 64492.7, overlap = 11.5625
PHY-3002 : Step(80): len = 64487, overlap = 9.125
PHY-3002 : Step(81): len = 64198.4, overlap = 6.9375
PHY-3002 : Step(82): len = 63722.3, overlap = 6.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000218099
PHY-3002 : Step(83): len = 64321.5, overlap = 9.1875
PHY-3002 : Step(84): len = 64613.6, overlap = 9.1875
PHY-3002 : Step(85): len = 64684.8, overlap = 9.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000436198
PHY-3002 : Step(86): len = 64733.6, overlap = 9.1875
PHY-3002 : Step(87): len = 64897.8, overlap = 9.0625
PHY-3001 : Before Legalized: Len = 64897.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011698s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (400.7%)

PHY-3001 : After Legalized: Len = 68033.8, Over = 2.3125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071467s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000977777
PHY-3002 : Step(88): len = 68278.5, overlap = 7.96875
PHY-3002 : Step(89): len = 67004.6, overlap = 8.53125
PHY-3002 : Step(90): len = 65515.7, overlap = 8.625
PHY-3002 : Step(91): len = 64481.6, overlap = 8.1875
PHY-3002 : Step(92): len = 63324.6, overlap = 8.125
PHY-3002 : Step(93): len = 61941.6, overlap = 7.53125
PHY-3002 : Step(94): len = 60609.2, overlap = 11.875
PHY-3002 : Step(95): len = 59103.3, overlap = 12.4375
PHY-3002 : Step(96): len = 57405.8, overlap = 14.0938
PHY-3002 : Step(97): len = 55939.1, overlap = 14.5625
PHY-3002 : Step(98): len = 54631.6, overlap = 14.1875
PHY-3002 : Step(99): len = 53139.3, overlap = 14.4062
PHY-3002 : Step(100): len = 51503.4, overlap = 19.1562
PHY-3002 : Step(101): len = 50093.6, overlap = 20.8438
PHY-3002 : Step(102): len = 49170.7, overlap = 21.4375
PHY-3002 : Step(103): len = 48492.3, overlap = 21.8125
PHY-3002 : Step(104): len = 47582.9, overlap = 20.4062
PHY-3002 : Step(105): len = 46781.9, overlap = 21.5312
PHY-3002 : Step(106): len = 46204.8, overlap = 22.2812
PHY-3002 : Step(107): len = 45717.1, overlap = 22.3125
PHY-3002 : Step(108): len = 45016.2, overlap = 23.4375
PHY-3002 : Step(109): len = 44304.3, overlap = 21.9062
PHY-3002 : Step(110): len = 44096.5, overlap = 19.6562
PHY-3002 : Step(111): len = 44058.6, overlap = 19.375
PHY-3002 : Step(112): len = 43712.2, overlap = 20
PHY-3002 : Step(113): len = 43419.5, overlap = 21.2812
PHY-3002 : Step(114): len = 43306.9, overlap = 22.0938
PHY-3002 : Step(115): len = 43396.5, overlap = 22.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00195555
PHY-3002 : Step(116): len = 43242.5, overlap = 21.1562
PHY-3002 : Step(117): len = 43160.2, overlap = 19.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074269s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.35347e-05
PHY-3002 : Step(118): len = 43762.1, overlap = 60.2188
PHY-3002 : Step(119): len = 44910.5, overlap = 59.3438
PHY-3002 : Step(120): len = 45652.8, overlap = 59.3125
PHY-3002 : Step(121): len = 45528.7, overlap = 48.625
PHY-3002 : Step(122): len = 45044.6, overlap = 46.3438
PHY-3002 : Step(123): len = 45123.7, overlap = 46.0625
PHY-3002 : Step(124): len = 45143, overlap = 46.0312
PHY-3002 : Step(125): len = 45548.7, overlap = 44.6875
PHY-3002 : Step(126): len = 45800.6, overlap = 39.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127069
PHY-3002 : Step(127): len = 45743.2, overlap = 40.1562
PHY-3002 : Step(128): len = 45832.9, overlap = 39.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00025081
PHY-3002 : Step(129): len = 46208, overlap = 38.5312
PHY-3002 : Step(130): len = 47028.1, overlap = 33.75
PHY-3002 : Step(131): len = 47851.7, overlap = 31.3125
PHY-3002 : Step(132): len = 47639.1, overlap = 30.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7405, tnet num: 2075, tinst num: 1557, tnode num: 10505, tedge num: 12504.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.72 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2077.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49944, over cnt = 247(0%), over = 1059, worst = 28
PHY-1001 : End global iterations;  0.074535s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (125.8%)

PHY-1001 : Congestion index: top1 = 40.95, top5 = 23.57, top10 = 15.30, top15 = 10.95.
PHY-1001 : End incremental global routing;  0.123419s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (113.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063948s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215030s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.0%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1615/2077.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49944, over cnt = 247(0%), over = 1059, worst = 28
PHY-1002 : len = 56576, over cnt = 172(0%), over = 416, worst = 13
PHY-1002 : len = 61440, over cnt = 33(0%), over = 37, worst = 4
PHY-1002 : len = 61744, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 62256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.103692s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (120.5%)

PHY-1001 : Congestion index: top1 = 36.10, top5 = 23.58, top10 = 16.88, top15 = 12.59.
OPT-1001 : End congestion update;  0.147905s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054748s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206298s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.670749s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (109.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 76 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 679 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1052/1357 primitive instances ...
PHY-3001 : End packing;  0.049282s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 789 instances
RUN-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1895 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 787 instances, 738 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 47712.2, Over = 58
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6185, tnet num: 1893, tinst num: 787, tnode num: 8425, tedge num: 10879.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.273570s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.05874e-05
PHY-3002 : Step(133): len = 47161, overlap = 59.25
PHY-3002 : Step(134): len = 46722.9, overlap = 58.25
PHY-3002 : Step(135): len = 46671.3, overlap = 59
PHY-3002 : Step(136): len = 46807.8, overlap = 57.25
PHY-3002 : Step(137): len = 46761, overlap = 56
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.11749e-05
PHY-3002 : Step(138): len = 47127.3, overlap = 54.25
PHY-3002 : Step(139): len = 47397.4, overlap = 51.75
PHY-3002 : Step(140): len = 48171, overlap = 51.5
PHY-3002 : Step(141): len = 48642, overlap = 50
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00012235
PHY-3002 : Step(142): len = 48725.3, overlap = 51
PHY-3002 : Step(143): len = 48806.2, overlap = 50.75
PHY-3001 : Before Legalized: Len = 48806.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078629s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (159.0%)

PHY-3001 : After Legalized: Len = 61696.5, Over = 0
PHY-3001 : Trial Legalized: Len = 61696.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046721s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000676887
PHY-3002 : Step(144): len = 58476.9, overlap = 8.75
PHY-3002 : Step(145): len = 56651.3, overlap = 14.25
PHY-3002 : Step(146): len = 55074.9, overlap = 15.25
PHY-3002 : Step(147): len = 54553.9, overlap = 18.75
PHY-3002 : Step(148): len = 54005.4, overlap = 19.75
PHY-3002 : Step(149): len = 53611.7, overlap = 21.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00135377
PHY-3002 : Step(150): len = 53955.9, overlap = 20.25
PHY-3002 : Step(151): len = 54112.9, overlap = 20.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00270755
PHY-3002 : Step(152): len = 54122.4, overlap = 20.5
PHY-3002 : Step(153): len = 54122.4, overlap = 20.5
PHY-3001 : Before Legalized: Len = 54122.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005077s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 58385, Over = 0
PHY-3001 : Legalized: Len = 58385, Over = 0
PHY-3001 : Spreading special nets. 12 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005518s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 18 instances has been re-located, deltaX = 2, deltaY = 18, maxDist = 2.
PHY-3001 : Final: Len = 58715, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6185, tnet num: 1893, tinst num: 787, tnode num: 8425, tedge num: 10879.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 71/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63688, over cnt = 139(0%), over = 219, worst = 8
PHY-1002 : len = 64808, over cnt = 86(0%), over = 101, worst = 4
PHY-1002 : len = 65808, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 66056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108903s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (129.1%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 21.95, top10 = 16.66, top15 = 12.92.
PHY-1001 : End incremental global routing;  0.159126s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (127.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055578s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.243024s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1690/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005144s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 21.95, top10 = 16.66, top15 = 12.92.
OPT-1001 : End congestion update;  0.049946s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046948s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 747 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 787 instances, 738 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 58688.2, Over = 0
PHY-3001 : End spreading;  0.005177s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 58688.2, Over = 0
PHY-3001 : End incremental legalization;  0.035494s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (308.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.147721s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (137.5%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047434s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66032, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66032, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024555s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.3%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 21.96, top10 = 16.68, top15 = 12.93.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044367s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.822193s wall, 0.859375s user + 0.062500s system = 0.921875s CPU (112.1%)

RUN-1003 : finish command "place" in  5.044178s wall, 6.953125s user + 3.328125s system = 10.281250s CPU (203.8%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 789 instances
RUN-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1895 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6185, tnet num: 1893, tinst num: 787, tnode num: 8425, tedge num: 10879.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 62848, over cnt = 138(0%), over = 223, worst = 8
PHY-1002 : len = 64040, over cnt = 92(0%), over = 110, worst = 4
PHY-1002 : len = 65128, over cnt = 19(0%), over = 23, worst = 2
PHY-1002 : len = 65472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120277s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (116.9%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 21.82, top10 = 16.59, top15 = 12.85.
PHY-1001 : End global routing;  0.170715s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (109.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.086097s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.009387s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End phase 1; 1.015225s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175592, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 1.228806s wall, 1.796875s user + 0.187500s system = 1.984375s CPU (161.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1686(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.133  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.326489s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.555393s wall, 2.125000s user + 0.187500s system = 2.312500s CPU (148.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175592, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013833s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (113.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175528, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027999s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (167.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175544, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.022923s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (204.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 175576, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018524s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1686(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.133  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.326772s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.159955s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.7%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.697755s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (103.0%)

PHY-1003 : Routed, final wirelength = 175576
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.009391s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.4%)

PHY-1001 : End detail routing;  6.540256s wall, 7.062500s user + 0.265625s system = 7.328125s CPU (112.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6185, tnet num: 1893, tinst num: 787, tnode num: 8425, tedge num: 10879.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.373310s wall, 7.890625s user + 0.281250s system = 8.171875s CPU (110.8%)

RUN-1004 : used memory is 499 MB, reserved memory is 473 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      756   out of  19600    3.86%
#reg                     1018   out of  19600    5.19%
#le                      1435
  #lut only               417   out of   1435   29.06%
  #reg only               679   out of   1435   47.32%
  #lut&reg                339   out of   1435   23.62%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         462
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1435   |566     |190     |1051    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1031   |260     |127     |828     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |31      |5       |20      |0       |0       |
|    demodu                  |Demodulation                                     |432    |78      |46      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |140    |19      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |67     |35      |7       |65      |0       |1       |
|    rs422                   |Rs422Output                                      |327    |76      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |21      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |170    |128     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |109    |82      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1318  
    #2          2       301   
    #3          3       108   
    #4          4        21   
    #5        5-10       72   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6185, tnet num: 1893, tinst num: 787, tnode num: 8425, tedge num: 10879.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 787
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1895, pip num: 14086
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1286 valid insts, and 37012 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.178348s wall, 16.875000s user + 0.187500s system = 17.062500s CPU (536.8%)

RUN-1004 : used memory is 512 MB, reserved memory is 484 MB, peak memory is 662 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250723_184440.log"

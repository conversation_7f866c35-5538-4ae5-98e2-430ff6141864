============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 16 17:36:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1596 instances
RUN-0007 : 378 luts, 970 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2128 nets
RUN-1001 : 1550 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     271     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1594 instances, 378 luts, 970 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7573, tnet num: 2126, tinst num: 1594, tnode num: 10737, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.272720s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 551917
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1594.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 476619, overlap = 18
PHY-3002 : Step(2): len = 448211, overlap = 18
PHY-3002 : Step(3): len = 433912, overlap = 18
PHY-3002 : Step(4): len = 419375, overlap = 9
PHY-3002 : Step(5): len = 409525, overlap = 6.75
PHY-3002 : Step(6): len = 392248, overlap = 11.25
PHY-3002 : Step(7): len = 377223, overlap = 15.75
PHY-3002 : Step(8): len = 370377, overlap = 15.75
PHY-3002 : Step(9): len = 356517, overlap = 15.75
PHY-3002 : Step(10): len = 346889, overlap = 15.75
PHY-3002 : Step(11): len = 340339, overlap = 13.5
PHY-3002 : Step(12): len = 328351, overlap = 13.5
PHY-3002 : Step(13): len = 319134, overlap = 13.5
PHY-3002 : Step(14): len = 313035, overlap = 15.75
PHY-3002 : Step(15): len = 304156, overlap = 15.75
PHY-3002 : Step(16): len = 296462, overlap = 13.5
PHY-3002 : Step(17): len = 291232, overlap = 13.5
PHY-3002 : Step(18): len = 283208, overlap = 13.5
PHY-3002 : Step(19): len = 277749, overlap = 13.5
PHY-3002 : Step(20): len = 272748, overlap = 13.5
PHY-3002 : Step(21): len = 266177, overlap = 13.5
PHY-3002 : Step(22): len = 259401, overlap = 13.5
PHY-3002 : Step(23): len = 255479, overlap = 13.5
PHY-3002 : Step(24): len = 249890, overlap = 20.25
PHY-3002 : Step(25): len = 243477, overlap = 20.25
PHY-3002 : Step(26): len = 238624, overlap = 20.25
PHY-3002 : Step(27): len = 234992, overlap = 20.25
PHY-3002 : Step(28): len = 228404, overlap = 20.25
PHY-3002 : Step(29): len = 223143, overlap = 20.25
PHY-3002 : Step(30): len = 219618, overlap = 20.25
PHY-3002 : Step(31): len = 214856, overlap = 20.25
PHY-3002 : Step(32): len = 204090, overlap = 20.25
PHY-3002 : Step(33): len = 199900, overlap = 20.25
PHY-3002 : Step(34): len = 197076, overlap = 20.25
PHY-3002 : Step(35): len = 190500, overlap = 20.25
PHY-3002 : Step(36): len = 177918, overlap = 20.25
PHY-3002 : Step(37): len = 174957, overlap = 20.25
PHY-3002 : Step(38): len = 172222, overlap = 20.25
PHY-3002 : Step(39): len = 115277, overlap = 18
PHY-3002 : Step(40): len = 111790, overlap = 20.25
PHY-3002 : Step(41): len = 110962, overlap = 20.25
PHY-3002 : Step(42): len = 108442, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.58691e-05
PHY-3002 : Step(43): len = 109063, overlap = 13.5
PHY-3002 : Step(44): len = 108633, overlap = 13.5
PHY-3002 : Step(45): len = 108075, overlap = 15.75
PHY-3002 : Step(46): len = 107608, overlap = 18
PHY-3002 : Step(47): len = 106147, overlap = 18
PHY-3002 : Step(48): len = 101446, overlap = 11.25
PHY-3002 : Step(49): len = 98865.7, overlap = 11.25
PHY-3002 : Step(50): len = 97764, overlap = 11.25
PHY-3002 : Step(51): len = 95920.9, overlap = 15.75
PHY-3002 : Step(52): len = 95474.3, overlap = 15.75
PHY-3002 : Step(53): len = 91323, overlap = 15.75
PHY-3002 : Step(54): len = 89768.9, overlap = 13.5
PHY-3002 : Step(55): len = 88668.9, overlap = 15.75
PHY-3002 : Step(56): len = 87723.4, overlap = 13.5
PHY-3002 : Step(57): len = 87559.9, overlap = 13.5
PHY-3002 : Step(58): len = 85745.6, overlap = 11.25
PHY-3002 : Step(59): len = 84153.8, overlap = 11.25
PHY-3002 : Step(60): len = 84100.1, overlap = 11.25
PHY-3002 : Step(61): len = 82155.8, overlap = 15.75
PHY-3002 : Step(62): len = 81011, overlap = 15.75
PHY-3002 : Step(63): len = 80777.8, overlap = 15.75
PHY-3002 : Step(64): len = 79410.7, overlap = 13.5
PHY-3002 : Step(65): len = 78548.4, overlap = 13.5
PHY-3002 : Step(66): len = 76815.1, overlap = 11.25
PHY-3002 : Step(67): len = 74944.2, overlap = 11.25
PHY-3002 : Step(68): len = 74197.5, overlap = 11.25
PHY-3002 : Step(69): len = 73773, overlap = 11.25
PHY-3002 : Step(70): len = 73004.5, overlap = 15.75
PHY-3002 : Step(71): len = 71953.4, overlap = 18
PHY-3002 : Step(72): len = 71452.7, overlap = 15.75
PHY-3002 : Step(73): len = 70493.5, overlap = 11.25
PHY-3002 : Step(74): len = 69612.9, overlap = 13.5625
PHY-3002 : Step(75): len = 68887, overlap = 13.5
PHY-3002 : Step(76): len = 68368.3, overlap = 13.5
PHY-3002 : Step(77): len = 68039, overlap = 15.75
PHY-3002 : Step(78): len = 67127.5, overlap = 15.75
PHY-3002 : Step(79): len = 66764, overlap = 15.75
PHY-3002 : Step(80): len = 66040.5, overlap = 13.5
PHY-3002 : Step(81): len = 65589.6, overlap = 13.5
PHY-3002 : Step(82): len = 65786.7, overlap = 13.5
PHY-3002 : Step(83): len = 65695.6, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000191738
PHY-3002 : Step(84): len = 65289, overlap = 13.5
PHY-3002 : Step(85): len = 65206.2, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000383476
PHY-3002 : Step(86): len = 65636.1, overlap = 13.5
PHY-3002 : Step(87): len = 65777.1, overlap = 13.5
PHY-3002 : Step(88): len = 65892.3, overlap = 13.5
PHY-3001 : Before Legalized: Len = 65892.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010093s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 70619.2, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066674s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(89): len = 71005.5, overlap = 3.25
PHY-3002 : Step(90): len = 69748, overlap = 3.5
PHY-3002 : Step(91): len = 68948.4, overlap = 4.6875
PHY-3002 : Step(92): len = 67758.6, overlap = 4.25
PHY-3002 : Step(93): len = 66912.6, overlap = 3.75
PHY-3002 : Step(94): len = 65190, overlap = 4.5
PHY-3002 : Step(95): len = 63401.6, overlap = 4.5625
PHY-3002 : Step(96): len = 62615.6, overlap = 4.6875
PHY-3002 : Step(97): len = 61788.8, overlap = 5.5625
PHY-3002 : Step(98): len = 60794, overlap = 5.8125
PHY-3002 : Step(99): len = 59462.9, overlap = 4.03125
PHY-3002 : Step(100): len = 58098.4, overlap = 4.28125
PHY-3002 : Step(101): len = 57314.7, overlap = 6.84375
PHY-3002 : Step(102): len = 56572.5, overlap = 6.78125
PHY-3002 : Step(103): len = 55836.6, overlap = 7.78125
PHY-3002 : Step(104): len = 55273.1, overlap = 11.0938
PHY-3002 : Step(105): len = 54540.6, overlap = 14.5312
PHY-3002 : Step(106): len = 54393.5, overlap = 15.1562
PHY-3002 : Step(107): len = 53953.5, overlap = 15.4375
PHY-3002 : Step(108): len = 53223.9, overlap = 12.5938
PHY-3002 : Step(109): len = 52354.3, overlap = 12.4688
PHY-3002 : Step(110): len = 51730, overlap = 16.4062
PHY-3002 : Step(111): len = 51078.3, overlap = 17.125
PHY-3002 : Step(112): len = 50436.1, overlap = 17.6562
PHY-3002 : Step(113): len = 50123.1, overlap = 17.5625
PHY-3002 : Step(114): len = 49671.4, overlap = 17.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146882
PHY-3002 : Step(115): len = 49566.5, overlap = 17.7188
PHY-3002 : Step(116): len = 49478.9, overlap = 17.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000293764
PHY-3002 : Step(117): len = 49573.4, overlap = 16.875
PHY-3002 : Step(118): len = 49894.5, overlap = 13.0312
PHY-3002 : Step(119): len = 49921.5, overlap = 13.2812
PHY-3002 : Step(120): len = 49486.3, overlap = 14.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070961s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.96635e-05
PHY-3002 : Step(121): len = 49955.9, overlap = 48.875
PHY-3002 : Step(122): len = 50779, overlap = 49.3125
PHY-3002 : Step(123): len = 51179.5, overlap = 48.4375
PHY-3002 : Step(124): len = 50602.2, overlap = 48.3125
PHY-3002 : Step(125): len = 50496.7, overlap = 48.5938
PHY-3002 : Step(126): len = 50427.4, overlap = 47.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000139327
PHY-3002 : Step(127): len = 50609.2, overlap = 47.5938
PHY-3002 : Step(128): len = 50826, overlap = 47.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000249352
PHY-3002 : Step(129): len = 51830.5, overlap = 46.3125
PHY-3002 : Step(130): len = 52256.2, overlap = 44.8125
PHY-3002 : Step(131): len = 52939.3, overlap = 41.125
PHY-3002 : Step(132): len = 52863.8, overlap = 41.3125
PHY-3002 : Step(133): len = 53055.3, overlap = 40.4062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000491198
PHY-3002 : Step(134): len = 53278, overlap = 40.1562
PHY-3002 : Step(135): len = 53721.5, overlap = 39.4688
PHY-3002 : Step(136): len = 54017.2, overlap = 37.7812
PHY-3002 : Step(137): len = 54935.1, overlap = 33.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7573, tnet num: 2126, tinst num: 1594, tnode num: 10737, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.91 peak overflow 2.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2128.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56936, over cnt = 245(0%), over = 1096, worst = 17
PHY-1001 : End global iterations;  0.080877s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 45.22, top5 = 26.22, top10 = 16.71, top15 = 11.91.
PHY-1001 : End incremental global routing;  0.133048s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (94.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069869s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.234043s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (100.1%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1644/2128.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56936, over cnt = 245(0%), over = 1096, worst = 17
PHY-1002 : len = 64016, over cnt = 172(0%), over = 495, worst = 12
PHY-1002 : len = 69784, over cnt = 32(0%), over = 34, worst = 2
PHY-1002 : len = 70224, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 70624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106981s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 39.44, top5 = 26.19, top10 = 18.90, top15 = 13.90.
OPT-1001 : End congestion update;  0.150902s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060686s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.214737s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.710604s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (114.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 195 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 104 SEQ with LUT/SLICE
SYN-4006 : 93 single LUT's are left
SYN-4006 : 671 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1049/1361 primitive instances ...
PHY-3001 : End packing;  0.065951s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 796 instances
RUN-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1941 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 794 instances, 745 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54975, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6325, tnet num: 1939, tinst num: 794, tnode num: 8615, tedge num: 11160.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297044s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.01732e-05
PHY-3002 : Step(138): len = 54326, overlap = 66.5
PHY-3002 : Step(139): len = 53767.7, overlap = 67.25
PHY-3002 : Step(140): len = 53392.1, overlap = 68
PHY-3002 : Step(141): len = 53068.8, overlap = 67.75
PHY-3002 : Step(142): len = 52983.6, overlap = 67.5
PHY-3002 : Step(143): len = 52835.6, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.03464e-05
PHY-3002 : Step(144): len = 53371.2, overlap = 62.25
PHY-3002 : Step(145): len = 53863, overlap = 58.75
PHY-3002 : Step(146): len = 54476, overlap = 57.25
PHY-3002 : Step(147): len = 54831.3, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000160693
PHY-3002 : Step(148): len = 55526.2, overlap = 52
PHY-3002 : Step(149): len = 56007.2, overlap = 48.25
PHY-3001 : Before Legalized: Len = 56007.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077098s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (182.4%)

PHY-3001 : After Legalized: Len = 70316.7, Over = 0
PHY-3001 : Trial Legalized: Len = 70316.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055826s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00138647
PHY-3002 : Step(150): len = 66861.2, overlap = 5.5
PHY-3002 : Step(151): len = 64981.6, overlap = 12
PHY-3002 : Step(152): len = 62772.2, overlap = 14
PHY-3002 : Step(153): len = 61512.2, overlap = 17.25
PHY-3002 : Step(154): len = 60847.6, overlap = 20.25
PHY-3002 : Step(155): len = 60167.8, overlap = 23.75
PHY-3002 : Step(156): len = 59734.6, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0025385
PHY-3002 : Step(157): len = 59947.8, overlap = 24.75
PHY-3002 : Step(158): len = 59934, overlap = 26
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00507701
PHY-3002 : Step(159): len = 59996.3, overlap = 26
PHY-3002 : Step(160): len = 59999.9, overlap = 26.25
PHY-3001 : Before Legalized: Len = 59999.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005423s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (288.1%)

PHY-3001 : After Legalized: Len = 64445.7, Over = 0
PHY-3001 : Legalized: Len = 64445.7, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005536s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 3, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 64551.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6325, tnet num: 1939, tinst num: 794, tnode num: 8615, tedge num: 11160.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 93/1941.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71544, over cnt = 165(0%), over = 247, worst = 7
PHY-1002 : len = 72512, over cnt = 101(0%), over = 121, worst = 4
PHY-1002 : len = 73216, over cnt = 50(0%), over = 62, worst = 4
PHY-1002 : len = 74208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129514s wall, 0.218750s user + 0.062500s system = 0.281250s CPU (217.2%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.52, top10 = 17.99, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.182667s wall, 0.265625s user + 0.062500s system = 0.328125s CPU (179.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060395s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273747s wall, 0.359375s user + 0.062500s system = 0.421875s CPU (154.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1722/1941.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006309s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.52, top10 = 17.99, top15 = 13.95.
OPT-1001 : End congestion update;  0.055922s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057785s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.115696s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056994s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1722/1941.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007039s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.52, top10 = 17.99, top15 = 13.95.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060449s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.838445s wall, 0.906250s user + 0.062500s system = 0.968750s CPU (115.5%)

RUN-1003 : finish command "place" in  5.382097s wall, 7.687500s user + 2.718750s system = 10.406250s CPU (193.3%)

RUN-1004 : used memory is 195 MB, reserved memory is 161 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 796 instances
RUN-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1941 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6325, tnet num: 1939, tinst num: 794, tnode num: 8615, tedge num: 11160.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70344, over cnt = 163(0%), over = 248, worst = 7
PHY-1002 : len = 71624, over cnt = 82(0%), over = 99, worst = 3
PHY-1002 : len = 72816, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134003s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (128.3%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 23.29, top10 = 17.72, top15 = 13.74.
PHY-1001 : End global routing;  0.185404s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (118.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.200810s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31096, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.102815s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 524, reserve = 499, peak = 524.
PHY-1001 : End phase 1; 1.109326s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179544, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.580032s wall, 2.125000s user + 0.187500s system = 2.312500s CPU (146.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.166  |  25   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345969s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 1.926095s wall, 2.468750s user + 0.187500s system = 2.656250s CPU (137.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179544, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017146s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (91.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179304, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033861s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179360, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021855s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020013s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (156.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.166  |  25   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.347164s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.184895s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 543, reserve = 514, peak = 543.
PHY-1001 : End phase 3; 0.754841s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (101.4%)

PHY-1003 : Routed, final wirelength = 179376
PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End export database. 0.012037s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (129.8%)

PHY-1001 : End detail routing;  7.185423s wall, 7.687500s user + 0.234375s system = 7.921875s CPU (110.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6325, tnet num: 1939, tinst num: 794, tnode num: 8615, tedge num: 11160.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.070005s wall, 8.578125s user + 0.265625s system = 8.843750s CPU (109.6%)

RUN-1004 : used memory is 519 MB, reserved memory is 493 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      774   out of  19600    3.95%
#reg                     1052   out of  19600    5.37%
#le                      1445
  #lut only               393   out of   1445   27.20%
  #reg only               671   out of   1445   46.44%
  #lut&reg                381   out of   1445   26.37%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1445   |577     |197     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1047   |270     |135     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |439    |84      |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |141    |28      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |102    |51      |14      |100     |0       |1       |
|    rs422                   |Rs422Output                                      |315    |71      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |19      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |166    |130     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |102    |82      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1331  
    #2          2       332   
    #3          3       108   
    #4          4        27   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6325, tnet num: 1939, tinst num: 794, tnode num: 8615, tedge num: 11160.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1939 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 794
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1941, pip num: 14416
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1342 valid insts, and 37728 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.253737s wall, 18.484375s user + 0.046875s system = 18.531250s CPU (569.5%)

RUN-1004 : used memory is 542 MB, reserved memory is 515 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_173633.log"

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 23 18:47:13 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1563 instances
RUN-0007 : 375 luts, 947 seqs, 120 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2082 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1561 instances, 375 luts, 947 seqs, 190 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7427, tnet num: 2080, tinst num: 1561, tnode num: 10528, tedge num: 12541.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.253653s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (98.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 527901
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1561.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 456741, overlap = 20.25
PHY-3002 : Step(2): len = 432722, overlap = 18
PHY-3002 : Step(3): len = 419502, overlap = 20.25
PHY-3002 : Step(4): len = 408717, overlap = 13.5
PHY-3002 : Step(5): len = 395550, overlap = 15.75
PHY-3002 : Step(6): len = 375583, overlap = 11.25
PHY-3002 : Step(7): len = 368010, overlap = 13.5
PHY-3002 : Step(8): len = 359007, overlap = 15.75
PHY-3002 : Step(9): len = 339549, overlap = 15.75
PHY-3002 : Step(10): len = 332004, overlap = 15.75
PHY-3002 : Step(11): len = 326555, overlap = 13.5
PHY-3002 : Step(12): len = 302111, overlap = 15.75
PHY-3002 : Step(13): len = 292920, overlap = 15.75
PHY-3002 : Step(14): len = 289295, overlap = 15.75
PHY-3002 : Step(15): len = 279605, overlap = 13.5
PHY-3002 : Step(16): len = 270777, overlap = 13.5
PHY-3002 : Step(17): len = 265734, overlap = 13.5
PHY-3002 : Step(18): len = 261967, overlap = 13.5
PHY-3002 : Step(19): len = 248151, overlap = 15.75
PHY-3002 : Step(20): len = 242357, overlap = 20.25
PHY-3002 : Step(21): len = 238765, overlap = 20.25
PHY-3002 : Step(22): len = 233216, overlap = 20.25
PHY-3002 : Step(23): len = 219012, overlap = 20.25
PHY-3002 : Step(24): len = 215551, overlap = 20.25
PHY-3002 : Step(25): len = 212499, overlap = 20.25
PHY-3002 : Step(26): len = 206674, overlap = 20.25
PHY-3002 : Step(27): len = 194124, overlap = 20.25
PHY-3002 : Step(28): len = 191470, overlap = 20.25
PHY-3002 : Step(29): len = 186625, overlap = 20.25
PHY-3002 : Step(30): len = 175541, overlap = 20.25
PHY-3002 : Step(31): len = 170525, overlap = 20.25
PHY-3002 : Step(32): len = 168547, overlap = 20.25
PHY-3002 : Step(33): len = 163751, overlap = 20.25
PHY-3002 : Step(34): len = 160212, overlap = 20.25
PHY-3002 : Step(35): len = 156883, overlap = 20.25
PHY-3002 : Step(36): len = 152989, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101392
PHY-3002 : Step(37): len = 153847, overlap = 18
PHY-3002 : Step(38): len = 152166, overlap = 13.5
PHY-3002 : Step(39): len = 150953, overlap = 13.5
PHY-3002 : Step(40): len = 147728, overlap = 13.5
PHY-3002 : Step(41): len = 142641, overlap = 13.5
PHY-3002 : Step(42): len = 141139, overlap = 11.25
PHY-3002 : Step(43): len = 136178, overlap = 9
PHY-3002 : Step(44): len = 131961, overlap = 11.25
PHY-3002 : Step(45): len = 129136, overlap = 9
PHY-3002 : Step(46): len = 127385, overlap = 11.25
PHY-3002 : Step(47): len = 124839, overlap = 11.25
PHY-3002 : Step(48): len = 122562, overlap = 11.25
PHY-3002 : Step(49): len = 121009, overlap = 9
PHY-3002 : Step(50): len = 118559, overlap = 9
PHY-3002 : Step(51): len = 114731, overlap = 11.25
PHY-3002 : Step(52): len = 112511, overlap = 11.25
PHY-3002 : Step(53): len = 111579, overlap = 11.25
PHY-3002 : Step(54): len = 108381, overlap = 13.5
PHY-3002 : Step(55): len = 105529, overlap = 15.75
PHY-3002 : Step(56): len = 103253, overlap = 13.5
PHY-3002 : Step(57): len = 102186, overlap = 13.5
PHY-3002 : Step(58): len = 97082.9, overlap = 13.5
PHY-3002 : Step(59): len = 94008.7, overlap = 13.5
PHY-3002 : Step(60): len = 91099.3, overlap = 11.25
PHY-3002 : Step(61): len = 90149.1, overlap = 11.25
PHY-3002 : Step(62): len = 88918.6, overlap = 11.25
PHY-3002 : Step(63): len = 88393.2, overlap = 13.5
PHY-3002 : Step(64): len = 85405, overlap = 15.75
PHY-3002 : Step(65): len = 83229.5, overlap = 16.375
PHY-3002 : Step(66): len = 81031.5, overlap = 14.3125
PHY-3002 : Step(67): len = 79552.2, overlap = 12.125
PHY-3002 : Step(68): len = 76308.6, overlap = 15.125
PHY-3002 : Step(69): len = 75520.7, overlap = 15.4375
PHY-3002 : Step(70): len = 74052.3, overlap = 17.875
PHY-3002 : Step(71): len = 70659.1, overlap = 14.9375
PHY-3002 : Step(72): len = 69627, overlap = 19.4375
PHY-3002 : Step(73): len = 67398.8, overlap = 14.875
PHY-3002 : Step(74): len = 67190.2, overlap = 15
PHY-3002 : Step(75): len = 66702.4, overlap = 15
PHY-3002 : Step(76): len = 66416.4, overlap = 12.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000202784
PHY-3002 : Step(77): len = 67268.1, overlap = 10.625
PHY-3002 : Step(78): len = 67808.9, overlap = 10.625
PHY-3002 : Step(79): len = 68099.2, overlap = 12.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000405567
PHY-3002 : Step(80): len = 68606.8, overlap = 10.625
PHY-3002 : Step(81): len = 68751, overlap = 10.625
PHY-3002 : Step(82): len = 68516, overlap = 10.625
PHY-3002 : Step(83): len = 68400.1, overlap = 10.625
PHY-3002 : Step(84): len = 68668.9, overlap = 10.625
PHY-3002 : Step(85): len = 68752.6, overlap = 10.5
PHY-3002 : Step(86): len = 68165.5, overlap = 12.9375
PHY-3002 : Step(87): len = 67534.5, overlap = 10.5
PHY-3002 : Step(88): len = 67038.2, overlap = 10.5
PHY-3002 : Step(89): len = 66946.2, overlap = 10.5
PHY-3002 : Step(90): len = 66203.6, overlap = 8.25
PHY-3002 : Step(91): len = 65466.4, overlap = 10.25
PHY-3002 : Step(92): len = 65385.2, overlap = 10.25
PHY-3002 : Step(93): len = 64948.8, overlap = 10.25
PHY-3002 : Step(94): len = 64849.4, overlap = 10.1875
PHY-3002 : Step(95): len = 64755.6, overlap = 10.1875
PHY-3002 : Step(96): len = 64499.9, overlap = 12.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000811135
PHY-3002 : Step(97): len = 64676.3, overlap = 10.25
PHY-3002 : Step(98): len = 64648.5, overlap = 10.25
PHY-3002 : Step(99): len = 64521.2, overlap = 10.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00162227
PHY-3002 : Step(100): len = 64609.5, overlap = 10.0625
PHY-3002 : Step(101): len = 64585.9, overlap = 10.0625
PHY-3001 : Before Legalized: Len = 64585.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008438s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (185.2%)

PHY-3001 : After Legalized: Len = 66429.8, Over = 3.3125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063363s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(102): len = 66321.2, overlap = 16.5625
PHY-3002 : Step(103): len = 65369.4, overlap = 16.4375
PHY-3002 : Step(104): len = 64310, overlap = 15.3438
PHY-3002 : Step(105): len = 63241, overlap = 15.75
PHY-3002 : Step(106): len = 61965.7, overlap = 15.2812
PHY-3002 : Step(107): len = 60956.7, overlap = 14.5
PHY-3002 : Step(108): len = 59337.8, overlap = 14.5312
PHY-3002 : Step(109): len = 58088.2, overlap = 15.5
PHY-3002 : Step(110): len = 56972.7, overlap = 15.9375
PHY-3002 : Step(111): len = 55738.8, overlap = 16.9062
PHY-3002 : Step(112): len = 54453.7, overlap = 17
PHY-3002 : Step(113): len = 52892.7, overlap = 17.5938
PHY-3002 : Step(114): len = 51366.6, overlap = 20.3438
PHY-3002 : Step(115): len = 49889.7, overlap = 21.9688
PHY-3002 : Step(116): len = 48866.5, overlap = 22.9062
PHY-3002 : Step(117): len = 48180.2, overlap = 24.1875
PHY-3002 : Step(118): len = 46837.4, overlap = 25.25
PHY-3002 : Step(119): len = 45940.7, overlap = 25.2188
PHY-3002 : Step(120): len = 45333.4, overlap = 25.625
PHY-3002 : Step(121): len = 44495.8, overlap = 25.875
PHY-3002 : Step(122): len = 44015.6, overlap = 27.7812
PHY-3002 : Step(123): len = 43510.7, overlap = 28.1562
PHY-3002 : Step(124): len = 42932, overlap = 28.9688
PHY-3002 : Step(125): len = 42549, overlap = 28.7812
PHY-3002 : Step(126): len = 42554.2, overlap = 28.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000145001
PHY-3002 : Step(127): len = 42539.2, overlap = 28.2812
PHY-3002 : Step(128): len = 42606.2, overlap = 27.9688
PHY-3002 : Step(129): len = 42535.4, overlap = 27.375
PHY-3002 : Step(130): len = 42539.8, overlap = 26.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000290001
PHY-3002 : Step(131): len = 42483.7, overlap = 26.375
PHY-3002 : Step(132): len = 42624.7, overlap = 23.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059432s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (105.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.94301e-05
PHY-3002 : Step(133): len = 42675.6, overlap = 61.1875
PHY-3002 : Step(134): len = 44268.8, overlap = 60.375
PHY-3002 : Step(135): len = 45352.4, overlap = 46.7812
PHY-3002 : Step(136): len = 45306.1, overlap = 50.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011886
PHY-3002 : Step(137): len = 45271.5, overlap = 51.1562
PHY-3002 : Step(138): len = 46004.7, overlap = 45.7812
PHY-3002 : Step(139): len = 46313.6, overlap = 43.3438
PHY-3002 : Step(140): len = 46492.4, overlap = 37.0938
PHY-3002 : Step(141): len = 46388.7, overlap = 36.9375
PHY-3002 : Step(142): len = 46329.9, overlap = 36.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00023772
PHY-3002 : Step(143): len = 46336.4, overlap = 36.0625
PHY-3002 : Step(144): len = 47288.2, overlap = 33.0625
PHY-3002 : Step(145): len = 47834.5, overlap = 32.7812
PHY-3002 : Step(146): len = 47854.2, overlap = 32.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7427, tnet num: 2080, tinst num: 1561, tnode num: 10528, tedge num: 12541.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.69 peak overflow 2.88
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49712, over cnt = 234(0%), over = 1024, worst = 19
PHY-1001 : End global iterations;  0.073680s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.8%)

PHY-1001 : Congestion index: top1 = 40.58, top5 = 23.34, top10 = 14.80, top15 = 10.62.
PHY-1001 : End incremental global routing;  0.127892s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068768s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.225506s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.0%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1608/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49712, over cnt = 234(0%), over = 1024, worst = 19
PHY-1002 : len = 57104, over cnt = 143(0%), over = 358, worst = 17
PHY-1002 : len = 61232, over cnt = 36(0%), over = 41, worst = 4
PHY-1002 : len = 61808, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 62080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091830s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (170.2%)

PHY-1001 : Congestion index: top1 = 35.69, top5 = 23.51, top10 = 16.77, top15 = 12.47.
OPT-1001 : End congestion update;  0.134645s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (150.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058618s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196383s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (135.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.658686s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (109.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 663 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1038/1343 primitive instances ...
PHY-3001 : End packing;  0.046863s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-1001 : 1355 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 785 instances, 736 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 47812.8, Over = 61.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6213, tnet num: 1896, tinst num: 785, tnode num: 8452, tedge num: 10933.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283165s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.28421e-05
PHY-3002 : Step(147): len = 47252.1, overlap = 63.5
PHY-3002 : Step(148): len = 46752.5, overlap = 63.5
PHY-3002 : Step(149): len = 46571.7, overlap = 64
PHY-3002 : Step(150): len = 46731.6, overlap = 63.75
PHY-3002 : Step(151): len = 46391.4, overlap = 60.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.56842e-05
PHY-3002 : Step(152): len = 46620, overlap = 57
PHY-3002 : Step(153): len = 47107.3, overlap = 55.25
PHY-3002 : Step(154): len = 47491.3, overlap = 53
PHY-3002 : Step(155): len = 47350.1, overlap = 52.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131368
PHY-3002 : Step(156): len = 47914.9, overlap = 51.25
PHY-3002 : Step(157): len = 48336.2, overlap = 50
PHY-3002 : Step(158): len = 48885.7, overlap = 48.75
PHY-3002 : Step(159): len = 49077.1, overlap = 50.5
PHY-3001 : Before Legalized: Len = 49077.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071877s wall, 0.031250s user + 0.125000s system = 0.156250s CPU (217.4%)

PHY-3001 : After Legalized: Len = 63261.6, Over = 0
PHY-3001 : Trial Legalized: Len = 63261.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048905s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00154302
PHY-3002 : Step(160): len = 59406.2, overlap = 7.75
PHY-3002 : Step(161): len = 57774.7, overlap = 8
PHY-3002 : Step(162): len = 55914.3, overlap = 12
PHY-3002 : Step(163): len = 54474.5, overlap = 16
PHY-3002 : Step(164): len = 53943, overlap = 19
PHY-3002 : Step(165): len = 53336.5, overlap = 21.25
PHY-3002 : Step(166): len = 52862.8, overlap = 22.75
PHY-3002 : Step(167): len = 52720.5, overlap = 23.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00308603
PHY-3002 : Step(168): len = 52795.5, overlap = 22.5
PHY-3002 : Step(169): len = 52791.9, overlap = 21.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00617206
PHY-3002 : Step(170): len = 52856.8, overlap = 21.5
PHY-3002 : Step(171): len = 52856.8, overlap = 21.5
PHY-3001 : Before Legalized: Len = 52856.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004994s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (312.9%)

PHY-3001 : After Legalized: Len = 56888.4, Over = 0
PHY-3001 : Legalized: Len = 56888.4, Over = 0
PHY-3001 : Spreading special nets. 13 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005297s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 18 instances has been re-located, deltaX = 4, deltaY = 13, maxDist = 1.
PHY-3001 : Final: Len = 57144.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6213, tnet num: 1896, tinst num: 785, tnode num: 8452, tedge num: 10933.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 37/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 62272, over cnt = 148(0%), over = 247, worst = 6
PHY-1002 : len = 63552, over cnt = 82(0%), over = 105, worst = 4
PHY-1002 : len = 64576, over cnt = 19(0%), over = 24, worst = 3
PHY-1002 : len = 64944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108072s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (159.0%)

PHY-1001 : Congestion index: top1 = 32.20, top5 = 22.61, top10 = 16.98, top15 = 13.13.
PHY-1001 : End incremental global routing;  0.159886s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (136.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056829s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.245849s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (120.8%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1684/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005249s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.20, top5 = 22.61, top10 = 16.98, top15 = 13.13.
OPT-1001 : End congestion update;  0.049199s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046227s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 745 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 785 instances, 736 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 57208.8, Over = 0
PHY-3001 : End spreading;  0.005014s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (311.6%)

PHY-3001 : Final: Len = 57208.8, Over = 0
PHY-3001 : End incremental legalization;  0.035171s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.143872s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.6%)

OPT-1001 : Current memory(MB): used = 217, reserve = 184, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046605s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1675/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65000, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 65000, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 65032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.023391s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 32.18, top5 = 22.61, top10 = 17.00, top15 = 13.14.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047232s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.836169s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (110.2%)

RUN-1003 : finish command "place" in  5.266219s wall, 7.062500s user + 4.000000s system = 11.062500s CPU (210.1%)

RUN-1004 : used memory is 198 MB, reserved memory is 164 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-1001 : 1355 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6213, tnet num: 1896, tinst num: 785, tnode num: 8452, tedge num: 10933.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61776, over cnt = 148(0%), over = 242, worst = 7
PHY-1002 : len = 63224, over cnt = 72(0%), over = 83, worst = 3
PHY-1002 : len = 64192, over cnt = 11(0%), over = 13, worst = 2
PHY-1002 : len = 64368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126335s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (136.0%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 22.35, top10 = 16.82, top15 = 13.00.
PHY-1001 : End global routing;  0.176500s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (115.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 202, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.126728s wall, 3.046875s user + 0.078125s system = 3.125000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.016583s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 525, reserve = 499, peak = 525.
PHY-1001 : End phase 1; 1.022283s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 171768, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.163142s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (158.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.758   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.037  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.332476s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End phase 2; 1.495715s wall, 2.140625s user + 0.031250s system = 2.171875s CPU (145.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 171768, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015354s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 171616, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027174s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (172.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 171712, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021848s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (143.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.758   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.037  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.333946s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.166267s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.4%)

PHY-1001 : Current memory(MB): used = 541, reserve = 513, peak = 541.
PHY-1001 : End phase 3; 0.687084s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (102.3%)

PHY-1003 : Routed, final wirelength = 171712
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.009325s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.511964s wall, 7.078125s user + 0.109375s system = 7.187500s CPU (110.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6213, tnet num: 1896, tinst num: 785, tnode num: 8452, tedge num: 10933.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/crc_data_b1[1]_syn_8.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_103.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_103.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_106.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_106.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_109.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_109.mi[1] slack -2909ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_91.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_94.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_94.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_96.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_99.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_99.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[3]_syn_24.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6337, tnet num: 1958, tinst num: 847, tnode num: 8576, tedge num: 11057.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -252ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -697ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[3]_syn_24_mi[0] slack -907ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_106_mi[1] slack -861ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_103_mi[0] slack -488ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_106_mi[0] slack -602ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_103_mi[1] slack -749ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_109_mi[0] slack -351ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_109_mi[1] slack -722ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[1] slack -714ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[0] slack -245ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[1] slack -209ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_96_mi[1] slack -437ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_99_mi[1] slack -234ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_99_mi[0] slack -582ps
RUN-1001 : End hold fix;  2.998510s wall, 3.000000s user + 0.218750s system = 3.218750s CPU (107.3%)

RUN-1003 : finish command "route" in  10.008219s wall, 10.546875s user + 0.375000s system = 10.921875s CPU (109.1%)

RUN-1004 : used memory is 532 MB, reserved memory is 506 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      885   out of  19600    4.52%
#reg                     1019   out of  19600    5.20%
#le                      1548
  #lut only               529   out of   1548   34.17%
  #reg only               663   out of   1548   42.83%
  #lut&reg                356   out of   1548   23.00%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         462
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1548   |695     |190     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1042   |287     |126     |830     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |21      |4       |22      |0       |0       |
|    demodu                  |Demodulation                                     |459    |109     |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |32      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |9       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |9       |0       |14      |0       |0       |
|    integ                   |Integration                                      |143    |26      |15      |115     |0       |0       |
|    modu                    |Modulation                                       |65     |40      |7       |63      |0       |1       |
|    rs422                   |Rs422Output                                      |326    |75      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |170    |129     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |108    |83      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |312    |267     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1378  
    #2          2       304   
    #3          3       107   
    #4          4        22   
    #5        5-10       74   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6337, tnet num: 1958, tinst num: 847, tnode num: 8576, tedge num: 11057.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1958 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 847
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1960, pip num: 14222
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1258 valid insts, and 38262 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.975146s wall, 16.500000s user + 0.171875s system = 16.671875s CPU (560.4%)

RUN-1004 : used memory is 545 MB, reserved memory is 516 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250723_184713.log"

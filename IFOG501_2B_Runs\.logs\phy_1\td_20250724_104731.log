============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 24 10:47:31 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1579 instances
RUN-0007 : 368 luts, 964 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2108 nets
RUN-1001 : 1537 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1577 instances, 368 luts, 964 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7513, tnet num: 2106, tinst num: 1577, tnode num: 10670, tedge num: 12730.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.255387s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 533007
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1577.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461463, overlap = 18
PHY-3002 : Step(2): len = 442089, overlap = 13.5
PHY-3002 : Step(3): len = 430343, overlap = 20.25
PHY-3002 : Step(4): len = 419696, overlap = 13.5
PHY-3002 : Step(5): len = 406863, overlap = 18
PHY-3002 : Step(6): len = 391594, overlap = 20.25
PHY-3002 : Step(7): len = 381889, overlap = 11.25
PHY-3002 : Step(8): len = 374080, overlap = 13.5
PHY-3002 : Step(9): len = 361485, overlap = 13.5
PHY-3002 : Step(10): len = 351956, overlap = 13.5
PHY-3002 : Step(11): len = 346089, overlap = 13.5
PHY-3002 : Step(12): len = 333190, overlap = 11.25
PHY-3002 : Step(13): len = 323456, overlap = 11.25
PHY-3002 : Step(14): len = 318697, overlap = 11.25
PHY-3002 : Step(15): len = 307939, overlap = 13.5
PHY-3002 : Step(16): len = 298031, overlap = 13.5
PHY-3002 : Step(17): len = 294335, overlap = 13.5
PHY-3002 : Step(18): len = 284525, overlap = 13.5
PHY-3002 : Step(19): len = 276204, overlap = 11.25
PHY-3002 : Step(20): len = 272291, overlap = 11.25
PHY-3002 : Step(21): len = 266948, overlap = 11.25
PHY-3002 : Step(22): len = 253813, overlap = 11.25
PHY-3002 : Step(23): len = 248656, overlap = 11.25
PHY-3002 : Step(24): len = 246049, overlap = 11.25
PHY-3002 : Step(25): len = 237820, overlap = 18
PHY-3002 : Step(26): len = 226274, overlap = 20.25
PHY-3002 : Step(27): len = 222881, overlap = 20.25
PHY-3002 : Step(28): len = 219217, overlap = 20.25
PHY-3002 : Step(29): len = 173573, overlap = 20.25
PHY-3002 : Step(30): len = 163137, overlap = 20.25
PHY-3002 : Step(31): len = 161515, overlap = 20.25
PHY-3002 : Step(32): len = 151252, overlap = 20.25
PHY-3002 : Step(33): len = 142115, overlap = 20.25
PHY-3002 : Step(34): len = 140446, overlap = 20.25
PHY-3002 : Step(35): len = 138029, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000122151
PHY-3002 : Step(36): len = 138711, overlap = 15.75
PHY-3002 : Step(37): len = 137339, overlap = 13.5
PHY-3002 : Step(38): len = 136550, overlap = 11.25
PHY-3002 : Step(39): len = 131719, overlap = 9
PHY-3002 : Step(40): len = 127241, overlap = 9
PHY-3002 : Step(41): len = 123877, overlap = 11.25
PHY-3002 : Step(42): len = 121504, overlap = 11.25
PHY-3002 : Step(43): len = 119472, overlap = 9
PHY-3002 : Step(44): len = 118816, overlap = 9
PHY-3002 : Step(45): len = 115711, overlap = 9
PHY-3002 : Step(46): len = 113259, overlap = 9
PHY-3002 : Step(47): len = 110940, overlap = 9
PHY-3002 : Step(48): len = 110307, overlap = 9
PHY-3002 : Step(49): len = 105619, overlap = 11.25
PHY-3002 : Step(50): len = 103278, overlap = 13.5
PHY-3002 : Step(51): len = 101587, overlap = 13.5
PHY-3002 : Step(52): len = 101037, overlap = 11.25
PHY-3002 : Step(53): len = 98241, overlap = 9
PHY-3002 : Step(54): len = 96969.3, overlap = 13.5
PHY-3002 : Step(55): len = 95200.4, overlap = 13.5
PHY-3002 : Step(56): len = 93007.7, overlap = 13.5
PHY-3002 : Step(57): len = 90931.2, overlap = 15.75
PHY-3002 : Step(58): len = 89511, overlap = 15.75
PHY-3002 : Step(59): len = 88441.4, overlap = 15.75
PHY-3002 : Step(60): len = 86968.3, overlap = 18
PHY-3002 : Step(61): len = 85422.9, overlap = 15.75
PHY-3002 : Step(62): len = 83749.1, overlap = 15.75
PHY-3002 : Step(63): len = 81186.9, overlap = 16.0625
PHY-3002 : Step(64): len = 80628.5, overlap = 16.6875
PHY-3002 : Step(65): len = 78862.9, overlap = 16.9375
PHY-3002 : Step(66): len = 77658.8, overlap = 19.5625
PHY-3002 : Step(67): len = 74889.9, overlap = 17.3125
PHY-3002 : Step(68): len = 74694.3, overlap = 17.5
PHY-3002 : Step(69): len = 72845.2, overlap = 17.5625
PHY-3002 : Step(70): len = 70654.2, overlap = 20.4375
PHY-3002 : Step(71): len = 69682.3, overlap = 18.5
PHY-3002 : Step(72): len = 68575, overlap = 18.625
PHY-3002 : Step(73): len = 68459.4, overlap = 18.6875
PHY-3002 : Step(74): len = 67751.3, overlap = 20.625
PHY-3002 : Step(75): len = 66461.4, overlap = 21.25
PHY-3002 : Step(76): len = 66113.2, overlap = 18.9375
PHY-3002 : Step(77): len = 65145.9, overlap = 18.875
PHY-3002 : Step(78): len = 63729, overlap = 18.75
PHY-3002 : Step(79): len = 63084.9, overlap = 18.4375
PHY-3002 : Step(80): len = 62668.8, overlap = 16.1875
PHY-3002 : Step(81): len = 62422.4, overlap = 16.1875
PHY-3002 : Step(82): len = 62569.7, overlap = 18.4375
PHY-3002 : Step(83): len = 62380.7, overlap = 18.4375
PHY-3002 : Step(84): len = 62280.1, overlap = 18.4375
PHY-3002 : Step(85): len = 61866.3, overlap = 18.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000244303
PHY-3002 : Step(86): len = 62189.1, overlap = 18.3125
PHY-3002 : Step(87): len = 62241.1, overlap = 18.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000488606
PHY-3002 : Step(88): len = 62345.8, overlap = 18.125
PHY-3002 : Step(89): len = 62422.6, overlap = 18.0625
PHY-3001 : Before Legalized: Len = 62422.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006800s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 66363.8, Over = 2.3125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064485s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 66561.1, overlap = 8.96875
PHY-3002 : Step(91): len = 65436.2, overlap = 7.875
PHY-3002 : Step(92): len = 64323, overlap = 7.75
PHY-3002 : Step(93): len = 63582.3, overlap = 7.375
PHY-3002 : Step(94): len = 62704.6, overlap = 7.375
PHY-3002 : Step(95): len = 61092.1, overlap = 7.0625
PHY-3002 : Step(96): len = 60194.9, overlap = 6.875
PHY-3002 : Step(97): len = 59174.9, overlap = 7.59375
PHY-3002 : Step(98): len = 58556.7, overlap = 7.90625
PHY-3002 : Step(99): len = 57748.9, overlap = 7.8125
PHY-3002 : Step(100): len = 56782.5, overlap = 7.84375
PHY-3002 : Step(101): len = 56226.5, overlap = 8.71875
PHY-3002 : Step(102): len = 55528.5, overlap = 8.375
PHY-3002 : Step(103): len = 55116.2, overlap = 10.9688
PHY-3002 : Step(104): len = 54018, overlap = 13.4375
PHY-3002 : Step(105): len = 53374, overlap = 13.625
PHY-3002 : Step(106): len = 52886.8, overlap = 16.6562
PHY-3002 : Step(107): len = 51941.3, overlap = 17.6875
PHY-3002 : Step(108): len = 51405.2, overlap = 17.6875
PHY-3002 : Step(109): len = 51284.1, overlap = 18.5
PHY-3002 : Step(110): len = 50804, overlap = 19.5312
PHY-3002 : Step(111): len = 50479, overlap = 21.7812
PHY-3002 : Step(112): len = 50088.8, overlap = 20.7188
PHY-3002 : Step(113): len = 49688.5, overlap = 20.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00054527
PHY-3002 : Step(114): len = 49646.7, overlap = 20.5938
PHY-3002 : Step(115): len = 49639.9, overlap = 20.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00109054
PHY-3002 : Step(116): len = 49501.5, overlap = 20.7188
PHY-3002 : Step(117): len = 49517.4, overlap = 20.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.090130s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (104.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.51156e-05
PHY-3002 : Step(118): len = 49559.2, overlap = 63.875
PHY-3002 : Step(119): len = 49921.4, overlap = 63.9062
PHY-3002 : Step(120): len = 50421.9, overlap = 58.4375
PHY-3002 : Step(121): len = 50700.7, overlap = 50.375
PHY-3002 : Step(122): len = 50453.7, overlap = 50.5625
PHY-3002 : Step(123): len = 50473.3, overlap = 50.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000110231
PHY-3002 : Step(124): len = 50631.9, overlap = 49.2812
PHY-3002 : Step(125): len = 50848.3, overlap = 48.625
PHY-3002 : Step(126): len = 51179.6, overlap = 46.3438
PHY-3002 : Step(127): len = 51462.2, overlap = 44.1875
PHY-3002 : Step(128): len = 51522.9, overlap = 43.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000220463
PHY-3002 : Step(129): len = 51911.7, overlap = 43.1562
PHY-3002 : Step(130): len = 52947.2, overlap = 42.3438
PHY-3002 : Step(131): len = 53540.4, overlap = 42.1875
PHY-3002 : Step(132): len = 53610.9, overlap = 41.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000440925
PHY-3002 : Step(133): len = 53746.8, overlap = 41.3438
PHY-3002 : Step(134): len = 54102.1, overlap = 38.1875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000829232
PHY-3002 : Step(135): len = 54801.7, overlap = 37.375
PHY-3002 : Step(136): len = 55022.3, overlap = 36.7188
PHY-3002 : Step(137): len = 55388.6, overlap = 34.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7513, tnet num: 2106, tinst num: 1577, tnode num: 10670, tedge num: 12730.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 99.59 peak overflow 3.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2108.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59336, over cnt = 239(0%), over = 1039, worst = 14
PHY-1001 : End global iterations;  0.083415s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (131.1%)

PHY-1001 : Congestion index: top1 = 45.26, top5 = 26.30, top10 = 17.04, top15 = 12.12.
PHY-1001 : End incremental global routing;  0.134714s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (127.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069548s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.237530s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (111.8%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1636/2108.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59336, over cnt = 239(0%), over = 1039, worst = 14
PHY-1002 : len = 65544, over cnt = 167(0%), over = 490, worst = 13
PHY-1002 : len = 70128, over cnt = 48(0%), over = 130, worst = 12
PHY-1002 : len = 71312, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 71600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095129s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (115.0%)

PHY-1001 : Congestion index: top1 = 38.19, top5 = 25.40, top10 = 18.54, top15 = 13.81.
OPT-1001 : End congestion update;  0.139453s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063146s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (123.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206119s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (106.1%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.693319s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (103.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 106 SEQ with LUT/SLICE
SYN-4006 : 91 single LUT's are left
SYN-4006 : 669 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1037/1348 primitive instances ...
PHY-3001 : End packing;  0.047484s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 791 instances
RUN-1001 : 370 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 789 instances, 740 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55469.4, Over = 64
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6280, tnet num: 1925, tinst num: 789, tnode num: 8558, tedge num: 11089.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.301032s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.09579e-05
PHY-3002 : Step(138): len = 54693, overlap = 66.75
PHY-3002 : Step(139): len = 54160.1, overlap = 68.25
PHY-3002 : Step(140): len = 53668.9, overlap = 67.75
PHY-3002 : Step(141): len = 53513.3, overlap = 64.75
PHY-3002 : Step(142): len = 53239.2, overlap = 65.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.19157e-05
PHY-3002 : Step(143): len = 53832, overlap = 63.75
PHY-3002 : Step(144): len = 54104.3, overlap = 60
PHY-3002 : Step(145): len = 54871.4, overlap = 59.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000163831
PHY-3002 : Step(146): len = 55480.7, overlap = 56.75
PHY-3002 : Step(147): len = 56231, overlap = 54.5
PHY-3002 : Step(148): len = 56993.2, overlap = 48.25
PHY-3002 : Step(149): len = 57134.1, overlap = 47.25
PHY-3001 : Before Legalized: Len = 57134.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.104136s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (135.0%)

PHY-3001 : After Legalized: Len = 69787.1, Over = 0
PHY-3001 : Trial Legalized: Len = 69787.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052512s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0019458
PHY-3002 : Step(150): len = 66932.9, overlap = 3.75
PHY-3002 : Step(151): len = 64871.6, overlap = 12.25
PHY-3002 : Step(152): len = 63034, overlap = 13.75
PHY-3002 : Step(153): len = 61930.2, overlap = 15.25
PHY-3002 : Step(154): len = 60574.6, overlap = 17.25
PHY-3002 : Step(155): len = 59645.3, overlap = 22.5
PHY-3002 : Step(156): len = 59338.6, overlap = 22.75
PHY-3002 : Step(157): len = 59033.8, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0038916
PHY-3002 : Step(158): len = 59203.9, overlap = 24
PHY-3002 : Step(159): len = 59178.4, overlap = 23.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0077832
PHY-3002 : Step(160): len = 59263.6, overlap = 23.5
PHY-3002 : Step(161): len = 59365.7, overlap = 23.25
PHY-3001 : Before Legalized: Len = 59365.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005285s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63868.4, Over = 0
PHY-3001 : Legalized: Len = 63868.4, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006361s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 2, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63964.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6280, tnet num: 1925, tinst num: 789, tnode num: 8558, tedge num: 11089.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 28/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70552, over cnt = 162(0%), over = 246, worst = 6
PHY-1002 : len = 71480, over cnt = 116(0%), over = 140, worst = 4
PHY-1002 : len = 73008, over cnt = 7(0%), over = 9, worst = 3
PHY-1002 : len = 73144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131078s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.4%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.63, top10 = 17.74, top15 = 13.91.
PHY-1001 : End incremental global routing;  0.185294s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (92.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060217s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277332s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1710/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006979s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (223.9%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.63, top10 = 17.74, top15 = 13.91.
OPT-1001 : End congestion update;  0.059637s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053016s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 749 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 789 instances, 740 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64008.4, Over = 0
PHY-3001 : End spreading;  0.004862s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64008.4, Over = 0
PHY-3001 : End incremental legalization;  0.039129s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (159.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.168399s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (167.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071853s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1698/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012996s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.59, top10 = 17.72, top15 = 13.90.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066801s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (70.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.923907s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (121.8%)

RUN-1003 : finish command "place" in  5.462605s wall, 7.421875s user + 3.062500s system = 10.484375s CPU (191.9%)

RUN-1004 : used memory is 198 MB, reserved memory is 165 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 791 instances
RUN-1001 : 370 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6280, tnet num: 1925, tinst num: 789, tnode num: 8558, tedge num: 11089.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 370 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70032, over cnt = 157(0%), over = 244, worst = 6
PHY-1002 : len = 71016, over cnt = 109(0%), over = 134, worst = 4
PHY-1002 : len = 72368, over cnt = 19(0%), over = 25, worst = 3
PHY-1002 : len = 72648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132709s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (141.3%)

PHY-1001 : Congestion index: top1 = 31.19, top5 = 22.58, top10 = 17.65, top15 = 13.81.
PHY-1001 : End global routing;  0.186416s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (125.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 468, peak = 496.
PHY-1001 : End build detailed router design. 3.433038s wall, 3.203125s user + 0.078125s system = 3.281250s CPU (95.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31400, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.250617s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 528, reserve = 502, peak = 528.
PHY-1001 : End phase 1; 1.258617s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (98.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181312, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 503, peak = 531.
PHY-1001 : End initial routed; 1.960184s wall, 2.687500s user + 0.140625s system = 2.828125s CPU (144.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.558  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363526s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 504, peak = 532.
PHY-1001 : End phase 2; 2.323803s wall, 3.046875s user + 0.140625s system = 3.187500s CPU (137.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181312, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018338s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181312, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.037799s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (124.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181304, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.033381s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (46.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.024909s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (250.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.558  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366891s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 10 nets
PHY-1001 : End commit to database; 0.177751s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 0.813375s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (103.7%)

PHY-1003 : Routed, final wirelength = 181304
PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End export database. 0.012581s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (124.2%)

PHY-1001 : End detail routing;  8.037789s wall, 8.484375s user + 0.265625s system = 8.750000s CPU (108.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6280, tnet num: 1925, tinst num: 789, tnode num: 8558, tedge num: 11089.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_22.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2767ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_89.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_89.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_92.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_92.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_94.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/tx_data_dy_b[1]_syn_22.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[4]_syn_21.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/tx_data_dy_b[6]_syn_30.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6398, tnet num: 1984, tinst num: 848, tnode num: 8676, tedge num: 11207.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -56ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -536ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -264ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -805ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -864ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[6]_syn_30_mi[0] slack -121ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[1]_syn_22_mi[0] slack -246ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -170ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -726ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_92_mi[1] slack -454ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_92_mi[0] slack -167ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[0] slack -145ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[4]_syn_21_mi[0] slack -272ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -398ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -239ps
RUN-1001 : End hold fix;  3.279703s wall, 3.375000s user + 0.281250s system = 3.656250s CPU (111.5%)

RUN-1003 : finish command "route" in  11.859288s wall, 12.453125s user + 0.546875s system = 13.000000s CPU (109.6%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      882   out of  19600    4.50%
#reg                     1049   out of  19600    5.35%
#le                      1551
  #lut only               502   out of   1551   32.37%
  #reg only               669   out of   1551   43.13%
  #lut&reg                380   out of   1551   24.50%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1551   |686     |196     |1082    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1054   |283     |132     |860     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |23      |0       |0       |
|    demodu                  |Demodulation                                     |454    |98      |45      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |59     |34      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |7       |0       |12      |0       |0       |
|    integ                   |Integration                                      |139    |24      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |100    |60      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |66      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |15      |6       |16      |0       |0       |
|  u_uart                    |UART_Control                                     |169    |132     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |20      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |108    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |304    |259     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1380  
    #2          2       327   
    #3          3       110   
    #4          4        26   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6398, tnet num: 1984, tinst num: 848, tnode num: 8676, tedge num: 11207.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 848
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1986, pip num: 14716
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1359 valid insts, and 39317 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.438846s wall, 18.375000s user + 0.078125s system = 18.453125s CPU (536.6%)

RUN-1004 : used memory is 520 MB, reserved memory is 493 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250724_104731.log"

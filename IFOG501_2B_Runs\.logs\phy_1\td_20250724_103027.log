============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 24 10:30:27 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1562 instances
RUN-0007 : 375 luts, 947 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2081 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1560 instances, 375 luts, 947 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7416, tnet num: 2079, tinst num: 1560, tnode num: 10517, tedge num: 12521.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.249679s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 517095
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1560.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 446627, overlap = 20.25
PHY-3002 : Step(2): len = 422100, overlap = 13.5
PHY-3002 : Step(3): len = 410400, overlap = 20.25
PHY-3002 : Step(4): len = 399585, overlap = 13.5
PHY-3002 : Step(5): len = 381057, overlap = 18
PHY-3002 : Step(6): len = 353668, overlap = 11.25
PHY-3002 : Step(7): len = 347744, overlap = 13.5
PHY-3002 : Step(8): len = 343356, overlap = 13.5
PHY-3002 : Step(9): len = 324495, overlap = 13.5
PHY-3002 : Step(10): len = 317706, overlap = 13.5
PHY-3002 : Step(11): len = 312272, overlap = 13.5
PHY-3002 : Step(12): len = 303743, overlap = 15.75
PHY-3002 : Step(13): len = 293944, overlap = 15.75
PHY-3002 : Step(14): len = 291121, overlap = 15.75
PHY-3002 : Step(15): len = 280134, overlap = 15.75
PHY-3002 : Step(16): len = 267487, overlap = 15.75
PHY-3002 : Step(17): len = 262836, overlap = 15.75
PHY-3002 : Step(18): len = 258716, overlap = 13.5
PHY-3002 : Step(19): len = 223443, overlap = 20.25
PHY-3002 : Step(20): len = 217472, overlap = 20.25
PHY-3002 : Step(21): len = 213963, overlap = 20.25
PHY-3002 : Step(22): len = 208692, overlap = 20.25
PHY-3002 : Step(23): len = 200305, overlap = 20.25
PHY-3002 : Step(24): len = 197017, overlap = 20.25
PHY-3002 : Step(25): len = 193044, overlap = 20.25
PHY-3002 : Step(26): len = 189019, overlap = 20.25
PHY-3002 : Step(27): len = 183648, overlap = 20.25
PHY-3002 : Step(28): len = 180092, overlap = 20.25
PHY-3002 : Step(29): len = 173549, overlap = 20.25
PHY-3002 : Step(30): len = 169795, overlap = 20.25
PHY-3002 : Step(31): len = 166495, overlap = 20.25
PHY-3002 : Step(32): len = 156228, overlap = 20.25
PHY-3002 : Step(33): len = 147216, overlap = 20.25
PHY-3002 : Step(34): len = 145920, overlap = 20.25
PHY-3002 : Step(35): len = 137642, overlap = 20.25
PHY-3002 : Step(36): len = 123987, overlap = 20.25
PHY-3002 : Step(37): len = 120620, overlap = 20.25
PHY-3002 : Step(38): len = 118652, overlap = 20.25
PHY-3002 : Step(39): len = 114632, overlap = 20.25
PHY-3002 : Step(40): len = 113564, overlap = 20.25
PHY-3002 : Step(41): len = 110480, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.09013e-05
PHY-3002 : Step(42): len = 111131, overlap = 15.75
PHY-3002 : Step(43): len = 110210, overlap = 13.5
PHY-3002 : Step(44): len = 108946, overlap = 18
PHY-3002 : Step(45): len = 107798, overlap = 15.75
PHY-3002 : Step(46): len = 104142, overlap = 13.5
PHY-3002 : Step(47): len = 101485, overlap = 13.5
PHY-3002 : Step(48): len = 98488.8, overlap = 11.25
PHY-3002 : Step(49): len = 98011.5, overlap = 11.25
PHY-3002 : Step(50): len = 96173, overlap = 11.25
PHY-3002 : Step(51): len = 90360.4, overlap = 15.75
PHY-3002 : Step(52): len = 88253.1, overlap = 13.5
PHY-3002 : Step(53): len = 87173.5, overlap = 13.5
PHY-3002 : Step(54): len = 86365.9, overlap = 13.5
PHY-3002 : Step(55): len = 86095, overlap = 15.75
PHY-3002 : Step(56): len = 84656.8, overlap = 11.25
PHY-3002 : Step(57): len = 83125.7, overlap = 13.5
PHY-3002 : Step(58): len = 78360.1, overlap = 11.25
PHY-3002 : Step(59): len = 77386.5, overlap = 11.25
PHY-3002 : Step(60): len = 76222.4, overlap = 11.25
PHY-3002 : Step(61): len = 74911.8, overlap = 17
PHY-3002 : Step(62): len = 74990.8, overlap = 14.8125
PHY-3002 : Step(63): len = 74641.4, overlap = 14.9375
PHY-3002 : Step(64): len = 73438, overlap = 15
PHY-3002 : Step(65): len = 73111.6, overlap = 15
PHY-3002 : Step(66): len = 72139, overlap = 17.375
PHY-3002 : Step(67): len = 70471, overlap = 19.9375
PHY-3002 : Step(68): len = 70266.7, overlap = 18.0625
PHY-3002 : Step(69): len = 69433, overlap = 18.0625
PHY-3002 : Step(70): len = 68077.2, overlap = 18.25
PHY-3002 : Step(71): len = 64990.6, overlap = 18.3125
PHY-3002 : Step(72): len = 63922.6, overlap = 16.1875
PHY-3002 : Step(73): len = 63824.7, overlap = 16.25
PHY-3002 : Step(74): len = 63554.7, overlap = 16.0625
PHY-3002 : Step(75): len = 62799.7, overlap = 15.9375
PHY-3002 : Step(76): len = 62408.7, overlap = 15.9375
PHY-3002 : Step(77): len = 62438.6, overlap = 13.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000161803
PHY-3002 : Step(78): len = 62892, overlap = 13.6875
PHY-3002 : Step(79): len = 62936.7, overlap = 13.6875
PHY-3002 : Step(80): len = 62877.2, overlap = 15.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000323605
PHY-3002 : Step(81): len = 62993, overlap = 15.9375
PHY-3002 : Step(82): len = 63054.1, overlap = 15.9375
PHY-3001 : Before Legalized: Len = 63054.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006812s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 66212.6, Over = 2.4375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059331s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(83): len = 66098.6, overlap = 11.9688
PHY-3002 : Step(84): len = 65380.4, overlap = 7.71875
PHY-3002 : Step(85): len = 63694.6, overlap = 7.8125
PHY-3002 : Step(86): len = 62742.3, overlap = 8.125
PHY-3002 : Step(87): len = 61530.7, overlap = 7.96875
PHY-3002 : Step(88): len = 59935.8, overlap = 8.75
PHY-3002 : Step(89): len = 59312.1, overlap = 8.75
PHY-3002 : Step(90): len = 58768.2, overlap = 8.9375
PHY-3002 : Step(91): len = 58056, overlap = 8.90625
PHY-3002 : Step(92): len = 57160.9, overlap = 9.21875
PHY-3002 : Step(93): len = 56130.9, overlap = 7.78125
PHY-3002 : Step(94): len = 55419.1, overlap = 8.0625
PHY-3002 : Step(95): len = 54812, overlap = 8
PHY-3002 : Step(96): len = 54475.4, overlap = 7.8125
PHY-3002 : Step(97): len = 53558.9, overlap = 7.4375
PHY-3002 : Step(98): len = 53102.1, overlap = 9.8125
PHY-3002 : Step(99): len = 52555.1, overlap = 10.25
PHY-3002 : Step(100): len = 51867.3, overlap = 10.8125
PHY-3002 : Step(101): len = 51480.8, overlap = 11.5
PHY-3002 : Step(102): len = 50799.3, overlap = 12.4375
PHY-3002 : Step(103): len = 50008.5, overlap = 10.0625
PHY-3002 : Step(104): len = 49648, overlap = 11.0625
PHY-3002 : Step(105): len = 49177.9, overlap = 13.0312
PHY-3002 : Step(106): len = 48767.7, overlap = 13.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000414402
PHY-3002 : Step(107): len = 48631.8, overlap = 13.8438
PHY-3002 : Step(108): len = 48785.4, overlap = 13.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000828804
PHY-3002 : Step(109): len = 48314.1, overlap = 13.8125
PHY-3002 : Step(110): len = 48393.4, overlap = 13.4375
PHY-3002 : Step(111): len = 48322.7, overlap = 13.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058450s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.68839e-05
PHY-3002 : Step(112): len = 48338.3, overlap = 53.9062
PHY-3002 : Step(113): len = 50629.4, overlap = 48.9688
PHY-3002 : Step(114): len = 51776.5, overlap = 46.7812
PHY-3002 : Step(115): len = 51583.5, overlap = 45.7188
PHY-3002 : Step(116): len = 51064, overlap = 45.1875
PHY-3002 : Step(117): len = 50916.9, overlap = 43
PHY-3002 : Step(118): len = 50485.4, overlap = 38.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133768
PHY-3002 : Step(119): len = 50733.8, overlap = 37.5938
PHY-3002 : Step(120): len = 51532.3, overlap = 37.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000267535
PHY-3002 : Step(121): len = 51630.2, overlap = 37.75
PHY-3002 : Step(122): len = 52044.6, overlap = 37.4375
PHY-3002 : Step(123): len = 53102.8, overlap = 37.5312
PHY-3002 : Step(124): len = 53625.2, overlap = 35.625
PHY-3002 : Step(125): len = 53476.3, overlap = 35.3438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000535071
PHY-3002 : Step(126): len = 53589.5, overlap = 33.7188
PHY-3002 : Step(127): len = 53823.1, overlap = 29.0938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000994974
PHY-3002 : Step(128): len = 54087.8, overlap = 31
PHY-3002 : Step(129): len = 54517.5, overlap = 30.1562
PHY-3002 : Step(130): len = 55005.4, overlap = 28.0938
PHY-3002 : Step(131): len = 55392.7, overlap = 26.4688
PHY-3002 : Step(132): len = 55450, overlap = 25.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7416, tnet num: 2079, tinst num: 1560, tnode num: 10517, tedge num: 12521.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.41 peak overflow 2.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2081.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58040, over cnt = 248(0%), over = 962, worst = 18
PHY-1001 : End global iterations;  0.073195s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (149.4%)

PHY-1001 : Congestion index: top1 = 43.00, top5 = 25.44, top10 = 16.51, top15 = 11.80.
PHY-1001 : End incremental global routing;  0.131502s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (130.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072454s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (107.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.235184s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (119.6%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1599/2081.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58040, over cnt = 248(0%), over = 962, worst = 18
PHY-1002 : len = 64368, over cnt = 154(0%), over = 327, worst = 18
PHY-1002 : len = 67240, over cnt = 43(0%), over = 81, worst = 7
PHY-1002 : len = 67744, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 67840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094418s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (132.4%)

PHY-1001 : Congestion index: top1 = 35.65, top5 = 24.10, top10 = 17.85, top15 = 13.28.
OPT-1001 : End congestion update;  0.141352s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (121.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061525s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206413s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (113.5%)

OPT-1001 : Current memory(MB): used = 210, reserve = 178, peak = 210.
OPT-1001 : End physical optimization;  0.732757s wall, 0.796875s user + 0.062500s system = 0.859375s CPU (117.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 657 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1032/1336 primitive instances ...
PHY-3001 : End packing;  0.051568s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 777 instances
RUN-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1897 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 775 instances, 726 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55281.8, Over = 49.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6179, tnet num: 1895, tinst num: 775, tnode num: 8403, tedge num: 10875.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311793s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.84953e-05
PHY-3002 : Step(133): len = 54179.7, overlap = 51.5
PHY-3002 : Step(134): len = 53493.5, overlap = 51.5
PHY-3002 : Step(135): len = 52922.7, overlap = 50.25
PHY-3002 : Step(136): len = 52831.4, overlap = 51.75
PHY-3002 : Step(137): len = 52798.5, overlap = 51.5
PHY-3002 : Step(138): len = 52398.6, overlap = 52.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.69906e-05
PHY-3002 : Step(139): len = 52699.1, overlap = 52.5
PHY-3002 : Step(140): len = 53086.9, overlap = 52
PHY-3002 : Step(141): len = 53697.1, overlap = 51.75
PHY-3002 : Step(142): len = 53881.2, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000193981
PHY-3002 : Step(143): len = 54475.8, overlap = 46
PHY-3002 : Step(144): len = 54980.2, overlap = 42.25
PHY-3001 : Before Legalized: Len = 54980.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083175s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (150.3%)

PHY-3001 : After Legalized: Len = 67927.7, Over = 0
PHY-3001 : Trial Legalized: Len = 67927.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050211s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00271113
PHY-3002 : Step(145): len = 65041.6, overlap = 6.75
PHY-3002 : Step(146): len = 63957.4, overlap = 8.5
PHY-3002 : Step(147): len = 62821.6, overlap = 11.5
PHY-3002 : Step(148): len = 61995.8, overlap = 12.75
PHY-3002 : Step(149): len = 61204.9, overlap = 15
PHY-3002 : Step(150): len = 60285.1, overlap = 16
PHY-3002 : Step(151): len = 59873.9, overlap = 16.75
PHY-3002 : Step(152): len = 59671.2, overlap = 17.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00542225
PHY-3002 : Step(153): len = 59726.6, overlap = 18.75
PHY-3002 : Step(154): len = 59707.6, overlap = 18.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0108445
PHY-3002 : Step(155): len = 59730.2, overlap = 18.5
PHY-3002 : Step(156): len = 59749.5, overlap = 19.25
PHY-3001 : Before Legalized: Len = 59749.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005456s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63371.7, Over = 0
PHY-3001 : Legalized: Len = 63371.7, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005277s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (296.1%)

PHY-3001 : 12 instances has been re-located, deltaX = 1, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 63613.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6179, tnet num: 1895, tinst num: 775, tnode num: 8403, tedge num: 10875.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 154/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70432, over cnt = 131(0%), over = 209, worst = 7
PHY-1002 : len = 71456, over cnt = 58(0%), over = 63, worst = 2
PHY-1002 : len = 72144, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 72240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110947s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (183.1%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.19, top10 = 17.86, top15 = 13.86.
PHY-1001 : End incremental global routing;  0.161302s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (155.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056349s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (83.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.246071s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (133.3%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005436s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.19, top10 = 17.86, top15 = 13.86.
OPT-1001 : End congestion update;  0.050842s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (92.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048105s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 735 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 775 instances, 726 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63613.8, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004792s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (326.1%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 63613.8, Over = 0
PHY-3001 : End incremental legalization;  0.032746s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.144163s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (97.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 188, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046512s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1897.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005239s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (298.3%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.19, top10 = 17.86, top15 = 13.86.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050415s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.816569s wall, 0.843750s user + 0.046875s system = 0.890625s CPU (109.1%)

RUN-1003 : finish command "place" in  5.145195s wall, 6.906250s user + 2.921875s system = 9.828125s CPU (191.0%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 777 instances
RUN-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1897 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6179, tnet num: 1895, tinst num: 775, tnode num: 8403, tedge num: 10875.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68880, over cnt = 151(0%), over = 229, worst = 6
PHY-1002 : len = 70128, over cnt = 70(0%), over = 75, worst = 2
PHY-1002 : len = 71024, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122205s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (115.1%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.83, top10 = 17.61, top15 = 13.66.
PHY-1001 : End global routing;  0.169454s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (110.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 5.217977s wall, 5.156250s user + 0.062500s system = 5.218750s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30560, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.791095s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End phase 1; 1.800357s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174320, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 2.282728s wall, 3.734375s user + 0.265625s system = 4.000000s CPU (175.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.654   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.088  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.571804s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 2; 2.854800s wall, 4.296875s user + 0.265625s system = 4.562500s CPU (159.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174320, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.029399s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (106.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 174152, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.043514s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 174264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.041814s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (149.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.654   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.088  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.714864s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.364188s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 1.426617s wall, 1.406250s user + 0.046875s system = 1.453125s CPU (101.9%)

PHY-1003 : Routed, final wirelength = 174264
PHY-1001 : Current memory(MB): used = 543, reserve = 516, peak = 543.
PHY-1001 : End export database. 0.013729s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (113.8%)

PHY-1001 : End detail routing;  11.593872s wall, 12.937500s user + 0.421875s system = 13.359375s CPU (115.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6179, tnet num: 1895, tinst num: 775, tnode num: 8403, tedge num: 10875.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  12.692694s wall, 14.046875s user + 0.421875s system = 14.468750s CPU (114.0%)

RUN-1004 : used memory is 497 MB, reserved memory is 471 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      757   out of  19600    3.86%
#reg                     1019   out of  19600    5.20%
#le                      1414
  #lut only               395   out of   1414   27.93%
  #reg only               657   out of   1414   46.46%
  #lut&reg                362   out of   1414   25.60%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         456
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1414   |568     |189     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1010   |257     |125     |829     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |17      |4       |18      |0       |0       |
|    demodu                  |Demodulation                                     |429    |78      |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |143    |28      |15      |115     |0       |0       |
|    modu                    |Modulation                                       |68     |40      |7       |66      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |66      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |165    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |105    |83      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1318  
    #2          2       301   
    #3          3       110   
    #4          4        23   
    #5        5-10       70   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6179, tnet num: 1895, tinst num: 775, tnode num: 8403, tedge num: 10875.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1895 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 775
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1897, pip num: 14197
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1291 valid insts, and 37141 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.510654s wall, 19.000000s user + 0.109375s system = 19.109375s CPU (544.3%)

RUN-1004 : used memory is 514 MB, reserved memory is 485 MB, peak memory is 662 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250724_103027.log"

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 10 14:55:51 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1500 instances
RUN-0007 : 382 luts, 878 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2018 nets
RUN-1001 : 1500 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1498 instances, 382 luts, 878 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7194, tnet num: 2016, tinst num: 1498, tnode num: 10106, tedge num: 12201.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.239614s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (104.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 532424
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1498.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 464684, overlap = 20.25
PHY-3002 : Step(2): len = 442304, overlap = 13.5
PHY-3002 : Step(3): len = 426620, overlap = 18
PHY-3002 : Step(4): len = 410948, overlap = 13.5
PHY-3002 : Step(5): len = 404137, overlap = 18
PHY-3002 : Step(6): len = 378821, overlap = 11.25
PHY-3002 : Step(7): len = 361859, overlap = 13.5
PHY-3002 : Step(8): len = 353842, overlap = 15.75
PHY-3002 : Step(9): len = 319561, overlap = 15.75
PHY-3002 : Step(10): len = 299835, overlap = 18
PHY-3002 : Step(11): len = 290256, overlap = 13.5
PHY-3002 : Step(12): len = 286484, overlap = 18
PHY-3002 : Step(13): len = 273214, overlap = 15.75
PHY-3002 : Step(14): len = 263753, overlap = 15.75
PHY-3002 : Step(15): len = 261068, overlap = 15.75
PHY-3002 : Step(16): len = 249739, overlap = 13.5
PHY-3002 : Step(17): len = 238960, overlap = 20.25
PHY-3002 : Step(18): len = 234810, overlap = 20.25
PHY-3002 : Step(19): len = 230103, overlap = 20.25
PHY-3002 : Step(20): len = 220817, overlap = 20.25
PHY-3002 : Step(21): len = 216595, overlap = 20.25
PHY-3002 : Step(22): len = 211917, overlap = 20.25
PHY-3002 : Step(23): len = 206335, overlap = 20.25
PHY-3002 : Step(24): len = 202259, overlap = 20.25
PHY-3002 : Step(25): len = 198667, overlap = 20.25
PHY-3002 : Step(26): len = 191384, overlap = 20.25
PHY-3002 : Step(27): len = 185646, overlap = 20.25
PHY-3002 : Step(28): len = 183213, overlap = 20.25
PHY-3002 : Step(29): len = 175839, overlap = 20.25
PHY-3002 : Step(30): len = 163217, overlap = 20.25
PHY-3002 : Step(31): len = 160362, overlap = 20.25
PHY-3002 : Step(32): len = 157365, overlap = 20.25
PHY-3002 : Step(33): len = 148056, overlap = 20.25
PHY-3002 : Step(34): len = 143661, overlap = 20.25
PHY-3002 : Step(35): len = 141457, overlap = 20.25
PHY-3002 : Step(36): len = 137157, overlap = 20.25
PHY-3002 : Step(37): len = 134389, overlap = 20.25
PHY-3002 : Step(38): len = 128468, overlap = 20.25
PHY-3002 : Step(39): len = 124487, overlap = 20.25
PHY-3002 : Step(40): len = 122078, overlap = 20.25
PHY-3002 : Step(41): len = 118662, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.03889e-05
PHY-3002 : Step(42): len = 119816, overlap = 20.25
PHY-3002 : Step(43): len = 119777, overlap = 20.25
PHY-3002 : Step(44): len = 118237, overlap = 18
PHY-3002 : Step(45): len = 117056, overlap = 18
PHY-3002 : Step(46): len = 113063, overlap = 11.25
PHY-3002 : Step(47): len = 109784, overlap = 13.5
PHY-3002 : Step(48): len = 108910, overlap = 9
PHY-3002 : Step(49): len = 104890, overlap = 13.5
PHY-3002 : Step(50): len = 99858.3, overlap = 13.625
PHY-3002 : Step(51): len = 97705.5, overlap = 16.125
PHY-3002 : Step(52): len = 97087.9, overlap = 11.875
PHY-3002 : Step(53): len = 94504.8, overlap = 14.1875
PHY-3002 : Step(54): len = 93901.4, overlap = 15.75
PHY-3002 : Step(55): len = 89757.2, overlap = 13.5
PHY-3002 : Step(56): len = 88392.6, overlap = 13.5
PHY-3002 : Step(57): len = 86433, overlap = 15.75
PHY-3002 : Step(58): len = 85862.8, overlap = 15.75
PHY-3002 : Step(59): len = 83770, overlap = 15.75
PHY-3002 : Step(60): len = 82071.7, overlap = 13.5
PHY-3002 : Step(61): len = 80432.1, overlap = 13.5
PHY-3002 : Step(62): len = 79790, overlap = 13.5
PHY-3002 : Step(63): len = 77508.4, overlap = 15.75
PHY-3002 : Step(64): len = 75677.6, overlap = 15.75
PHY-3002 : Step(65): len = 74487.3, overlap = 18
PHY-3002 : Step(66): len = 72545.1, overlap = 18
PHY-3002 : Step(67): len = 72020, overlap = 18
PHY-3002 : Step(68): len = 71137.6, overlap = 15.75
PHY-3002 : Step(69): len = 70683.4, overlap = 15.75
PHY-3002 : Step(70): len = 69509.5, overlap = 15.75
PHY-3002 : Step(71): len = 68036.2, overlap = 13.5
PHY-3002 : Step(72): len = 67435.9, overlap = 15.8125
PHY-3002 : Step(73): len = 66623.3, overlap = 13.625
PHY-3002 : Step(74): len = 65887.9, overlap = 13.5
PHY-3002 : Step(75): len = 65747.9, overlap = 15.75
PHY-3002 : Step(76): len = 64465.2, overlap = 15.8125
PHY-3002 : Step(77): len = 63536.9, overlap = 11.3125
PHY-3002 : Step(78): len = 62502.1, overlap = 13.5625
PHY-3002 : Step(79): len = 61613.3, overlap = 15.875
PHY-3002 : Step(80): len = 60253.1, overlap = 13.5
PHY-3002 : Step(81): len = 60064.3, overlap = 15.75
PHY-3002 : Step(82): len = 59676.3, overlap = 13.5
PHY-3002 : Step(83): len = 59080.2, overlap = 11.25
PHY-3002 : Step(84): len = 59031.2, overlap = 13.5
PHY-3002 : Step(85): len = 57536.4, overlap = 11.25
PHY-3002 : Step(86): len = 57161.8, overlap = 13.5
PHY-3002 : Step(87): len = 56347.8, overlap = 13.5
PHY-3002 : Step(88): len = 56260.6, overlap = 13.5
PHY-3002 : Step(89): len = 55968.5, overlap = 13.5
PHY-3002 : Step(90): len = 55397.5, overlap = 13.5
PHY-3002 : Step(91): len = 55210.5, overlap = 13.5
PHY-3002 : Step(92): len = 54939.4, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000180778
PHY-3002 : Step(93): len = 55404.2, overlap = 13.5
PHY-3002 : Step(94): len = 55461.8, overlap = 13.5
PHY-3002 : Step(95): len = 55307, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000361556
PHY-3002 : Step(96): len = 55624.9, overlap = 13.5
PHY-3002 : Step(97): len = 55537.9, overlap = 13.5
PHY-3001 : Before Legalized: Len = 55537.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010843s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.1%)

PHY-3001 : After Legalized: Len = 61421.3, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070946s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(98): len = 61525.2, overlap = 10.4375
PHY-3002 : Step(99): len = 61644.3, overlap = 10.625
PHY-3002 : Step(100): len = 60799, overlap = 11.0625
PHY-3002 : Step(101): len = 60545.3, overlap = 11.0938
PHY-3002 : Step(102): len = 59519.2, overlap = 12.5312
PHY-3002 : Step(103): len = 58816.4, overlap = 10.8438
PHY-3002 : Step(104): len = 58575.7, overlap = 10.9062
PHY-3002 : Step(105): len = 57376, overlap = 12.1562
PHY-3002 : Step(106): len = 56167.1, overlap = 11.6875
PHY-3002 : Step(107): len = 54612.3, overlap = 13.4062
PHY-3002 : Step(108): len = 53987.9, overlap = 14.0938
PHY-3002 : Step(109): len = 52829.5, overlap = 14.4062
PHY-3002 : Step(110): len = 52683.7, overlap = 15.0312
PHY-3002 : Step(111): len = 52594, overlap = 15.9688
PHY-3002 : Step(112): len = 51908.9, overlap = 16.2812
PHY-3002 : Step(113): len = 51482, overlap = 18.4688
PHY-3002 : Step(114): len = 51264.8, overlap = 16.2812
PHY-3002 : Step(115): len = 51084.8, overlap = 16.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00376444
PHY-3002 : Step(116): len = 50949.4, overlap = 16.9375
PHY-3002 : Step(117): len = 50696.1, overlap = 16.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062808s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.58774e-05
PHY-3002 : Step(118): len = 50900, overlap = 56.5625
PHY-3002 : Step(119): len = 52544.1, overlap = 54.625
PHY-3002 : Step(120): len = 52983.4, overlap = 43.9062
PHY-3002 : Step(121): len = 52581.6, overlap = 42.375
PHY-3002 : Step(122): len = 52323.6, overlap = 43.3125
PHY-3002 : Step(123): len = 52221.5, overlap = 44.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000171755
PHY-3002 : Step(124): len = 52398.1, overlap = 43
PHY-3002 : Step(125): len = 52765.3, overlap = 41.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00034351
PHY-3002 : Step(126): len = 53588, overlap = 39.3438
PHY-3002 : Step(127): len = 54420.4, overlap = 39.6562
PHY-3002 : Step(128): len = 55233.6, overlap = 36.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7194, tnet num: 2016, tinst num: 1498, tnode num: 10106, tedge num: 12201.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.12 peak overflow 3.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2018.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57256, over cnt = 233(0%), over = 1105, worst = 15
PHY-1001 : End global iterations;  0.057084s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (136.9%)

PHY-1001 : Congestion index: top1 = 46.29, top5 = 26.37, top10 = 16.29, top15 = 11.52.
PHY-1001 : End incremental global routing;  0.103820s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (105.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064873s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.197334s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.9%)

OPT-1001 : Current memory(MB): used = 207, reserve = 173, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1542/2018.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57256, over cnt = 233(0%), over = 1105, worst = 15
PHY-1002 : len = 64840, over cnt = 158(0%), over = 396, worst = 13
PHY-1002 : len = 66856, over cnt = 96(0%), over = 205, worst = 11
PHY-1002 : len = 69144, over cnt = 26(0%), over = 77, worst = 11
PHY-1002 : len = 70080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097750s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (127.9%)

PHY-1001 : Congestion index: top1 = 39.27, top5 = 25.84, top10 = 18.31, top15 = 13.43.
OPT-1001 : End congestion update;  0.139320s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (123.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2016 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053241s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.195489s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (119.9%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : End physical optimization;  0.625386s wall, 0.640625s user + 0.046875s system = 0.687500s CPU (109.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 382 LUT to BLE ...
SYN-4008 : Packed 382 LUT and 180 SEQ to BLE.
SYN-4003 : Packing 698 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 606 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 988/1292 primitive instances ...
PHY-3001 : End packing;  0.046082s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 761 instances
RUN-1001 : 355 mslices, 355 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1846 nets
RUN-1001 : 1326 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 759 instances, 710 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55316.6, Over = 58.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6076, tnet num: 1844, tinst num: 759, tnode num: 8198, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268480s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.85687e-05
PHY-3002 : Step(129): len = 54297.9, overlap = 58
PHY-3002 : Step(130): len = 53461.5, overlap = 60.5
PHY-3002 : Step(131): len = 53212.7, overlap = 61.5
PHY-3002 : Step(132): len = 52770.2, overlap = 60
PHY-3002 : Step(133): len = 52638.8, overlap = 59
PHY-3002 : Step(134): len = 52411.7, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.71375e-05
PHY-3002 : Step(135): len = 53147.5, overlap = 55
PHY-3002 : Step(136): len = 53880, overlap = 51
PHY-3002 : Step(137): len = 54575.4, overlap = 47.75
PHY-3002 : Step(138): len = 55048.6, overlap = 47.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000154275
PHY-3002 : Step(139): len = 55229.4, overlap = 44.5
PHY-3002 : Step(140): len = 55862.7, overlap = 44
PHY-3001 : Before Legalized: Len = 55862.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068362s wall, 0.031250s user + 0.062500s system = 0.093750s CPU (137.1%)

PHY-3001 : After Legalized: Len = 68300.4, Over = 0
PHY-3001 : Trial Legalized: Len = 68300.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047136s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000832621
PHY-3002 : Step(141): len = 65193.5, overlap = 4.75
PHY-3002 : Step(142): len = 63096.9, overlap = 8.5
PHY-3002 : Step(143): len = 61417.1, overlap = 11.25
PHY-3002 : Step(144): len = 60301, overlap = 11.5
PHY-3002 : Step(145): len = 59631.4, overlap = 16.5
PHY-3002 : Step(146): len = 59248.2, overlap = 18.5
PHY-3002 : Step(147): len = 58909.5, overlap = 18.75
PHY-3002 : Step(148): len = 58796.2, overlap = 19
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00166524
PHY-3002 : Step(149): len = 59069.9, overlap = 19.25
PHY-3002 : Step(150): len = 59152.7, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00333049
PHY-3002 : Step(151): len = 59256.9, overlap = 18.75
PHY-3002 : Step(152): len = 59329.4, overlap = 19.75
PHY-3001 : Before Legalized: Len = 59329.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005057s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63385.9, Over = 0
PHY-3001 : Legalized: Len = 63385.9, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004896s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 63427.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6076, tnet num: 1844, tinst num: 759, tnode num: 8198, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70016, over cnt = 148(0%), over = 223, worst = 6
PHY-1002 : len = 70888, over cnt = 79(0%), over = 91, worst = 3
PHY-1002 : len = 71896, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120894s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (116.3%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 22.90, top10 = 17.77, top15 = 13.78.
PHY-1001 : End incremental global routing;  0.170090s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (110.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054501s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.253395s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (111.0%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1640/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006704s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 22.90, top10 = 17.77, top15 = 13.78.
OPT-1001 : End congestion update;  0.052719s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046887s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 719 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 759 instances, 710 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63476.6, Over = 0
PHY-3001 : End spreading;  0.004860s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63476.6, Over = 0
PHY-3001 : End incremental legalization;  0.032973s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.146479s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (170.7%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044728s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1628/1846.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007130s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 22.87, top10 = 17.76, top15 = 13.77.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046158s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.798933s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (115.4%)

RUN-1003 : finish command "place" in  4.763069s wall, 6.375000s user + 2.312500s system = 8.687500s CPU (182.4%)

RUN-1004 : used memory is 193 MB, reserved memory is 161 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 761 instances
RUN-1001 : 355 mslices, 355 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1846 nets
RUN-1001 : 1326 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6076, tnet num: 1844, tinst num: 759, tnode num: 8198, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 355 mslices, 355 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69136, over cnt = 151(0%), over = 231, worst = 6
PHY-1002 : len = 70056, over cnt = 85(0%), over = 99, worst = 3
PHY-1002 : len = 71320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104377s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (119.8%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 22.83, top10 = 17.71, top15 = 13.66.
PHY-1001 : End global routing;  0.152693s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 464, peak = 493.
PHY-1001 : End build detailed router design. 3.053542s wall, 3.000000s user + 0.046875s system = 3.046875s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30040, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.002283s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 525, reserve = 497, peak = 525.
PHY-1001 : End phase 1; 1.007956s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178680, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.630485s wall, 2.265625s user + 0.140625s system = 2.406250s CPU (147.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1638(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.454   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.273   |  -12.728  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.314492s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.945075s wall, 2.578125s user + 0.140625s system = 2.718750s CPU (139.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178680, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014945s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178592, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032677s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (143.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178640, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020901s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (149.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1638(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.454   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.273   |  -12.728  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.314512s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.158481s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.665718s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (103.3%)

PHY-1003 : Routed, final wirelength = 178640
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.009643s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.0%)

PHY-1001 : End detail routing;  6.853301s wall, 7.437500s user + 0.218750s system = 7.656250s CPU (111.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6076, tnet num: 1844, tinst num: 759, tnode num: 8198, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.638121s wall, 8.234375s user + 0.234375s system = 8.468750s CPU (110.9%)

RUN-1004 : used memory is 496 MB, reserved memory is 472 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      766   out of  19600    3.91%
#reg                      953   out of  19600    4.86%
#le                      1372
  #lut only               419   out of   1372   30.54%
  #reg only               606   out of   1372   44.17%
  #lut&reg                347   out of   1372   25.29%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       429
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       102
#3        wendu/clk_us                    GCLK               lslice             signal_process/trans/clk_out_n_syn_50.q0    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1372   |577     |189     |986     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1011   |281     |127     |825     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |430    |89      |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |38      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |14      |0       |17      |0       |0       |
|    integ                   |Integration                                      |139    |24      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |67     |31      |7       |65      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |91      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |133    |123     |7       |61      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |75     |75      |0       |28      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |65      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1287  
    #2          2       272   
    #3          3       123   
    #4          4        15   
    #5        5-10       76   
    #6        11-50      28   
    #7       101-500     1    
  Average     2.01            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6076, tnet num: 1844, tinst num: 759, tnode num: 8198, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1844 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 759
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1846, pip num: 14100
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1271 valid insts, and 37039 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.949498s wall, 16.250000s user + 0.109375s system = 16.359375s CPU (554.6%)

RUN-1004 : used memory is 513 MB, reserved memory is 483 MB, peak memory is 662 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_145551.log"

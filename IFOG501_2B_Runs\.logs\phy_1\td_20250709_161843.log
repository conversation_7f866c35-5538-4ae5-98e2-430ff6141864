============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul  9 16:18:43 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1530 instances
RUN-0007 : 384 luts, 899 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2063 nets
RUN-1001 : 1512 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1528 instances, 384 luts, 899 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7357, tnet num: 2061, tinst num: 1528, tnode num: 10338, tedge num: 12519.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.272635s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 528700
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1528.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453551, overlap = 15.75
PHY-3002 : Step(2): len = 429599, overlap = 18
PHY-3002 : Step(3): len = 415721, overlap = 15.75
PHY-3002 : Step(4): len = 405333, overlap = 18
PHY-3002 : Step(5): len = 392637, overlap = 13.5
PHY-3002 : Step(6): len = 377302, overlap = 9
PHY-3002 : Step(7): len = 364586, overlap = 13.5
PHY-3002 : Step(8): len = 356865, overlap = 13.5
PHY-3002 : Step(9): len = 340607, overlap = 15.75
PHY-3002 : Step(10): len = 328805, overlap = 15.75
PHY-3002 : Step(11): len = 322832, overlap = 15.75
PHY-3002 : Step(12): len = 313565, overlap = 13.5
PHY-3002 : Step(13): len = 305065, overlap = 13.5
PHY-3002 : Step(14): len = 299091, overlap = 13.5
PHY-3002 : Step(15): len = 292407, overlap = 15.75
PHY-3002 : Step(16): len = 283493, overlap = 15.75
PHY-3002 : Step(17): len = 278667, overlap = 13.5
PHY-3002 : Step(18): len = 273073, overlap = 13.5
PHY-3002 : Step(19): len = 263692, overlap = 15.75
PHY-3002 : Step(20): len = 257056, overlap = 20.25
PHY-3002 : Step(21): len = 253505, overlap = 20.25
PHY-3002 : Step(22): len = 243005, overlap = 20.25
PHY-3002 : Step(23): len = 234188, overlap = 20.25
PHY-3002 : Step(24): len = 230925, overlap = 20.25
PHY-3002 : Step(25): len = 225460, overlap = 20.25
PHY-3002 : Step(26): len = 202258, overlap = 18
PHY-3002 : Step(27): len = 195528, overlap = 20.25
PHY-3002 : Step(28): len = 193611, overlap = 20.25
PHY-3002 : Step(29): len = 162851, overlap = 20.25
PHY-3002 : Step(30): len = 148206, overlap = 20.25
PHY-3002 : Step(31): len = 146819, overlap = 20.25
PHY-3002 : Step(32): len = 142548, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132191
PHY-3002 : Step(33): len = 143208, overlap = 20.25
PHY-3002 : Step(34): len = 143253, overlap = 20.25
PHY-3002 : Step(35): len = 140962, overlap = 15.75
PHY-3002 : Step(36): len = 139306, overlap = 15.75
PHY-3002 : Step(37): len = 134822, overlap = 15.75
PHY-3002 : Step(38): len = 133060, overlap = 13.5
PHY-3002 : Step(39): len = 127579, overlap = 15.75
PHY-3002 : Step(40): len = 125159, overlap = 15.75
PHY-3002 : Step(41): len = 122468, overlap = 13.5
PHY-3002 : Step(42): len = 121814, overlap = 13.5
PHY-3002 : Step(43): len = 117825, overlap = 13.5
PHY-3002 : Step(44): len = 116183, overlap = 13.5
PHY-3002 : Step(45): len = 113169, overlap = 15.75
PHY-3002 : Step(46): len = 112784, overlap = 18
PHY-3002 : Step(47): len = 109099, overlap = 15.75
PHY-3002 : Step(48): len = 105536, overlap = 15.75
PHY-3002 : Step(49): len = 102641, overlap = 15.75
PHY-3002 : Step(50): len = 102559, overlap = 18
PHY-3002 : Step(51): len = 98904.3, overlap = 15.75
PHY-3002 : Step(52): len = 96688.7, overlap = 18
PHY-3002 : Step(53): len = 94277.6, overlap = 18
PHY-3002 : Step(54): len = 93643.3, overlap = 18
PHY-3002 : Step(55): len = 90358.1, overlap = 15.75
PHY-3002 : Step(56): len = 89146.7, overlap = 15.75
PHY-3002 : Step(57): len = 86222.6, overlap = 18
PHY-3002 : Step(58): len = 84840.2, overlap = 18
PHY-3002 : Step(59): len = 83721.6, overlap = 18
PHY-3002 : Step(60): len = 82326.6, overlap = 18
PHY-3002 : Step(61): len = 79774, overlap = 15.75
PHY-3002 : Step(62): len = 78344.1, overlap = 13.5
PHY-3002 : Step(63): len = 77263.8, overlap = 13.5
PHY-3002 : Step(64): len = 76954.6, overlap = 15.75
PHY-3002 : Step(65): len = 75868.4, overlap = 18
PHY-3002 : Step(66): len = 74751.7, overlap = 18
PHY-3002 : Step(67): len = 72885.1, overlap = 15.75
PHY-3002 : Step(68): len = 72466.7, overlap = 13.5
PHY-3002 : Step(69): len = 71734.5, overlap = 15.75
PHY-3002 : Step(70): len = 71289.2, overlap = 15.75
PHY-3002 : Step(71): len = 70235.4, overlap = 15.75
PHY-3002 : Step(72): len = 68640.7, overlap = 15.75
PHY-3002 : Step(73): len = 66641.4, overlap = 15.75
PHY-3002 : Step(74): len = 65001.8, overlap = 18
PHY-3002 : Step(75): len = 64578.9, overlap = 18
PHY-3002 : Step(76): len = 64270.2, overlap = 18
PHY-3002 : Step(77): len = 63927.9, overlap = 11.25
PHY-3002 : Step(78): len = 63588.7, overlap = 11.25
PHY-3002 : Step(79): len = 62675.1, overlap = 18
PHY-3002 : Step(80): len = 61196.2, overlap = 18
PHY-3002 : Step(81): len = 60732.6, overlap = 15.75
PHY-3002 : Step(82): len = 60145.6, overlap = 15.75
PHY-3002 : Step(83): len = 59511.2, overlap = 15.75
PHY-3002 : Step(84): len = 59461.5, overlap = 15.75
PHY-3002 : Step(85): len = 58567.4, overlap = 15.75
PHY-3002 : Step(86): len = 58120.5, overlap = 13.5
PHY-3002 : Step(87): len = 57241.9, overlap = 15.75
PHY-3002 : Step(88): len = 56680.3, overlap = 15.75
PHY-3002 : Step(89): len = 56728.1, overlap = 15.75
PHY-3002 : Step(90): len = 56343.3, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000264383
PHY-3002 : Step(91): len = 56595.1, overlap = 15.75
PHY-3002 : Step(92): len = 56631.1, overlap = 15.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000528766
PHY-3002 : Step(93): len = 56811.8, overlap = 15.75
PHY-3002 : Step(94): len = 56879.7, overlap = 15.75
PHY-3001 : Before Legalized: Len = 56879.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011659s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 60092.7, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068472s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(95): len = 60031.3, overlap = 11.3125
PHY-3002 : Step(96): len = 60034.6, overlap = 11.3125
PHY-3002 : Step(97): len = 59608.6, overlap = 14.8438
PHY-3002 : Step(98): len = 59544.7, overlap = 14.75
PHY-3002 : Step(99): len = 59616.1, overlap = 14.4688
PHY-3002 : Step(100): len = 58856, overlap = 14
PHY-3002 : Step(101): len = 58468.6, overlap = 13.3438
PHY-3002 : Step(102): len = 57188.6, overlap = 11.4062
PHY-3002 : Step(103): len = 56638.1, overlap = 11.2812
PHY-3002 : Step(104): len = 56051.5, overlap = 12.7812
PHY-3002 : Step(105): len = 54915.7, overlap = 13.2188
PHY-3002 : Step(106): len = 54321.7, overlap = 13.5312
PHY-3002 : Step(107): len = 53626.3, overlap = 18.9062
PHY-3002 : Step(108): len = 53009.8, overlap = 20.1562
PHY-3002 : Step(109): len = 52530.5, overlap = 21.2188
PHY-3002 : Step(110): len = 52168.8, overlap = 21.375
PHY-3002 : Step(111): len = 52170.1, overlap = 21.9375
PHY-3002 : Step(112): len = 52232.3, overlap = 22.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00454869
PHY-3002 : Step(113): len = 52011.3, overlap = 22.6562
PHY-3002 : Step(114): len = 52044.2, overlap = 23.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00909738
PHY-3002 : Step(115): len = 51948, overlap = 23.625
PHY-3002 : Step(116): len = 51939.6, overlap = 23.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060870s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (102.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.86696e-05
PHY-3002 : Step(117): len = 52212.3, overlap = 53.9688
PHY-3002 : Step(118): len = 53368.2, overlap = 46.2812
PHY-3002 : Step(119): len = 53989.2, overlap = 45.375
PHY-3002 : Step(120): len = 53753.5, overlap = 46.1562
PHY-3002 : Step(121): len = 53505.9, overlap = 46.5938
PHY-3002 : Step(122): len = 53446.6, overlap = 46.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000177339
PHY-3002 : Step(123): len = 53262.8, overlap = 45.75
PHY-3002 : Step(124): len = 53845.4, overlap = 43.0938
PHY-3002 : Step(125): len = 54214.4, overlap = 44.25
PHY-3002 : Step(126): len = 55000, overlap = 38.6562
PHY-3002 : Step(127): len = 54862.3, overlap = 39.4688
PHY-3002 : Step(128): len = 54534, overlap = 40.8438
PHY-3002 : Step(129): len = 54336, overlap = 39.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000354678
PHY-3002 : Step(130): len = 54262.6, overlap = 39.5312
PHY-3002 : Step(131): len = 54282.4, overlap = 39.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7357, tnet num: 2061, tinst num: 1528, tnode num: 10338, tedge num: 12519.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.06 peak overflow 2.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58744, over cnt = 249(0%), over = 988, worst = 17
PHY-1001 : End global iterations;  0.074967s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (125.1%)

PHY-1001 : Congestion index: top1 = 43.17, top5 = 26.07, top10 = 16.46, top15 = 11.71.
PHY-1001 : End incremental global routing;  0.125637s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (111.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070789s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (44.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.226041s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (89.9%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1552/2063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58744, over cnt = 249(0%), over = 988, worst = 17
PHY-1002 : len = 65080, over cnt = 158(0%), over = 340, worst = 10
PHY-1002 : len = 68432, over cnt = 55(0%), over = 81, worst = 9
PHY-1002 : len = 69960, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 69992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097422s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (112.3%)

PHY-1001 : Congestion index: top1 = 36.85, top5 = 24.95, top10 = 18.03, top15 = 13.38.
OPT-1001 : End congestion update;  0.142854s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (109.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056724s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.202436s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (108.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.687871s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (97.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 718 remaining SEQ's ...
SYN-4005 : Packed 77 SEQ with LUT/SLICE
SYN-4006 : 141 single LUT's are left
SYN-4006 : 641 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1025/1336 primitive instances ...
PHY-3001 : End packing;  0.050257s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 783 instances
RUN-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-1001 : 1341 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 781 instances, 732 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54633, Over = 64
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6234, tnet num: 1888, tinst num: 781, tnode num: 8422, tedge num: 11044.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.295824s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.1428e-05
PHY-3002 : Step(132): len = 53919.4, overlap = 66.25
PHY-3002 : Step(133): len = 53635.2, overlap = 67.75
PHY-3002 : Step(134): len = 53349.6, overlap = 67.75
PHY-3002 : Step(135): len = 53097, overlap = 65.5
PHY-3002 : Step(136): len = 52827.4, overlap = 63
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.28559e-05
PHY-3002 : Step(137): len = 53168.9, overlap = 63.25
PHY-3002 : Step(138): len = 54122.7, overlap = 61.75
PHY-3002 : Step(139): len = 54689.1, overlap = 59.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000125712
PHY-3002 : Step(140): len = 54853.8, overlap = 58
PHY-3002 : Step(141): len = 55396.9, overlap = 56.75
PHY-3002 : Step(142): len = 55908.2, overlap = 51.75
PHY-3002 : Step(143): len = 56002.9, overlap = 50.25
PHY-3001 : Before Legalized: Len = 56002.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.076558s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (163.3%)

PHY-3001 : After Legalized: Len = 68275.9, Over = 0
PHY-3001 : Trial Legalized: Len = 68275.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049680s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000976714
PHY-3002 : Step(144): len = 64809.5, overlap = 4.5
PHY-3002 : Step(145): len = 62426.4, overlap = 10.5
PHY-3002 : Step(146): len = 60880, overlap = 14.75
PHY-3002 : Step(147): len = 59987.7, overlap = 17.25
PHY-3002 : Step(148): len = 59508.7, overlap = 19
PHY-3002 : Step(149): len = 59143.1, overlap = 21.25
PHY-3002 : Step(150): len = 58802, overlap = 21.75
PHY-3002 : Step(151): len = 58680, overlap = 21.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00189923
PHY-3002 : Step(152): len = 58896.5, overlap = 22.5
PHY-3002 : Step(153): len = 58955.7, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00379846
PHY-3002 : Step(154): len = 59129.5, overlap = 22
PHY-3002 : Step(155): len = 59259.9, overlap = 21.75
PHY-3001 : Before Legalized: Len = 59259.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005370s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63945.2, Over = 0
PHY-3001 : Legalized: Len = 63945.2, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005105s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (306.0%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64039.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6234, tnet num: 1888, tinst num: 781, tnode num: 8422, tedge num: 11044.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70976, over cnt = 169(0%), over = 249, worst = 6
PHY-1002 : len = 72232, over cnt = 76(0%), over = 92, worst = 3
PHY-1002 : len = 73360, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 73568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132036s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (165.7%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 23.33, top10 = 18.18, top15 = 14.11.
PHY-1001 : End incremental global routing;  0.185675s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (143.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061343s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277256s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (129.6%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1664/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005649s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (276.6%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 23.33, top10 = 18.18, top15 = 14.11.
OPT-1001 : End congestion update;  0.052419s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060046s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 741 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 781 instances, 732 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64043, Over = 0
PHY-3001 : End spreading;  0.004924s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (317.3%)

PHY-3001 : Final: Len = 64043, Over = 0
PHY-3001 : End incremental legalization;  0.035147s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.161237s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048837s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1660/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007672s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 23.31, top10 = 18.19, top15 = 14.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051067s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.883365s wall, 0.968750s user + 0.046875s system = 1.015625s CPU (115.0%)

RUN-1003 : finish command "place" in  5.376961s wall, 7.500000s user + 2.890625s system = 10.390625s CPU (193.2%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 783 instances
RUN-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-1001 : 1341 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6234, tnet num: 1888, tinst num: 781, tnode num: 8422, tedge num: 11044.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70128, over cnt = 157(0%), over = 239, worst = 6
PHY-1002 : len = 71264, over cnt = 90(0%), over = 114, worst = 3
PHY-1002 : len = 72480, over cnt = 17(0%), over = 22, worst = 2
PHY-1002 : len = 72760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133937s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (81.7%)

PHY-1001 : Congestion index: top1 = 31.68, top5 = 23.07, top10 = 17.94, top15 = 13.95.
PHY-1001 : End global routing;  0.184853s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (93.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 203, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 466, peak = 494.
PHY-1001 : End build detailed router design. 3.286910s wall, 3.234375s user + 0.046875s system = 3.281250s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30976, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.179897s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 526, reserve = 500, peak = 526.
PHY-1001 : End phase 1; 1.186169s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (97.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188408, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 501, peak = 528.
PHY-1001 : End initial routed; 1.933895s wall, 2.703125s user + 0.312500s system = 3.015625s CPU (155.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1675(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.146   |  -13.594  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349477s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 531, reserve = 504, peak = 531.
PHY-1001 : End phase 2; 2.283477s wall, 3.046875s user + 0.312500s system = 3.359375s CPU (147.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188408, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016570s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188392, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026188s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (59.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188432, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022610s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1675(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.146   |  -13.594  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.361309s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.218863s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 0.779644s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (98.2%)

PHY-1003 : Routed, final wirelength = 188432
PHY-1001 : Current memory(MB): used = 546, reserve = 519, peak = 546.
PHY-1001 : End export database. 0.011040s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.728824s wall, 8.421875s user + 0.359375s system = 8.781250s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6234, tnet num: 1888, tinst num: 781, tnode num: 8422, tedge num: 11044.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.613773s wall, 9.250000s user + 0.390625s system = 9.640625s CPU (111.9%)

RUN-1004 : used memory is 500 MB, reserved memory is 475 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      782   out of  19600    3.99%
#reg                      988   out of  19600    5.04%
#le                      1423
  #lut only               435   out of   1423   30.57%
  #reg only               641   out of   1423   45.05%
  #lut&reg                347   out of   1423   24.39%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       444
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       103
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_13.q0    45
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1423   |586     |196     |1021    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1045   |276     |134     |857     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |24      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |430    |87      |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |39      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |5       |0       |13      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |15      |0       |15      |0       |0       |
|    integ                   |Integration                                      |140    |26      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |99     |32      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |311    |80      |46      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |145    |132     |7       |62      |0       |0       |
|    U0                      |speed_select_Tx                                  |38     |29      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |84     |84      |0       |29      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1302  
    #2          2       301   
    #3          3       121   
    #4          4        14   
    #5        5-10       77   
    #6        11-50      30   
    #7       101-500     1    
  Average     2.01            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6234, tnet num: 1888, tinst num: 781, tnode num: 8422, tedge num: 11044.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 781
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1890, pip num: 14509
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1339 valid insts, and 38051 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.485684s wall, 19.593750s user + 0.078125s system = 19.671875s CPU (564.4%)

RUN-1004 : used memory is 535 MB, reserved memory is 507 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250709_161843.log"

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul  9 16:17:25 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1527 instances
RUN-0007 : 382 luts, 898 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2059 nets
RUN-1001 : 1510 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1525 instances, 382 luts, 898 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7340, tnet num: 2057, tinst num: 1525, tnode num: 10320, tedge num: 12490.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.264570s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 520083
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1525.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 447547, overlap = 20.25
PHY-3002 : Step(2): len = 424488, overlap = 15.75
PHY-3002 : Step(3): len = 409387, overlap = 18
PHY-3002 : Step(4): len = 398133, overlap = 13.5
PHY-3002 : Step(5): len = 383122, overlap = 13.5
PHY-3002 : Step(6): len = 365997, overlap = 11.25
PHY-3002 : Step(7): len = 352874, overlap = 13.5
PHY-3002 : Step(8): len = 345623, overlap = 13.5
PHY-3002 : Step(9): len = 294767, overlap = 11.25
PHY-3002 : Step(10): len = 272156, overlap = 15.75
PHY-3002 : Step(11): len = 264479, overlap = 13.5
PHY-3002 : Step(12): len = 262235, overlap = 11.25
PHY-3002 : Step(13): len = 242445, overlap = 15.75
PHY-3002 : Step(14): len = 232223, overlap = 18
PHY-3002 : Step(15): len = 230006, overlap = 20.25
PHY-3002 : Step(16): len = 223377, overlap = 20.25
PHY-3002 : Step(17): len = 212724, overlap = 20.25
PHY-3002 : Step(18): len = 208696, overlap = 20.25
PHY-3002 : Step(19): len = 204059, overlap = 20.25
PHY-3002 : Step(20): len = 195133, overlap = 20.25
PHY-3002 : Step(21): len = 191477, overlap = 20.25
PHY-3002 : Step(22): len = 188119, overlap = 20.25
PHY-3002 : Step(23): len = 183444, overlap = 20.25
PHY-3002 : Step(24): len = 179539, overlap = 20.25
PHY-3002 : Step(25): len = 175560, overlap = 20.25
PHY-3002 : Step(26): len = 171481, overlap = 20.25
PHY-3002 : Step(27): len = 165499, overlap = 20.25
PHY-3002 : Step(28): len = 162235, overlap = 20.25
PHY-3002 : Step(29): len = 157502, overlap = 20.25
PHY-3002 : Step(30): len = 152634, overlap = 20.25
PHY-3002 : Step(31): len = 149069, overlap = 20.25
PHY-3002 : Step(32): len = 145496, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000116277
PHY-3002 : Step(33): len = 147022, overlap = 20.25
PHY-3002 : Step(34): len = 147218, overlap = 15.75
PHY-3002 : Step(35): len = 146367, overlap = 15.75
PHY-3002 : Step(36): len = 144363, overlap = 15.75
PHY-3002 : Step(37): len = 141740, overlap = 15.75
PHY-3002 : Step(38): len = 138611, overlap = 15.75
PHY-3002 : Step(39): len = 136366, overlap = 13.5
PHY-3002 : Step(40): len = 132291, overlap = 11.25
PHY-3002 : Step(41): len = 126666, overlap = 9
PHY-3002 : Step(42): len = 123765, overlap = 11.25
PHY-3002 : Step(43): len = 123376, overlap = 15.75
PHY-3002 : Step(44): len = 119776, overlap = 15.75
PHY-3002 : Step(45): len = 117716, overlap = 15.75
PHY-3002 : Step(46): len = 114947, overlap = 15.75
PHY-3002 : Step(47): len = 113800, overlap = 13.5
PHY-3002 : Step(48): len = 108655, overlap = 13.5
PHY-3002 : Step(49): len = 106866, overlap = 13.5
PHY-3002 : Step(50): len = 104826, overlap = 13.5
PHY-3002 : Step(51): len = 103471, overlap = 18
PHY-3002 : Step(52): len = 100185, overlap = 18
PHY-3002 : Step(53): len = 99314.6, overlap = 15.75
PHY-3002 : Step(54): len = 95938.4, overlap = 15.75
PHY-3002 : Step(55): len = 93856.1, overlap = 13.5
PHY-3002 : Step(56): len = 92125.4, overlap = 13.5
PHY-3002 : Step(57): len = 90809.1, overlap = 18
PHY-3002 : Step(58): len = 88171.4, overlap = 18
PHY-3002 : Step(59): len = 87396.8, overlap = 15.75
PHY-3002 : Step(60): len = 85887.7, overlap = 15.75
PHY-3002 : Step(61): len = 83567.1, overlap = 13.5
PHY-3002 : Step(62): len = 80854.1, overlap = 13.5
PHY-3002 : Step(63): len = 80459.8, overlap = 13.5
PHY-3002 : Step(64): len = 79036.8, overlap = 13.5
PHY-3002 : Step(65): len = 78123.9, overlap = 13.5
PHY-3002 : Step(66): len = 76579.2, overlap = 13.5
PHY-3002 : Step(67): len = 73596.6, overlap = 15.75
PHY-3002 : Step(68): len = 71912.5, overlap = 13.5
PHY-3002 : Step(69): len = 71628.1, overlap = 13.5
PHY-3002 : Step(70): len = 70580.8, overlap = 11.5625
PHY-3002 : Step(71): len = 69556.2, overlap = 11.75
PHY-3002 : Step(72): len = 68635, overlap = 9.5
PHY-3002 : Step(73): len = 67977.5, overlap = 9.5625
PHY-3002 : Step(74): len = 67792.2, overlap = 7.4375
PHY-3002 : Step(75): len = 67778.7, overlap = 7.375
PHY-3002 : Step(76): len = 67847.3, overlap = 7.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000232555
PHY-3002 : Step(77): len = 68747.5, overlap = 9.75
PHY-3002 : Step(78): len = 69119.2, overlap = 9.75
PHY-3002 : Step(79): len = 69278.5, overlap = 9.75
PHY-3002 : Step(80): len = 68789.9, overlap = 9.75
PHY-3002 : Step(81): len = 68609.8, overlap = 9.75
PHY-3002 : Step(82): len = 68911.5, overlap = 9.75
PHY-3002 : Step(83): len = 68880.8, overlap = 9.625
PHY-3002 : Step(84): len = 67787.5, overlap = 7.25
PHY-3002 : Step(85): len = 66921.2, overlap = 7.3125
PHY-3002 : Step(86): len = 66703.7, overlap = 7.3125
PHY-3002 : Step(87): len = 66263.5, overlap = 8.3125
PHY-3002 : Step(88): len = 65918.5, overlap = 8.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00046511
PHY-3002 : Step(89): len = 66225.4, overlap = 8.25
PHY-3002 : Step(90): len = 66375.9, overlap = 8.25
PHY-3002 : Step(91): len = 66418.8, overlap = 10.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000930219
PHY-3002 : Step(92): len = 66551.6, overlap = 10.5
PHY-3002 : Step(93): len = 66578.4, overlap = 10.5
PHY-3001 : Before Legalized: Len = 66578.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006972s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 68521.1, Over = 1.5
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060672s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(94): len = 68480.6, overlap = 13.4062
PHY-3002 : Step(95): len = 66209.2, overlap = 13.5938
PHY-3002 : Step(96): len = 64599.7, overlap = 13.9062
PHY-3002 : Step(97): len = 63555.5, overlap = 14.3438
PHY-3002 : Step(98): len = 62193.7, overlap = 14.8125
PHY-3002 : Step(99): len = 60435, overlap = 14.9688
PHY-3002 : Step(100): len = 58024.7, overlap = 17.0312
PHY-3002 : Step(101): len = 56386.3, overlap = 18.4062
PHY-3002 : Step(102): len = 55200.7, overlap = 19.3125
PHY-3002 : Step(103): len = 53910.1, overlap = 19.625
PHY-3002 : Step(104): len = 52514.6, overlap = 20.7188
PHY-3002 : Step(105): len = 50877.4, overlap = 23.9688
PHY-3002 : Step(106): len = 49274.8, overlap = 25.4062
PHY-3002 : Step(107): len = 48398.6, overlap = 25.2188
PHY-3002 : Step(108): len = 47271.8, overlap = 26.5
PHY-3002 : Step(109): len = 46313.6, overlap = 27.375
PHY-3002 : Step(110): len = 45216.7, overlap = 27.5312
PHY-3002 : Step(111): len = 44602, overlap = 28.125
PHY-3002 : Step(112): len = 44014.1, overlap = 27.875
PHY-3002 : Step(113): len = 43219.6, overlap = 26.4062
PHY-3002 : Step(114): len = 42956.8, overlap = 27.2812
PHY-3002 : Step(115): len = 42840.3, overlap = 26.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000240141
PHY-3002 : Step(116): len = 42682.4, overlap = 27.25
PHY-3002 : Step(117): len = 42453.9, overlap = 27.6562
PHY-3002 : Step(118): len = 42378.7, overlap = 27.4688
PHY-3002 : Step(119): len = 42336.7, overlap = 27.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000480281
PHY-3002 : Step(120): len = 42105.1, overlap = 27.0625
PHY-3002 : Step(121): len = 42199.4, overlap = 26.8438
PHY-3002 : Step(122): len = 42314.9, overlap = 26.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060239s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.62498e-05
PHY-3002 : Step(123): len = 43113.6, overlap = 61.5312
PHY-3002 : Step(124): len = 43982.6, overlap = 60.2812
PHY-3002 : Step(125): len = 44569.6, overlap = 59.375
PHY-3002 : Step(126): len = 44260.7, overlap = 52.375
PHY-3002 : Step(127): len = 44200.7, overlap = 52.4688
PHY-3002 : Step(128): len = 44496.1, overlap = 52.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001525
PHY-3002 : Step(129): len = 44480.4, overlap = 52.1562
PHY-3002 : Step(130): len = 45671.3, overlap = 48.7812
PHY-3002 : Step(131): len = 46201.4, overlap = 43.7812
PHY-3002 : Step(132): len = 46647.1, overlap = 42.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000304999
PHY-3002 : Step(133): len = 46812.9, overlap = 44.2812
PHY-3002 : Step(134): len = 48404, overlap = 35.2188
PHY-3002 : Step(135): len = 49127.7, overlap = 32.6875
PHY-3002 : Step(136): len = 49132.7, overlap = 28.1562
PHY-3002 : Step(137): len = 49130.1, overlap = 26.0312
PHY-3002 : Step(138): len = 48982.2, overlap = 25.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7340, tnet num: 2057, tinst num: 1525, tnode num: 10320, tedge num: 12490.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.03 peak overflow 2.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51232, over cnt = 254(0%), over = 896, worst = 13
PHY-1001 : End global iterations;  0.074879s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (166.9%)

PHY-1001 : Congestion index: top1 = 38.38, top5 = 24.49, top10 = 15.67, top15 = 11.17.
PHY-1001 : End incremental global routing;  0.126319s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (136.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066005s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (94.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.222804s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (119.2%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1595/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51232, over cnt = 254(0%), over = 896, worst = 13
PHY-1002 : len = 56136, over cnt = 172(0%), over = 390, worst = 9
PHY-1002 : len = 60144, over cnt = 26(0%), over = 28, worst = 2
PHY-1002 : len = 60576, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 60832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094654s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (148.6%)

PHY-1001 : Congestion index: top1 = 33.28, top5 = 24.47, top10 = 17.38, top15 = 12.69.
OPT-1001 : End congestion update;  0.140172s wall, 0.125000s user + 0.062500s system = 0.187500s CPU (133.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058290s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.202656s wall, 0.187500s user + 0.062500s system = 0.250000s CPU (123.4%)

OPT-1001 : Current memory(MB): used = 210, reserve = 178, peak = 210.
OPT-1001 : End physical optimization;  0.678874s wall, 0.656250s user + 0.109375s system = 0.765625s CPU (112.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 382 LUT to BLE ...
SYN-4008 : Packed 382 LUT and 180 SEQ to BLE.
SYN-4003 : Packing 718 remaining SEQ's ...
SYN-4005 : Packed 86 SEQ with LUT/SLICE
SYN-4006 : 131 single LUT's are left
SYN-4006 : 632 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1014/1325 primitive instances ...
PHY-3001 : End packing;  0.048354s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (129.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 780 instances
RUN-1001 : 364 mslices, 365 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-1001 : 1341 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 778 instances, 729 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 49066.4, Over = 51.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6202, tnet num: 1885, tinst num: 778, tnode num: 8388, tedge num: 10979.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.298146s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.29118e-05
PHY-3002 : Step(139): len = 48247.1, overlap = 53
PHY-3002 : Step(140): len = 47310.8, overlap = 52.5
PHY-3002 : Step(141): len = 46821.4, overlap = 53
PHY-3002 : Step(142): len = 46516.5, overlap = 50.75
PHY-3002 : Step(143): len = 46385.2, overlap = 50.25
PHY-3002 : Step(144): len = 46133.3, overlap = 49.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.58237e-05
PHY-3002 : Step(145): len = 46416.2, overlap = 48
PHY-3002 : Step(146): len = 47091.5, overlap = 47.75
PHY-3002 : Step(147): len = 47730.9, overlap = 42.75
PHY-3002 : Step(148): len = 48085.1, overlap = 42.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131647
PHY-3002 : Step(149): len = 48461.3, overlap = 42
PHY-3002 : Step(150): len = 49066.7, overlap = 40.75
PHY-3001 : Before Legalized: Len = 49066.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.072309s wall, 0.078125s user + 0.156250s system = 0.234375s CPU (324.1%)

PHY-3001 : After Legalized: Len = 60549.3, Over = 0
PHY-3001 : Trial Legalized: Len = 60549.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050137s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000722843
PHY-3002 : Step(151): len = 58007.5, overlap = 6
PHY-3002 : Step(152): len = 56071.6, overlap = 11.25
PHY-3002 : Step(153): len = 54756, overlap = 13.75
PHY-3002 : Step(154): len = 54207.4, overlap = 14.75
PHY-3002 : Step(155): len = 53437.3, overlap = 18.25
PHY-3002 : Step(156): len = 53021.6, overlap = 19
PHY-3002 : Step(157): len = 52813.5, overlap = 19
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00144569
PHY-3002 : Step(158): len = 53113.3, overlap = 20.25
PHY-3002 : Step(159): len = 53150, overlap = 19.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00289137
PHY-3002 : Step(160): len = 53288.5, overlap = 19.5
PHY-3002 : Step(161): len = 53288.5, overlap = 19.5
PHY-3001 : Before Legalized: Len = 53288.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005525s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 57523.5, Over = 0
PHY-3001 : Legalized: Len = 57523.5, Over = 0
PHY-3001 : Spreading special nets. 12 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005602s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 7, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 57639.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6202, tnet num: 1885, tinst num: 778, tnode num: 8388, tedge num: 10979.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64064, over cnt = 166(0%), over = 263, worst = 7
PHY-1002 : len = 65280, over cnt = 92(0%), over = 115, worst = 5
PHY-1002 : len = 66552, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 66816, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138730s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (191.5%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.73, top10 = 17.53, top15 = 13.52.
PHY-1001 : End incremental global routing;  0.190794s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (163.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059439s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.279434s wall, 0.343750s user + 0.062500s system = 0.406250s CPU (145.4%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1666/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005297s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.73, top10 = 17.53, top15 = 13.52.
OPT-1001 : End congestion update;  0.054044s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051116s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (61.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 738 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 778 instances, 729 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 57646.4, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006776s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 57656.4, Over = 0
PHY-3001 : End incremental legalization;  0.039375s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (198.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.162065s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (106.1%)

OPT-1001 : Current memory(MB): used = 217, reserve = 184, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049357s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1658/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011447s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.71, top10 = 17.50, top15 = 13.50.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059439s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.891017s wall, 0.953125s user + 0.078125s system = 1.031250s CPU (115.7%)

RUN-1003 : finish command "place" in  5.296639s wall, 7.265625s user + 3.421875s system = 10.687500s CPU (201.8%)

RUN-1004 : used memory is 194 MB, reserved memory is 161 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 780 instances
RUN-1001 : 364 mslices, 365 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-1001 : 1341 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6202, tnet num: 1885, tinst num: 778, tnode num: 8388, tedge num: 10979.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 364 mslices, 365 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63264, over cnt = 173(0%), over = 270, worst = 7
PHY-1002 : len = 64552, over cnt = 109(0%), over = 133, worst = 5
PHY-1002 : len = 66208, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 66360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139266s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (145.9%)

PHY-1001 : Congestion index: top1 = 30.54, top5 = 22.59, top10 = 17.43, top15 = 13.42.
PHY-1001 : End global routing;  0.190367s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (131.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 200, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.223482s wall, 3.156250s user + 0.062500s system = 3.218750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34104, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.068391s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (93.6%)

PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End phase 1; 1.074355s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (93.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177728, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.263891s wall, 2.312500s user + 0.109375s system = 2.421875s CPU (191.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.283   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.173   |  -12.321  |  14   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.341125s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.605106s wall, 2.656250s user + 0.109375s system = 2.765625s CPU (172.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177728, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015813s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177640, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024969s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (62.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177656, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023364s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.283   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.173   |  -12.321  |  14   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350104s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.162919s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.706769s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 177656
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.009647s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.0%)

PHY-1001 : End detail routing;  6.799199s wall, 7.703125s user + 0.171875s system = 7.875000s CPU (115.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6202, tnet num: 1885, tinst num: 778, tnode num: 8388, tedge num: 10979.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.659328s wall, 8.593750s user + 0.203125s system = 8.796875s CPU (114.9%)

RUN-1004 : used memory is 498 MB, reserved memory is 474 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      776   out of  19600    3.96%
#reg                      987   out of  19600    5.04%
#le                      1408
  #lut only               421   out of   1408   29.90%
  #reg only               632   out of   1408   44.89%
  #lut&reg                355   out of   1408   25.21%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         449
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    46
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1408   |580     |196     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1050   |289     |134     |858     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |33      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |432    |81      |45      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |32      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |8       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |11     |5       |0       |11      |0       |0       |
|    integ                   |Integration                                      |141    |26      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |99     |39      |14      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |94      |46      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |131    |119     |7       |61      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |73     |73      |0       |28      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |66      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1302  
    #2          2       300   
    #3          3       122   
    #4          4        16   
    #5        5-10       73   
    #6        11-50      29   
    #7       101-500     1    
  Average     2.00            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6202, tnet num: 1885, tinst num: 778, tnode num: 8388, tedge num: 10979.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 778
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1887, pip num: 14137
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1222 valid insts, and 37354 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.060223s wall, 17.171875s user + 0.000000s system = 17.171875s CPU (561.1%)

RUN-1004 : used memory is 514 MB, reserved memory is 485 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250709_161725.log"

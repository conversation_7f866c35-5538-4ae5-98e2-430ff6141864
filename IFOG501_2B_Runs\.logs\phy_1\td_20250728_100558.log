============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Jul 28 10:05:58 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1553 instances
RUN-0007 : 370 luts, 943 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2072 nets
RUN-1001 : 1527 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1551 instances, 370 luts, 943 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7392, tnet num: 2070, tinst num: 1551, tnode num: 10491, tedge num: 12492.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.250592s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 531791
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1551.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 467367, overlap = 20.25
PHY-3002 : Step(2): len = 450771, overlap = 15.75
PHY-3002 : Step(3): len = 439103, overlap = 20.25
PHY-3002 : Step(4): len = 427082, overlap = 18
PHY-3002 : Step(5): len = 417254, overlap = 13.5
PHY-3002 : Step(6): len = 378316, overlap = 18
PHY-3002 : Step(7): len = 362434, overlap = 11.25
PHY-3002 : Step(8): len = 357047, overlap = 11.25
PHY-3002 : Step(9): len = 347603, overlap = 11.25
PHY-3002 : Step(10): len = 340460, overlap = 13.5
PHY-3002 : Step(11): len = 330798, overlap = 11.25
PHY-3002 : Step(12): len = 323978, overlap = 11.25
PHY-3002 : Step(13): len = 316251, overlap = 11.25
PHY-3002 : Step(14): len = 308894, overlap = 11.25
PHY-3002 : Step(15): len = 299720, overlap = 13.5
PHY-3002 : Step(16): len = 295299, overlap = 13.5
PHY-3002 : Step(17): len = 286665, overlap = 13.5
PHY-3002 : Step(18): len = 281009, overlap = 13.5
PHY-3002 : Step(19): len = 275178, overlap = 13.5
PHY-3002 : Step(20): len = 270004, overlap = 13.5
PHY-3002 : Step(21): len = 261973, overlap = 13.5
PHY-3002 : Step(22): len = 257204, overlap = 11.25
PHY-3002 : Step(23): len = 252027, overlap = 11.25
PHY-3002 : Step(24): len = 244320, overlap = 11.25
PHY-3002 : Step(25): len = 237853, overlap = 13.5
PHY-3002 : Step(26): len = 235563, overlap = 13.5
PHY-3002 : Step(27): len = 224650, overlap = 20.25
PHY-3002 : Step(28): len = 214087, overlap = 20.25
PHY-3002 : Step(29): len = 210709, overlap = 20.25
PHY-3002 : Step(30): len = 207452, overlap = 20.25
PHY-3002 : Step(31): len = 159787, overlap = 18
PHY-3002 : Step(32): len = 154097, overlap = 20.25
PHY-3002 : Step(33): len = 152469, overlap = 20.25
PHY-3002 : Step(34): len = 147587, overlap = 20.25
PHY-3002 : Step(35): len = 130235, overlap = 20.25
PHY-3002 : Step(36): len = 127210, overlap = 20.25
PHY-3002 : Step(37): len = 124047, overlap = 20.25
PHY-3002 : Step(38): len = 120750, overlap = 20.25
PHY-3002 : Step(39): len = 117575, overlap = 20.25
PHY-3002 : Step(40): len = 114733, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102584
PHY-3002 : Step(41): len = 115697, overlap = 18
PHY-3002 : Step(42): len = 114350, overlap = 18
PHY-3002 : Step(43): len = 113471, overlap = 15.75
PHY-3002 : Step(44): len = 111824, overlap = 13.5
PHY-3002 : Step(45): len = 108489, overlap = 13.5
PHY-3002 : Step(46): len = 106626, overlap = 11.25
PHY-3002 : Step(47): len = 105164, overlap = 11.25
PHY-3002 : Step(48): len = 101481, overlap = 13.5
PHY-3002 : Step(49): len = 100658, overlap = 9
PHY-3002 : Step(50): len = 98693.7, overlap = 11.25
PHY-3002 : Step(51): len = 96563.8, overlap = 13.5
PHY-3002 : Step(52): len = 94100.4, overlap = 13.5
PHY-3002 : Step(53): len = 93415.2, overlap = 13.5
PHY-3002 : Step(54): len = 90896.3, overlap = 13.5
PHY-3002 : Step(55): len = 89571.5, overlap = 11.25
PHY-3002 : Step(56): len = 87754.3, overlap = 13.5
PHY-3002 : Step(57): len = 86477.5, overlap = 13.5
PHY-3002 : Step(58): len = 85458.7, overlap = 13.5
PHY-3002 : Step(59): len = 85006.8, overlap = 13.5
PHY-3002 : Step(60): len = 83378.6, overlap = 13.5
PHY-3002 : Step(61): len = 82058.8, overlap = 13.5
PHY-3002 : Step(62): len = 78573.9, overlap = 13.5
PHY-3002 : Step(63): len = 78693.5, overlap = 13.5
PHY-3002 : Step(64): len = 77349, overlap = 13.5
PHY-3002 : Step(65): len = 76563.8, overlap = 13.5
PHY-3002 : Step(66): len = 75508.4, overlap = 13.5
PHY-3002 : Step(67): len = 73171.1, overlap = 13.5
PHY-3002 : Step(68): len = 72908.7, overlap = 13.5
PHY-3002 : Step(69): len = 71775.4, overlap = 13.875
PHY-3002 : Step(70): len = 70464.3, overlap = 16.5
PHY-3002 : Step(71): len = 67657, overlap = 14
PHY-3002 : Step(72): len = 67014.4, overlap = 15.25
PHY-3002 : Step(73): len = 66372.2, overlap = 15.375
PHY-3002 : Step(74): len = 66070.1, overlap = 15.125
PHY-3002 : Step(75): len = 65722.2, overlap = 15.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205168
PHY-3002 : Step(76): len = 66027.4, overlap = 15.0625
PHY-3002 : Step(77): len = 66003.9, overlap = 15.0625
PHY-3002 : Step(78): len = 65794.7, overlap = 15.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000410336
PHY-3002 : Step(79): len = 66149.4, overlap = 15.0625
PHY-3002 : Step(80): len = 66051, overlap = 15.125
PHY-3002 : Step(81): len = 66014.9, overlap = 12.875
PHY-3001 : Before Legalized: Len = 66014.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007629s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 69949.2, Over = 1.625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064840s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (72.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 69808.9, overlap = 10.9688
PHY-3002 : Step(83): len = 68904.2, overlap = 10.7812
PHY-3002 : Step(84): len = 67535.4, overlap = 10.8438
PHY-3002 : Step(85): len = 66765.8, overlap = 10.9062
PHY-3002 : Step(86): len = 65396.9, overlap = 10.4375
PHY-3002 : Step(87): len = 63921.3, overlap = 11.5938
PHY-3002 : Step(88): len = 62744.6, overlap = 12.1875
PHY-3002 : Step(89): len = 61962.9, overlap = 12.4062
PHY-3002 : Step(90): len = 60904.5, overlap = 13.3125
PHY-3002 : Step(91): len = 59713.7, overlap = 10.1875
PHY-3002 : Step(92): len = 58220.9, overlap = 10.25
PHY-3002 : Step(93): len = 57082.8, overlap = 10.5625
PHY-3002 : Step(94): len = 56446.8, overlap = 12.4062
PHY-3002 : Step(95): len = 55693.6, overlap = 12.9062
PHY-3002 : Step(96): len = 54637.7, overlap = 14.0312
PHY-3002 : Step(97): len = 54021.8, overlap = 14.1562
PHY-3002 : Step(98): len = 53261.1, overlap = 14.2188
PHY-3002 : Step(99): len = 52790.8, overlap = 14.4062
PHY-3002 : Step(100): len = 51700.9, overlap = 16.6875
PHY-3002 : Step(101): len = 51115, overlap = 15.1562
PHY-3002 : Step(102): len = 50885.9, overlap = 15.4375
PHY-3002 : Step(103): len = 50137.7, overlap = 15.4375
PHY-3002 : Step(104): len = 50149.1, overlap = 15.5312
PHY-3002 : Step(105): len = 49693.7, overlap = 17.6562
PHY-3002 : Step(106): len = 49498.5, overlap = 18.375
PHY-3002 : Step(107): len = 49672.5, overlap = 18.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00100693
PHY-3002 : Step(108): len = 49340.6, overlap = 18.25
PHY-3002 : Step(109): len = 49089.9, overlap = 17.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00201385
PHY-3002 : Step(110): len = 49191.4, overlap = 17.9688
PHY-3002 : Step(111): len = 49226.3, overlap = 17.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062402s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.73052e-05
PHY-3002 : Step(112): len = 49035.4, overlap = 53.9062
PHY-3002 : Step(113): len = 50030.7, overlap = 45.625
PHY-3002 : Step(114): len = 50684, overlap = 43.3125
PHY-3002 : Step(115): len = 50462.5, overlap = 42.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011461
PHY-3002 : Step(116): len = 50536.4, overlap = 37.7812
PHY-3002 : Step(117): len = 51027.4, overlap = 37.75
PHY-3002 : Step(118): len = 51401.1, overlap = 36.3125
PHY-3002 : Step(119): len = 51316.8, overlap = 36.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000229221
PHY-3002 : Step(120): len = 51790.3, overlap = 35.8125
PHY-3002 : Step(121): len = 53341.6, overlap = 32.7812
PHY-3002 : Step(122): len = 53781.2, overlap = 31.625
PHY-3002 : Step(123): len = 53813.6, overlap = 30.5312
PHY-3002 : Step(124): len = 53693.3, overlap = 29.375
PHY-3002 : Step(125): len = 53222.6, overlap = 28.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7392, tnet num: 2070, tinst num: 1551, tnode num: 10491, tedge num: 12492.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.81 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2072.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56376, over cnt = 223(0%), over = 989, worst = 19
PHY-1001 : End global iterations;  0.068732s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (113.7%)

PHY-1001 : Congestion index: top1 = 44.27, top5 = 24.83, top10 = 15.72, top15 = 11.17.
PHY-1001 : End incremental global routing;  0.120923s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (116.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065396s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (95.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215244s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (108.9%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1510/2072.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56376, over cnt = 223(0%), over = 989, worst = 19
PHY-1002 : len = 63184, over cnt = 140(0%), over = 389, worst = 15
PHY-1002 : len = 66960, over cnt = 21(0%), over = 23, worst = 2
PHY-1002 : len = 67296, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 67344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101929s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.0%)

PHY-1001 : Congestion index: top1 = 36.55, top5 = 24.11, top10 = 17.38, top15 = 12.75.
OPT-1001 : End congestion update;  0.146070s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055766s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205123s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.661596s wall, 0.640625s user + 0.031250s system = 0.671875s CPU (101.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 97 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 658 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1028/1332 primitive instances ...
PHY-3001 : End packing;  0.049440s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 772 instances
RUN-1001 : 361 mslices, 360 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1892 nets
RUN-1001 : 1352 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 770 instances, 721 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53291.8, Over = 54
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6162, tnet num: 1890, tinst num: 770, tnode num: 8391, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288097s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.8454e-05
PHY-3002 : Step(126): len = 52924.5, overlap = 52
PHY-3002 : Step(127): len = 52398.6, overlap = 54
PHY-3002 : Step(128): len = 52365.6, overlap = 54
PHY-3002 : Step(129): len = 52482.7, overlap = 53.5
PHY-3002 : Step(130): len = 52199.9, overlap = 54.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.6908e-05
PHY-3002 : Step(131): len = 52345.4, overlap = 53.5
PHY-3002 : Step(132): len = 52792.1, overlap = 53.75
PHY-3002 : Step(133): len = 53369.7, overlap = 53.25
PHY-3002 : Step(134): len = 53624.5, overlap = 52.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153816
PHY-3002 : Step(135): len = 53962.7, overlap = 48.75
PHY-3002 : Step(136): len = 54819, overlap = 44.25
PHY-3001 : Before Legalized: Len = 54819
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071468s wall, 0.031250s user + 0.062500s system = 0.093750s CPU (131.2%)

PHY-3001 : After Legalized: Len = 67689.8, Over = 0
PHY-3001 : Trial Legalized: Len = 67689.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048733s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00140013
PHY-3002 : Step(137): len = 64666.6, overlap = 5.75
PHY-3002 : Step(138): len = 62882.8, overlap = 10.5
PHY-3002 : Step(139): len = 60924.9, overlap = 14.5
PHY-3002 : Step(140): len = 59741.3, overlap = 17.75
PHY-3002 : Step(141): len = 58876.5, overlap = 19.25
PHY-3002 : Step(142): len = 58242.3, overlap = 21
PHY-3002 : Step(143): len = 57884.5, overlap = 23.75
PHY-3002 : Step(144): len = 57683.4, overlap = 23.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00280027
PHY-3002 : Step(145): len = 57769, overlap = 23.5
PHY-3002 : Step(146): len = 57853.4, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00560053
PHY-3002 : Step(147): len = 57906.2, overlap = 23
PHY-3002 : Step(148): len = 57906.1, overlap = 22.5
PHY-3001 : Before Legalized: Len = 57906.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004935s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62244, Over = 0
PHY-3001 : Legalized: Len = 62244, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005512s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (283.5%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 62346, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6162, tnet num: 1890, tinst num: 770, tnode num: 8391, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 132/1892.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69256, over cnt = 151(0%), over = 243, worst = 7
PHY-1002 : len = 70064, over cnt = 68(0%), over = 91, worst = 4
PHY-1002 : len = 70656, over cnt = 38(0%), over = 45, worst = 4
PHY-1002 : len = 71352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120628s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (181.3%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 23.11, top10 = 17.86, top15 = 13.78.
PHY-1001 : End incremental global routing;  0.174225s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (152.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063332s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.266598s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (134.8%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1892.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006495s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 23.11, top10 = 17.86, top15 = 13.78.
OPT-1001 : End congestion update;  0.055994s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047838s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.105761s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.4%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049840s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1892.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006812s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 23.11, top10 = 17.86, top15 = 13.78.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047730s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.807971s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (112.2%)

RUN-1003 : finish command "place" in  5.035808s wall, 6.750000s user + 3.187500s system = 9.937500s CPU (197.3%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 772 instances
RUN-1001 : 361 mslices, 360 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1892 nets
RUN-1001 : 1352 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6162, tnet num: 1890, tinst num: 770, tnode num: 8391, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 361 mslices, 360 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1890 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67576, over cnt = 146(0%), over = 225, worst = 6
PHY-1002 : len = 68560, over cnt = 90(0%), over = 116, worst = 4
PHY-1002 : len = 69960, over cnt = 9(0%), over = 12, worst = 3
PHY-1002 : len = 70152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117859s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (106.1%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 22.88, top10 = 17.59, top15 = 13.57.
PHY-1001 : End global routing;  0.167042s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 199, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.188913s wall, 3.171875s user + 0.015625s system = 3.187500s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31008, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.090657s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.097178s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 171728, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 529.
PHY-1001 : End initial routed; 1.453635s wall, 2.375000s user + 0.078125s system = 2.453125s CPU (168.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.689  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.343049s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 1.796788s wall, 2.718750s user + 0.078125s system = 2.796875s CPU (155.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 171728, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015034s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 171648, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023371s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (200.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 171760, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027135s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.689  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.342043s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.169555s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.2%)

PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End phase 3; 0.703595s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (102.2%)

PHY-1003 : Routed, final wirelength = 171760
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.012282s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (127.2%)

PHY-1001 : End detail routing;  6.971843s wall, 7.906250s user + 0.093750s system = 8.000000s CPU (114.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6162, tnet num: 1890, tinst num: 770, tnode num: 8391, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[18] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_76.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_76.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_79.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_82.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_85.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_91.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_91.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_94.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_94.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_97.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_97.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6286, tnet num: 1952, tinst num: 832, tnode num: 8515, tedge num: 10971.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -256ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[0] slack -462ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -84ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -558ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -709ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -699ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -362ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -568ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -594ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -447ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[1] slack -709ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[1] slack -711ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[0] slack -22ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[0] slack -353ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[1] slack -321ps
RUN-1001 : End hold fix;  3.114285s wall, 3.296875s user + 0.390625s system = 3.687500s CPU (118.4%)

RUN-1003 : finish command "route" in  10.573702s wall, 11.703125s user + 0.484375s system = 12.187500s CPU (115.3%)

RUN-1004 : used memory is 499 MB, reserved memory is 471 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      874   out of  19600    4.46%
#reg                     1019   out of  19600    5.20%
#le                      1532
  #lut only               513   out of   1532   33.49%
  #reg only               658   out of   1532   42.95%
  #lut&reg                361   out of   1532   23.56%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         459
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1532   |685     |189     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1040   |282     |128     |829     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |16      |7       |18      |0       |0       |
|    demodu                  |Demodulation                                     |458    |104     |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |29      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |7       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |6       |0       |12      |0       |0       |
|    integ                   |Integration                                      |143    |33      |15      |115     |0       |0       |
|    modu                    |Modulation                                       |66     |34      |7       |64      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |69      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |163    |128     |7       |112     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |104    |82      |0       |80      |0       |0       |
|  wendu                     |DS18B20                                          |311    |266     |45      |77      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1375  
    #2          2       303   
    #3          3       108   
    #4          4        26   
    #5        5-10       67   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6286, tnet num: 1952, tinst num: 832, tnode num: 8515, tedge num: 10971.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 832
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1954, pip num: 14330
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 14
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1310 valid insts, and 38366 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.200211s wall, 18.218750s user + 0.062500s system = 18.281250s CPU (571.3%)

RUN-1004 : used memory is 518 MB, reserved memory is 489 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250728_100558.log"

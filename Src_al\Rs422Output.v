`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// 公司: tianhailu
// 工程师: Andrew
//
// 创建日期:    	15:33:33 08/03/2022
// 设计名称: 	IFOG501_2B
// 模块名称:    	Rs422Output (增强版本，支持温度补偿)
// 项目名称: 	IFOG501_2B
// 目标器件:
// 工具版本: 	TD5.6.2-64bit
// 描述: 	增强的Rs422输出模块，具有改进的边沿检测和温度补偿功能
// 版本 2.00 - 针对全温范围工作进行增强
// 附加说明:
//时序图
//   ___
//  |   |
//__|   |_________________________________________//clk_DA 时钟
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid AD有效信号
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate 解调信号
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate 积分信号
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive 输出驱动信号
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate 调制信号
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////

module Rs422Output
#(
	parameter bPOLAR=1,				// 极性选择参数
	parameter iWID_IN=56,			// 输入数据位宽
	parameter iWID_OUT=32,			// 输出数据位宽
	parameter iDELAYED=150,			// 基础延迟参数
	parameter iOUTPUT_SCALE=1350,	// 输出缩放系数
	parameter FILTER_DEPTH=16,		// 增强的滤波深度，提高抗噪声能力
	parameter TEMP_COMP_EN=1		// 使能温度补偿功能
)
(
	input						rst_n,			// 复位信号，低电平有效
	input						clk, 			// 120MHz参考时钟
	input						output_drive,	// 输出驱动信号
	input						RxTransmit, 	// 外部触发信号，100Hz同步信号
	input						polarity,		// 极性信号
	input		[iWID_IN-1:0]	din, 			// 输入角速度数据
	input		[15:0]			temp_data,		// 温度数据，用于温度补偿
	output reg					transmit,  		// 发送时钟信号给picoblaze
	output reg 	[iWID_OUT-1:0]	dout,  	  		// 输出数据
	output reg					signal_quality_ok	// 信号质量指示器
);
////////////////////////////////////////////////////////////////////////////////
// 增强的边沿检测，采用更深的滤波
reg [FILTER_DEPTH-1:0] 	RxTr_filter;		// 增强的RxTransmit滤波寄存器
reg [7:0] 				RxTr_dy;			// 兼容性寄存器
reg	[7:0]				output_filter;  	// 增强的output_drive滤波寄存器
reg	[1:0]				output_dy;  		// 兼容性寄存器
reg						rx_edge_detected;	// RxTransmit边沿检测标志
reg						output_edge_detected;// output_drive边沿检测标志
reg [3:0]				edge_confirm_cnt;	// 边沿确认计数器
reg [7:0]				noise_counter;		// 噪声检测计数器
// 温度补偿相关变量
reg [15:0]			temp_comp_delay;	// 温度补偿后的延迟值
reg [7:0]			base_delay;			// 基础延迟值
reg signed [15:0]	temp_offset;		// 相对25°C的温度偏移
reg [7:0]			delay_adjustment;	// 计算得到的延迟调整量

reg [7:0]			count_pos;			// 正向准备数据计数器
reg [79:0]			p_sum;				// 速度累加和
reg [iWID_IN-1:0]	sum; 				// 速度累加和
reg [iWID_IN-1:0]	din_temp;			// 输入数据缓存
reg [iWID_OUT-1:0] 	RS_dout;			// RS422输出数据
reg	[iWID_IN-1:0]	sum_dy;				// 累加和延迟寄存器
reg	[79:0]			p_sum_dy;			// 乘积累加和延迟寄存器
reg [7:0]			stable_window_cnt;	// 稳定积分窗口计数器
reg					integration_valid;	// 积分窗口有效性标志
////////////////////////////////////////////////////////////////////////////////
// 状态机相关变量
reg	[3:0]	next_state;			// 下一状态
reg	[3:0]	trans_state;		// 当前状态
reg	[3:0]	dalay_cout;			// 延迟计数器
localparam	idle_s 				= 4'd0 ;	// 空闲状态
localparam	wait_1us_s 			= 4'd1 ;	// 等待1微秒状态
localparam	dalay_state 		= 4'd2 ;	// 延迟状态
localparam	check_data_stable_s = 4'd3 ;	// 检查数据稳定状态
localparam	transmit_data_s 	= 4'd4 ;	// 发送数据状态
localparam	clear_data_s 		= 4'd5 ;	// 清除数据状态

// 增强的边沿检测，具有噪声滤波和确认机制
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		RxTr_filter <= {FILTER_DEPTH{1'b0}};	// 清零滤波寄存器
		output_filter <= 8'h00;					// 清零输出滤波寄存器
		RxTr_dy <= 8'h00;						// 清零兼容性寄存器
		output_dy <= 2'b00;						// 清零输出延迟寄存器
		rx_edge_detected <= 1'b0;				// 清零接收边沿检测标志
		output_edge_detected <= 1'b0;			// 清零输出边沿检测标志
		edge_confirm_cnt <= 4'h0;				// 清零边沿确认计数器
		noise_counter <= 8'h00;					// 清零噪声计数器
		signal_quality_ok <= 1'b0;				// 清零信号质量标志
	end
	else begin
		// 使用更深的移位寄存器进行增强滤波
		RxTr_filter <= {RxTr_filter[FILTER_DEPTH-2:0], RxTransmit};
		output_filter <= {output_filter[6:0], output_drive};

		// 兼容性寄存器，保持与原代码的兼容性
		RxTr_dy <= RxTr_filter[FILTER_DEPTH-1:FILTER_DEPTH-8];
		output_dy <= output_filter[1:0];

		// 使用多数表决的增强边沿检测
		if(majority_vote(RxTr_filter[FILTER_DEPTH-1:FILTER_DEPTH-8]) == 2'b01) begin
			if(edge_confirm_cnt < 4'hF)
				edge_confirm_cnt <= edge_confirm_cnt + 1'b1;
			rx_edge_detected <= (edge_confirm_cnt >= 4'h3); // 需要3次以上确认
		end
		else begin
			edge_confirm_cnt <= 4'h0;
			rx_edge_detected <= 1'b0;
		end

		// 输出驱动边沿检测，带滤波功能
		output_edge_detected <= (output_filter[3:2] == 2'b01) &&
								(output_filter[7:4] == 4'b0000); // 确保干净的跳变

		// 信号质量监控
		if(RxTr_filter[FILTER_DEPTH-1] != RxTr_filter[FILTER_DEPTH-2]) begin
			if(noise_counter < 8'hFF)
				noise_counter <= noise_counter + 1'b1;
		end
		else if(noise_counter > 8'h00)
			noise_counter <= noise_counter - 1'b1;

		signal_quality_ok <= (noise_counter < 8'h10); // 低噪声时信号质量良好
	end
end

// 滤波信号的多数表决函数
function [1:0] majority_vote;
	input [7:0] filter_reg;		// 输入滤波寄存器
	reg [3:0] ones_count;		// 高电平计数
	integer i;
	begin
		ones_count = 4'h0;
		// 统计滤波窗口内的高电平数量
		for(i = 0; i < 8; i = i + 1) begin
			if(filter_reg[i])
				ones_count = ones_count + 1'b1;
		end

		if(ones_count >= 4'h6)
			majority_vote = 2'b11;  // 强高电平
		else if(ones_count >= 4'h3)
			majority_vote = 2'b01;  // 上升沿候选
		else if(ones_count >= 4'h2)
			majority_vote = 2'b10;  // 下降沿候选
		else
			majority_vote = 2'b00;  // 强低电平
	end
endfunction

// 温度补偿逻辑
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		temp_comp_delay <= iDELAYED;		// 初始化温度补偿延迟
		base_delay <= iDELAYED;				// 初始化基础延迟
		temp_offset <= 16'h0000;			// 初始化温度偏移
		delay_adjustment <= 8'h00;			// 初始化延迟调整量
	end
	else if(TEMP_COMP_EN)begin
		// 计算相对于25°C（参考温度）的温度偏移
		// temp_data格式：有符号16位，0.1°C分辨率
		temp_offset <= temp_data - 16'd250; // 25.0°C = 250（0.1°C单位）

		// 温度系数：大约每度0.1个时钟周期
		// 对于光纤系统，典型温度系数约为0.01%/°C
		if(temp_offset[15]) begin // 负温度（低于25°C）
			delay_adjustment <= (~temp_offset[7:0] + 1'b1) >> 4; // 除以16进行缩放
			temp_comp_delay <= base_delay + delay_adjustment;
		end
		else begin // 正温度（高于25°C）
			delay_adjustment <= temp_offset[7:0] >> 4; // 除以16进行缩放
			temp_comp_delay <= (base_delay > delay_adjustment) ?
							   (base_delay - delay_adjustment) : base_delay;
		end
	end
	else begin
		temp_comp_delay <= iDELAYED; // 如果禁用补偿则使用固定延迟
	end
end

//增强的三状态机，具有改进的同步机制
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		trans_state <= idle_s;		// 复位时进入空闲状态
	end
	else begin
		trans_state <= next_state;	// 状态转换
	end
end

// 状态机组合逻辑
always@(*)begin
	case(trans_state)
		idle_s:	begin
					// 增强的边沿检测，带信号质量检查
					if(rx_edge_detected && signal_quality_ok)begin
						next_state <= wait_1us_s;
					end
					else begin
						next_state <= idle_s;
					end
				end
		wait_1us_s:begin
					// 使用温度补偿后的延迟
					if(count_pos >= (temp_comp_delay[7:0] - 8'd5))
						next_state <= dalay_state;
					else
						next_state <= wait_1us_s;
				end
		dalay_state:begin
					// 扩展延迟以提高稳定性
					if(dalay_cout >= 4'd15) // 从12增加到15
						next_state <= check_data_stable_s;
					else
						next_state <= dalay_state;
				end
		check_data_stable_s:begin
					// 增强的同步检查，包含多个条件
					if(output_edge_detected &&
					   (polarity == 1'b1) &&
					   signal_quality_ok &&
					   integration_valid)begin
						next_state <= transmit_data_s;
					end
					else begin
						next_state <= check_data_stable_s;
					end
				end
		transmit_data_s:next_state <= clear_data_s;	// 发送数据后清除
		clear_data_s:	next_state <= idle_s;		// 清除数据后回到空闲
		default:		next_state <= idle_s;		// 默认状态
	endcase
end

//数据有效状态：发送信号
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		transmit <= 1'b0;		// 复位时清零发送信号
	end
	else if((trans_state == transmit_data_s)||(trans_state == clear_data_s))begin
		transmit <= 1'b1;		// 在发送和清除状态时置高
	end
	else begin
		transmit <= 1'b0;		// 其他状态时清零
	end
end

//计数1毫秒
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		count_pos <= 8'd0;		// 复位时清零计数器
	end
	else if(trans_state == wait_1us_s)begin
		count_pos <= count_pos + 1'b1;	// 在等待状态时递增
	end
	else begin
		count_pos <= 8'd0;		// 其他状态时清零
	end
end

//延迟4个时钟周期(120MHz)
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		dalay_cout <= 4'd0;		// 复位时清零延迟计数器
	end
	else if(trans_state == dalay_state)begin
		dalay_cout <= dalay_cout + 1'b1;	// 在延迟状态时递增
	end
	else begin
		dalay_cout <= 4'd0;		// 其他状态时清零
	end
end

// 增强的积分窗口控制，带稳定性监控
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		stable_window_cnt <= 8'h00;	// 复位时清零稳定窗口计数器
		integration_valid <= 1'b0;		// 复位时清零积分有效标志
	end
	else begin
		// 监控积分窗口稳定性
		if((trans_state == idle_s) || (trans_state == wait_1us_s))begin
			if(stable_window_cnt < 8'hFF)
				stable_window_cnt <= stable_window_cnt + 1'b1;	// 在稳定期间递增
		end
		else begin
			stable_window_cnt <= 8'h00;	// 非稳定期间清零
		end

		// 只有在足够稳定的窗口后积分才有效
		integration_valid <= (stable_window_cnt >= 8'h10) && signal_quality_ok;
	end
end

//增强的输入数据缓存，带验证功能
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		din_temp <= {iWID_IN{1'b0}};	// 复位时清零输入缓存
	end
	else if(polarity && integration_valid)begin
		din_temp <= din;				// 在极性正确且积分有效时缓存数据
	end
	// 如果条件不满足则保持之前的值
end

//增强的输入角速度积分，带窗口保护
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		sum <= {iWID_IN{1'b0}};			// 复位时清零累加和
	end
	else if(trans_state == clear_data_s)begin
		sum <= {iWID_IN{1'b0}};			// 清除状态时清零累加和
	end
	else if(((trans_state == idle_s) || (trans_state == wait_1us_s)) &&
			integration_valid && signal_quality_ok)begin
		// 只有在窗口稳定且信号质量良好时才进行积分
		sum <= sum + din_temp;
	end
	// 在不稳定期间保持数值
end

//sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	sum_dy <= (dalay_cout == 4'd3)?sum : sum_dy;
end

always@(posedge clk or negedge rst_n)begin
	if(~rst_n)
		p_sum <= 80'd0;
	else if(dalay_cout == 4'd8)
		p_sum <= sum*iOUTPUT_SCALE;
	else
		p_sum <= p_sum;
end

//p_sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	p_sum_dy <= p_sum;
end

//out data
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		RS_dout <=32'd0;
	end
	else if(trans_state == transmit_data_s)begin
		RS_dout <= p_sum_dy[iWID_OUT+24:25];
	end
	else begin
		RS_dout <= RS_dout;
	end
end

//RS_dout:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	if(bPOLAR)
		dout <= ~RS_dout + 1'b1;
	else 
		dout <= RS_dout;
end

endmodule


`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
//
// Create Date:    	15:33:33 08/03/2022
// Design Name: 	IFOG501_2B
// Module Name:    	Rs422Output (Enhanced with Temperature Compensation)
// Project Name: 	IFOG501_2B
// Target Devices:
// Tool versions: 	TD5.6.2-64bit
// Description: 	Enhanced Rs422Output with improved edge detection and temperature compensation
// Revision 2.00 - Enhanced for full temperature range operation
// Additional Comments:
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////

module Rs422Output
#(
	parameter bPOLAR=1,
	parameter iWID_IN=56,
	parameter iWID_OUT=32,
	parameter iDELAYED=150,
	parameter iOUTPUT_SCALE=1350,
	parameter FILTER_DEPTH=16,		// Enhanced filter depth for better noise immunity
	parameter TEMP_COMP_EN=1		// Enable temperature compensation
)
(
	input						rst_n,
	input						clk, //120MHz reference clock
	input						output_drive,
	input						RxTransmit, //RxTransmit signal,100HZ同步信号
	input						polarity,
	input		[iWID_IN-1:0]	din,
	input		[15:0]			temp_data,	// Temperature data for compensation
	output reg					transmit,  //RxTransmit clock for picoblaze
	output reg 	[iWID_OUT-1:0]	dout,  	  //data out
	output reg					signal_quality_ok	// Signal quality indicator
);
////////////////////////////////////////////////////////////////////////////////
// Enhanced edge detection with deeper filtering
reg [FILTER_DEPTH-1:0] 	RxTr_filter;	// Enhanced RxTransmit filter register
reg [7:0] 				RxTr_dy;		// Legacy compatibility register
reg	[7:0]				output_filter;  // Enhanced output_drive filter
reg	[1:0]				output_dy;  	// Legacy compatibility register
reg						rx_edge_detected;
reg						output_edge_detected;
reg [3:0]				edge_confirm_cnt;	// Edge confirmation counter
reg [7:0]				noise_counter;		// Noise detection counter
// Temperature compensation variables
reg [15:0]			temp_comp_delay;	// Temperature compensated delay
reg [7:0]			base_delay;			// Base delay value
reg signed [15:0]	temp_offset;		// Temperature offset from 25°C
reg [7:0]			delay_adjustment;	// Calculated delay adjustment

reg [7:0]			count_pos;//counter for positive preparing data
reg [79:0]			p_sum;//sum of velocity
reg [iWID_IN-1:0]	sum; //sum of velocity
reg [iWID_IN-1:0]	din_temp;//sum of velocity
reg [iWID_OUT-1:0] 	RS_dout;
reg	[iWID_IN-1:0]	sum_dy;
reg	[79:0]			p_sum_dy;
reg [7:0]			stable_window_cnt;	// Stable integration window counter
reg					integration_valid;	// Integration window validity flag
////////////////////////////////////////////////////////////////////////////////
reg	[3:0]	next_state;
reg	[3:0]	trans_state;
reg	[3:0]	dalay_cout;
localparam	idle_s 				= 4'd0 ;
localparam	wait_1us_s 			= 4'd1 ;
localparam	dalay_state 		= 4'd2 ;
localparam	check_data_stable_s = 4'd3 ;
localparam	transmit_data_s 	= 4'd4 ;
localparam	clear_data_s 		= 4'd5 ;

// Enhanced edge detection with noise filtering and confirmation
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		RxTr_filter <= {FILTER_DEPTH{1'b0}};
		output_filter <= 8'h00;
		RxTr_dy <= 8'h00;
		output_dy <= 2'b00;
		rx_edge_detected <= 1'b0;
		output_edge_detected <= 1'b0;
		edge_confirm_cnt <= 4'h0;
		noise_counter <= 8'h00;
		signal_quality_ok <= 1'b0;
	end
	else begin
		// Enhanced filtering with deeper shift register
		RxTr_filter <= {RxTr_filter[FILTER_DEPTH-2:0], RxTransmit};
		output_filter <= {output_filter[6:0], output_drive};

		// Legacy compatibility registers
		RxTr_dy <= RxTr_filter[FILTER_DEPTH-1:FILTER_DEPTH-8];
		output_dy <= output_filter[1:0];

		// Enhanced edge detection with majority voting
		if(majority_vote(RxTr_filter[FILTER_DEPTH-1:FILTER_DEPTH-8]) == 2'b01) begin
			if(edge_confirm_cnt < 4'hF)
				edge_confirm_cnt <= edge_confirm_cnt + 1'b1;
			rx_edge_detected <= (edge_confirm_cnt >= 4'h3); // Require 3+ confirmations
		end
		else begin
			edge_confirm_cnt <= 4'h0;
			rx_edge_detected <= 1'b0;
		end

		// Output drive edge detection with filtering
		output_edge_detected <= (output_filter[3:2] == 2'b01) &&
								(output_filter[7:4] == 4'b0000); // Ensure clean transition

		// Signal quality monitoring
		if(RxTr_filter[FILTER_DEPTH-1] != RxTr_filter[FILTER_DEPTH-2]) begin
			if(noise_counter < 8'hFF)
				noise_counter <= noise_counter + 1'b1;
		end
		else if(noise_counter > 8'h00)
			noise_counter <= noise_counter - 1'b1;

		signal_quality_ok <= (noise_counter < 8'h10); // Good quality if low noise
	end
end

// Function for majority voting on filtered signal
function [1:0] majority_vote;
	input [7:0] filter_reg;
	reg [3:0] ones_count;
	integer i;
	begin
		ones_count = 4'h0;
		for(i = 0; i < 8; i = i + 1) begin
			if(filter_reg[i])
				ones_count = ones_count + 1'b1;
		end

		if(ones_count >= 4'h6)
			majority_vote = 2'b11;  // Strong high
		else if(ones_count >= 4'h3)
			majority_vote = 2'b01;  // Rising edge candidate
		else if(ones_count >= 4'h2)
			majority_vote = 2'b10;  // Falling edge candidate
		else
			majority_vote = 2'b00;  // Strong low
	end
endfunction

// Temperature compensation logic
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		temp_comp_delay <= iDELAYED;
		base_delay <= iDELAYED;
		temp_offset <= 16'h0000;
		delay_adjustment <= 8'h00;
	end
	else if(TEMP_COMP_EN)begin
		// Calculate temperature offset from 25°C (reference temperature)
		// temp_data format: signed 16-bit, 0.1°C resolution
		temp_offset <= temp_data - 16'd250; // 25.0°C = 250 in 0.1°C units

		// Temperature coefficient: approximately 0.1 clock cycles per degree
		// For fiber optic systems, typical temperature coefficient is ~0.01%/°C
		if(temp_offset[15]) begin // Negative temperature (below 25°C)
			delay_adjustment <= (~temp_offset[7:0] + 1'b1) >> 4; // Divide by 16 for scaling
			temp_comp_delay <= base_delay + delay_adjustment;
		end
		else begin // Positive temperature (above 25°C)
			delay_adjustment <= temp_offset[7:0] >> 4; // Divide by 16 for scaling
			temp_comp_delay <= (base_delay > delay_adjustment) ?
							   (base_delay - delay_adjustment) : base_delay;
		end
	end
	else begin
		temp_comp_delay <= iDELAYED; // Use fixed delay if compensation disabled
	end
end

//Enhanced Three state machine with improved synchronization
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		trans_state <= idle_s;
	end
	else begin
		trans_state <= next_state;
	end
end

always@(*)begin
	case(trans_state)
		idle_s:	begin
					// Enhanced edge detection with signal quality check
					if(rx_edge_detected && signal_quality_ok)begin
						next_state <= wait_1us_s;
					end
					else begin
						next_state <= idle_s;
					end
				end
		wait_1us_s:begin
					// Use temperature compensated delay
					if(count_pos >= (temp_comp_delay[7:0] - 8'd5))
						next_state <= dalay_state;
					else
						next_state <= wait_1us_s;
				end
		dalay_state:begin
					// Extended delay for better stability
					if(dalay_cout >= 4'd15) // Increased from 12 to 15
						next_state <= check_data_stable_s;
					else
						next_state <= dalay_state;
				end
		check_data_stable_s:begin
					// Enhanced synchronization check with multiple conditions
					if(output_edge_detected &&
					   (polarity == 1'b1) &&
					   signal_quality_ok &&
					   integration_valid)begin
						next_state <= transmit_data_s;
					end
					else begin
						next_state <= check_data_stable_s;
					end
				end
		transmit_data_s:next_state <= clear_data_s;
		clear_data_s:	next_state <= idle_s;
		default:		next_state <= idle_s;
	endcase
end

//Valid status of data:transmit
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		transmit <= 1'b0;
	end
	else if((trans_state == transmit_data_s)||(trans_state == clear_data_s))begin
		transmit <= 1'b1;
	end
	else begin
		transmit <= 1'b0;
	end
end

//Count 1 millisecond
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		count_pos <= 8'd0;
	end
	else if(trans_state == wait_1us_s)begin
		count_pos <= count_pos + 1'b1;
	end
	else begin
		count_pos <= 8'd0;
	end
end

//delay 4 clock(120MHz)
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		dalay_cout <= 4'd0;
	end
	else if(trans_state == dalay_state)begin
		dalay_cout <= dalay_cout + 1'b1;
	end
	else begin
		dalay_cout <= 4'd0;
	end
end

// Enhanced integration window control with stability monitoring
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		stable_window_cnt <= 8'h00;
		integration_valid <= 1'b0;
	end
	else begin
		// Monitor integration window stability
		if((trans_state == idle_s) || (trans_state == wait_1us_s))begin
			if(stable_window_cnt < 8'hFF)
				stable_window_cnt <= stable_window_cnt + 1'b1;
		end
		else begin
			stable_window_cnt <= 8'h00;
		end

		// Integration is valid only after sufficient stable window
		integration_valid <= (stable_window_cnt >= 8'h10) && signal_quality_ok;
	end
end

//Enhanced cache input data with validation
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		din_temp <= {iWID_IN{1'b0}};
	end
	else if(polarity && integration_valid)begin
		din_temp <= din;
	end
	// Hold previous value if conditions not met
end

//Enhanced input angular velocity integral with window protection
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		sum <= {iWID_IN{1'b0}};
	end
	else if(trans_state == clear_data_s)begin
		sum <= {iWID_IN{1'b0}};
	end
	else if(((trans_state == idle_s) || (trans_state == wait_1us_s)) &&
			integration_valid && signal_quality_ok)begin
		// Only integrate when window is stable and signal quality is good
		sum <= sum + din_temp;
	end
	// Hold value during unstable periods
end

//sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	sum_dy <= (dalay_cout == 4'd3)?sum : sum_dy;
end

always@(posedge clk or negedge rst_n)begin
	if(~rst_n)
		p_sum <= 80'd0;
	else if(dalay_cout == 4'd8)
		p_sum <= sum*iOUTPUT_SCALE;
	else
		p_sum <= p_sum;
end

//p_sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	p_sum_dy <= p_sum;
end

//out data
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		RS_dout <=32'd0;
	end
	else if(trans_state == transmit_data_s)begin
		RS_dout <= p_sum_dy[iWID_OUT+24:25];
	end
	else begin
		RS_dout <= RS_dout;
	end
end

//RS_dout:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	if(bPOLAR)
		dout <= ~RS_dout + 1'b1;
	else 
		dout <= RS_dout;
end

endmodule


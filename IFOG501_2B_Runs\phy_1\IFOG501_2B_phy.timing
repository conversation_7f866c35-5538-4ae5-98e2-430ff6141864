=========================================================================================================
Auto created by Tang Dynasty v5.6.71036
   Copyright (c) 2012-2023 Anlogic Inc.
Mon Aug 18 16:28:44 2025
=========================================================================================================


Top Model:                IFOG501_2B                                                      
Device:                   eagle_20                                                        
Timing Constraint File:   ../../Constraints/IFOG_11FB.sdc                                 
STA Level:                Detail                                                          
Speed Grade:              NA                                                              

=========================================================================================================
Timing constraint:        clock: clk_in                                                   
Clock = clk_in, period 50ns, rising at 0ns, falling at 25ns

0 endpoints analyzed totally, and 0 paths analyzed
0 errors detected : 0 setup errors (TNS = 0), 0 hold errors (TNS = 0)
Minimum period is 0ns
---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[0]                                  
Clock = CLK120/pll_inst.clkc[0], period 8.333ns, rising at 0ns, falling at 4.166ns

2274 endpoints analyzed totally, and 40218 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 7.751ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg2_syn_207 (106 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.582 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.571ns  (logic 6.240ns, net 1.331ns, 82% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[1]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[1]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.881 r     6.996
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.996      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.128
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.128      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.260
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.260      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.392
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.392      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.524
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.524      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.656
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.656      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.788
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.788      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.920
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.920      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     8.052
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     8.052      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.184
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.184      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.316
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.316      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.448
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.448      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.580
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.580      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[0]                    cell (ADDER)            0.387 r     8.967
 signal_process/demodu/reg2_syn_207.mi[1] (signal_process/demodu/INS_dout_b2[52]) net  (fanout = 1)       0.737 r     9.704      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg1               0.143       9.847
 Arrival time                                                                        9.847                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.582ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.693 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.460ns  (logic 6.129ns, net 1.331ns, 82% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[2]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.b[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[2]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.770 r     6.885
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.885      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.017
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.017      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.149
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.149      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.281
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.281      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.413
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.413      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.545
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.545      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.677
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.677      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.809
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.809      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.941
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.941      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.073
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.073      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.205
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.205      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.337
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.337      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.469
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.469      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[0]                    cell (ADDER)            0.387 r     8.856
 signal_process/demodu/reg2_syn_207.mi[1] (signal_process/demodu/INS_dout_b2[52]) net  (fanout = 1)       0.737 r     9.593      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg1               0.143       9.736
 Arrival time                                                                        9.736                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.693ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.702 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.451ns  (logic 5.976ns, net 1.475ns, 80% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dob[0]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_286.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[9]) net  (fanout = 1)       0.738 r     6.259      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.881 r     7.140
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.140      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.272
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.272      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.404
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.404      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.536
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.536      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.668
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.668      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.800
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.800      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.932
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.932      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.064
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.064      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.196
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.196      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.328
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.328      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.460
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.460      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[0]                    cell (ADDER)            0.387 r     8.847
 signal_process/demodu/reg2_syn_207.mi[1] (signal_process/demodu/INS_dout_b2[52]) net  (fanout = 1)       0.737 r     9.584      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg1               0.143       9.727
 Arrival time                                                                        9.727                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.702ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg2_syn_213 (98 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.697 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_213.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.456ns  (logic 6.108ns, net 1.348ns, 81% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[1]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[1]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.881 r     6.996
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.996      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.128
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.128      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.260
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.260      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.392
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.392      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.524
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.524      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.656
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.656      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.788
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.788      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.920
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.920      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     8.052
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     8.052      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.184
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.184      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.316
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.316      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.448
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.448      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fx[0]                    cell (ADDER)            0.387 r     8.835
 signal_process/demodu/reg2_syn_213.mi[1] (signal_process/demodu/INS_dout_b2[48]) net  (fanout = 1)       0.754 r     9.589      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_213                          path2reg1               0.143       9.732
 Arrival time                                                                        9.732                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_213.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.697ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.808 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_213.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.345ns  (logic 5.997ns, net 1.348ns, 81% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[2]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.b[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[2]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.770 r     6.885
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.885      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.017
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.017      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.149
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.149      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.281
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.281      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.413
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.413      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.545
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.545      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.677
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.677      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.809
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.809      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.941
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.941      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.073
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.073      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.205
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.205      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.337
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.337      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fx[0]                    cell (ADDER)            0.387 r     8.724
 signal_process/demodu/reg2_syn_213.mi[1] (signal_process/demodu/INS_dout_b2[48]) net  (fanout = 1)       0.754 r     9.478      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_213                          path2reg1               0.143       9.621
 Arrival time                                                                        9.621                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_213.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.808ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.817 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_213.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.336ns  (logic 5.844ns, net 1.492ns, 79% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dob[0]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_286.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[9]) net  (fanout = 1)       0.738 r     6.259      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.881 r     7.140
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.140      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.272
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.272      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.404
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.404      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.536
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.536      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.668
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.668      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.800
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.800      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.932
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.932      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.064
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.064      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.196
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.196      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.328
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.328      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fx[0]                    cell (ADDER)            0.387 r     8.715
 signal_process/demodu/reg2_syn_213.mi[1] (signal_process/demodu/INS_dout_b2[48]) net  (fanout = 1)       0.754 r     9.469      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_213                          path2reg1               0.143       9.612
 Arrival time                                                                        9.612                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_213.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.817ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg2_syn_207 (110 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.793 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.360ns  (logic 6.306ns, net 1.054ns, 85% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[1]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[1]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.881 r     6.996
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.996      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.128
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.128      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.260
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.260      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.392
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.392      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.524
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.524      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.656
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.656      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.788
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.788      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.920
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.920      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     8.052
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     8.052      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.184
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.184      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.316
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.316      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.448
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.448      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.580
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.580      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[1]                    cell (ADDER)            0.453 r     9.033
 signal_process/demodu/reg2_syn_207.mi[0] (signal_process/demodu/INS_dout_b2[54]) net  (fanout = 1)       0.460 r     9.493      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg0               0.143       9.636
 Arrival time                                                                        9.636                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.793ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.904 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.249ns  (logic 6.195ns, net 1.054ns, 85% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.doa[2]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_284.b[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[2]) net  (fanout = 1)       0.594 r     6.115      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_284.fco                      cell (ADDER)            0.770 r     6.885
 signal_process/demodu/sub0_syn_285.fci (signal_process/demodu/sub0_syn_230) net  (fanout = 1)       0.000 f     6.885      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_285.fco                      cell (ADDER)            0.132 r     7.017
 signal_process/demodu/sub0_syn_286.fci (signal_process/demodu/sub0_syn_234) net  (fanout = 1)       0.000 f     7.017      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.132 r     7.149
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.149      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.281
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.281      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.413
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.413      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.545
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.545      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.677
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.677      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.809
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.809      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.941
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.941      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.073
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.073      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.205
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.205      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.337
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.337      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.469
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.469      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[1]                    cell (ADDER)            0.453 r     8.922
 signal_process/demodu/reg2_syn_207.mi[0] (signal_process/demodu/INS_dout_b2[54]) net  (fanout = 1)       0.460 r     9.382      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg0               0.143       9.525
 Arrival time                                                                        9.525                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.904ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.913 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg2_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.240ns  (logic 6.042ns, net 1.198ns, 83% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clkb (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dob[0]   clk2q                   3.245 r     5.521
 signal_process/demodu/sub0_syn_286.a[1] (signal_process/demodu/fifo/ram_inst/dob_tmp[9]) net  (fanout = 1)       0.738 r     6.259      ../../al_ip/Asys_fifo56X16.v(445)
 signal_process/demodu/sub0_syn_286.fco                      cell (ADDER)            0.881 r     7.140
 signal_process/demodu/sub0_syn_287.fci (signal_process/demodu/sub0_syn_238) net  (fanout = 1)       0.000 f     7.140      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_287.fco                      cell (ADDER)            0.132 r     7.272
 signal_process/demodu/sub0_syn_288.fci (signal_process/demodu/sub0_syn_242) net  (fanout = 1)       0.000 f     7.272      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_288.fco                      cell (ADDER)            0.132 r     7.404
 signal_process/demodu/sub0_syn_289.fci (signal_process/demodu/sub0_syn_246) net  (fanout = 1)       0.000 f     7.404      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_289.fco                      cell (ADDER)            0.132 r     7.536
 signal_process/demodu/sub0_syn_290.fci (signal_process/demodu/sub0_syn_250) net  (fanout = 1)       0.000 f     7.536      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_290.fco                      cell (ADDER)            0.132 r     7.668
 signal_process/demodu/sub0_syn_291.fci (signal_process/demodu/sub0_syn_254) net  (fanout = 1)       0.000 f     7.668      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_291.fco                      cell (ADDER)            0.132 r     7.800
 signal_process/demodu/sub0_syn_292.fci (signal_process/demodu/sub0_syn_258) net  (fanout = 1)       0.000 f     7.800      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_292.fco                      cell (ADDER)            0.132 r     7.932
 signal_process/demodu/sub0_syn_293.fci (signal_process/demodu/sub0_syn_262) net  (fanout = 1)       0.000 f     7.932      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_293.fco                      cell (ADDER)            0.132 r     8.064
 signal_process/demodu/sub0_syn_294.fci (signal_process/demodu/sub0_syn_266) net  (fanout = 1)       0.000 f     8.064      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_294.fco                      cell (ADDER)            0.132 r     8.196
 signal_process/demodu/sub0_syn_295.fci (signal_process/demodu/sub0_syn_270) net  (fanout = 1)       0.000 f     8.196      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_295.fco                      cell (ADDER)            0.132 r     8.328
 signal_process/demodu/sub0_syn_296.fci (signal_process/demodu/sub0_syn_274) net  (fanout = 1)       0.000 f     8.328      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_296.fco                      cell (ADDER)            0.132 r     8.460
 signal_process/demodu/sub0_syn_297.fci (signal_process/demodu/sub0_syn_278) net  (fanout = 1)       0.000 f     8.460      ../../Src_al/Demodulation.v(168)
 signal_process/demodu/sub0_syn_297.fx[1]                    cell (ADDER)            0.453 r     8.913
 signal_process/demodu/reg2_syn_207.mi[0] (signal_process/demodu/INS_dout_b2[54]) net  (fanout = 1)       0.460 r     9.373      ../../Src_al/Demodulation.v(68)
 signal_process/demodu/reg2_syn_207                          path2reg0               0.143       9.516
 Arrival time                                                                        9.516                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg2_syn_207.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.262
 clock uncertainty                                                                  -0.000      10.262
 clock recovergence pessimism                                                        0.167      10.429
 Required time                                                                      10.429            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.913ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/rs422/reg12_syn_47 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.260 ns                                                        
 Start Point:             signal_process/rs422/reg12_syn_43.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/rs422/reg12_syn_47.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.429ns  (logic 0.204ns, net 0.225ns, 47% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/rs422/reg12_syn_43.clk (signal_process/clk)  net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/rs422/reg12_syn_43.q[0]                      clk2q                   0.109 r     2.047
 signal_process/rs422/reg12_syn_47.mi[0] (signal_process/rs422/output_filter[0]) net  (fanout = 1)       0.225 r     2.272      ../../Src_al/Rs422Output.v(69)
 signal_process/rs422/reg12_syn_47                           path2reg0               0.095       2.367
 Arrival time                                                                        2.367                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/rs422/reg12_syn_47.clk (signal_process/clk)  net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       2.191
 clock uncertainty                                                                   0.000       2.191
 clock recovergence pessimism                                                       -0.084       2.107
 Required time                                                                       2.107            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.260ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg9_syn_208 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.266 ns                                                        
 Start Point:             signal_process/demodu/reg11_syn_210.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg9_syn_208.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.428ns  (logic 0.204ns, net 0.224ns, 47% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_210.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg11_syn_210.q[0]                    clk2q                   0.109 r     2.138
 signal_process/demodu/reg9_syn_208.mi[0] (signal_process/demodu/sample_sum[52]) net  (fanout = 3)       0.224 r     2.362      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg9_syn_208                          path2reg0               0.095       2.457
 Arrival time                                                                        2.457                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg9_syn_208.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       2.191
 clock uncertainty                                                                   0.000       2.191
 clock recovergence pessimism                                                        0.000       2.191
 Required time                                                                       2.191            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.266ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_28 (4 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.269 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[4] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.514ns  (logic 0.109ns, net 0.405ns, 21% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_47.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_47.q[1]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[4] (signal_process/demodu/fifo/rd_addr[0]) net  (fanout = 10)      0.405 r     2.452      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28         path2reg (EMB)          0.000       2.452
 Arrival time                                                                        2.452                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.269ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.366 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[5] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.611ns  (logic 0.109ns, net 0.502ns, 17% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_47.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_47.q[0]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[5] (signal_process/demodu/fifo/rd_addr[1]) net  (fanout = 10)      0.502 r     2.549      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28         path2reg (EMB)          0.000       2.549
 Arrival time                                                                        2.549                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.366ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.394 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_44.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[6] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.639ns  (logic 0.109ns, net 0.530ns, 17% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_44.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_44.q[1]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.addrb[6] (signal_process/demodu/fifo/rd_addr[2]) net  (fanout = 9)       0.530 r     2.577      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28         path2reg (EMB)          0.000       2.577
 Arrival time                                                                        2.577                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.394ns          

---------------------------------------------------------------------------------------------------------

Recovery checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_66 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.370 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_66.rstb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.649ns  (logic 0.146ns, net 1.503ns, 8% logic)                 
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/ram_inst/ramread0_syn_66.rstb (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      1.503 r     3.925      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_66         path2reg                0.000       3.925
 Arrival time                                                                        3.925                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_66.clkb (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.250      10.128
 clock uncertainty                                                                  -0.000      10.128
 clock recovergence pessimism                                                        0.167      10.295
 Required time                                                                      10.295            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.370ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_47 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.532 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_47.rstb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.487ns  (logic 0.146ns, net 1.341ns, 9% logic)                 
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.rstb (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      1.341 r     3.763      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47         path2reg                0.000       3.763
 Arrival time                                                                        3.763                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.clkb (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.250      10.128
 clock uncertainty                                                                  -0.000      10.128
 clock recovergence pessimism                                                        0.167      10.295
 Required time                                                                      10.295            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.532ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_28 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.633 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_28.rstb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.386ns  (logic 0.146ns, net 1.240ns, 10% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.rstb (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      1.240 r     3.662      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28         path2reg                0.000       3.662
 Arrival time                                                                        3.662                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_28.clkb (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.250      10.128
 clock uncertainty                                                                  -0.000      10.128
 clock recovergence pessimism                                                        0.167      10.295
 Required time                                                                      10.295            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.633ns          

---------------------------------------------------------------------------------------------------------

Removal checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.362 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.660ns  (logic 0.161ns, net 0.499ns, 24% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      0.499 r     2.546      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23  path2reg                0.052       2.598
 Arrival time                                                                        2.598                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.383
 clock uncertainty                                                                   0.000       2.383
 clock recovergence pessimism                                                       -0.147       2.236
 Required time                                                                       2.236            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.362ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.419 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.717ns  (logic 0.161ns, net 0.556ns, 22% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      0.556 r     2.603      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28  path2reg                0.052       2.655
 Arrival time                                                                        2.655                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.383
 clock uncertainty                                                                   0.000       2.383
 clock recovergence pessimism                                                       -0.147       2.236
 Required time                                                                       2.236            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.419ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.419 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.717ns  (logic 0.161ns, net 0.556ns, 22% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[0]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 16)      0.556 r     2.603      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22  path2reg                0.052       2.655
 Arrival time                                                                        2.655                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_22.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.383
 clock uncertainty                                                                   0.000       2.383
 clock recovergence pessimism                                                       -0.147       2.236
 Required time                                                                       2.236            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.419ns          

---------------------------------------------------------------------------------------------------------

Period checks:
---------------------------------------------------------------------------------------------------------
 Point                                             Type          Setting(ns)    Requied(ns)     Slack(ns)  
---------------------------------------------------------------------------------------------------------
 signal_process/modu/mult0_syn_2.clk               min period       8.333          3.414          4.919    

=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[3]                                  
Clock = CLK120/pll_inst.clkc[3], period 16.666ns, rising at 0ns, falling at 8.333ns

546 endpoints analyzed totally, and 5606 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 9.98ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg0_syn_13 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     6.686 ns                                                        
 Start Point:             signal_process/ctrl_signal/AD_valid_reg_syn_31.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg0_syn_13.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.221ns  (logic 0.289ns, net 0.932ns, 23% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/ctrl_signal/AD_valid_reg_syn_31.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/ctrl_signal/AD_valid_reg_syn_31.q[1]         clk2q                   0.146 r     2.422
 signal_process/demodu/reg0_syn_13.mi[0] (signal_process/ctrl_signal/AD_valid) net  (fanout = 2)       0.932 r     3.354      ../../Src_al/SignalGenerator.v(52)
 signal_process/demodu/reg0_syn_13                           path2reg0               0.143       3.497
 Arrival time                                                                        3.497                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg0_syn_13.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  8.333      10.499
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.383
 clock uncertainty                                                                  -0.200      10.183
 clock recovergence pessimism                                                        0.000      10.183
 Required time                                                                      10.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.686ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg11_syn_210 (65 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     11.496 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_56.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_210.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.790ns  (logic 2.975ns, net 1.815ns, 62% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_56.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_56.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_327.d[1] (signal_process/demodu/din_reg1[1]) net  (fanout = 1)       1.061 r     3.617      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.715 r     4.332
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_273) net  (fanout = 1)       0.000 f     4.332      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     4.464
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.464      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.596
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.596      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.728
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.728      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.860
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.860      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.992
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.992      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.124
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.124      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.256
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.256      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.388
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.388      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.520
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.520      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.652
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.652      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.784
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.784      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.916
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.916      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.fx[0]                    cell (ADDER)            0.387 r     6.303
 signal_process/demodu/reg11_syn_210.mi[0] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.754 r     7.057      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_210                         path2reg0               0.143       7.200
 Arrival time                                                                        7.200                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_210.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.496ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.501 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_56.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_210.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.785ns  (logic 2.975ns, net 1.810ns, 62% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_56.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_56.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_327.e[1] (signal_process/demodu/din_reg1[2]) net  (fanout = 1)       1.056 r     3.612      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.715 r     4.327
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_273) net  (fanout = 1)       0.000 f     4.327      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     4.459
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.459      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.591
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.591      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.723
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.723      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.855
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.855      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.987
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.987      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.119
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.119      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.251
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.251      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.383
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.383      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.515
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.515      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.647
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.647      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.779
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.779      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.911
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.911      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.fx[0]                    cell (ADDER)            0.387 r     6.298
 signal_process/demodu/reg11_syn_210.mi[0] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.754 r     7.052      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_210                         path2reg0               0.143       7.195
 Arrival time                                                                        7.195                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_210.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.501ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.540 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_210.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.746ns  (logic 2.909ns, net 1.837ns, 61% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_47.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_47.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_328.d[0] (signal_process/demodu/din_reg1[3]) net  (fanout = 1)       1.083 r     3.639      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.781 r     4.420
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.420      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.552
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.552      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.684
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.684      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.816
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.816      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.948
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.948      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.080
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.080      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.212
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.212      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.344
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.344      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.476
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.476      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.608
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.608      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.740
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.740      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.872
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.872      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.fx[0]                    cell (ADDER)            0.387 r     6.259
 signal_process/demodu/reg11_syn_210.mi[0] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.754 r     7.013      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_210                         path2reg0               0.143       7.156
 Arrival time                                                                        7.156                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_210.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.540ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg11_syn_207 (67 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     11.779 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_56.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.507ns  (logic 2.852ns, net 1.655ns, 63% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_56.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_56.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_327.d[1] (signal_process/demodu/din_reg1[1]) net  (fanout = 1)       1.061 r     3.617      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.715 r     4.332
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_273) net  (fanout = 1)       0.000 f     4.332      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     4.464
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.464      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.596
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.596      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.728
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.728      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.860
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.860      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.992
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.992      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.124
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.124      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.256
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.256      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.388
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.388      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.520
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.520      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.652
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.652      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.784
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.784      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.916
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.916      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.f[1]                     cell (ADDER)            0.264 r     6.180
 signal_process/demodu/reg11_syn_207.mi[0] (signal_process/demodu/sample_sum_b2[53]) net  (fanout = 1)       0.594 r     6.774      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_207                         path2reg0               0.143       6.917
 Arrival time                                                                        6.917                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_207.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.779ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.784 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_56.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.502ns  (logic 2.852ns, net 1.650ns, 63% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_56.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_56.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_327.e[1] (signal_process/demodu/din_reg1[2]) net  (fanout = 1)       1.056 r     3.612      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.715 r     4.327
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_273) net  (fanout = 1)       0.000 f     4.327      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     4.459
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.459      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.591
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.591      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.723
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.723      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.855
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.855      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.987
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.987      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.119
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.119      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.251
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.251      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.383
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.383      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.515
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.515      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.647
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.647      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.779
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.779      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.911
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.911      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.f[1]                     cell (ADDER)            0.264 r     6.175
 signal_process/demodu/reg11_syn_207.mi[0] (signal_process/demodu/sample_sum_b2[53]) net  (fanout = 1)       0.594 r     6.769      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_207                         path2reg0               0.143       6.912
 Arrival time                                                                        6.912                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_207.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.784ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.823 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_207.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.463ns  (logic 2.786ns, net 1.677ns, 62% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_47.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_47.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_328.d[0] (signal_process/demodu/din_reg1[3]) net  (fanout = 1)       1.083 r     3.639      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.781 r     4.420
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_277) net  (fanout = 1)       0.000 f     4.420      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     4.552
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_281) net  (fanout = 1)       0.000 f     4.552      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     4.684
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_285) net  (fanout = 1)       0.000 f     4.684      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     4.816
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_289) net  (fanout = 1)       0.000 f     4.816      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     4.948
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_293) net  (fanout = 1)       0.000 f     4.948      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.080
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_297) net  (fanout = 1)       0.000 f     5.080      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.212
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_301) net  (fanout = 1)       0.000 f     5.212      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     5.344
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_305) net  (fanout = 1)       0.000 f     5.344      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fco                      cell (ADDER)            0.132 r     5.476
 signal_process/demodu/add1_syn_337.fci (signal_process/demodu/add1_syn_309) net  (fanout = 1)       0.000 f     5.476      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_337.fco                      cell (ADDER)            0.132 r     5.608
 signal_process/demodu/add1_syn_338.fci (signal_process/demodu/add1_syn_313) net  (fanout = 1)       0.000 f     5.608      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_338.fco                      cell (ADDER)            0.132 r     5.740
 signal_process/demodu/add1_syn_339.fci (signal_process/demodu/add1_syn_317) net  (fanout = 1)       0.000 f     5.740      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_339.fco                      cell (ADDER)            0.132 r     5.872
 signal_process/demodu/add1_syn_340.fci (signal_process/demodu/add1_syn_321) net  (fanout = 1)       0.000 f     5.872      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_340.f[1]                     cell (ADDER)            0.264 r     6.136
 signal_process/demodu/reg11_syn_207.mi[0] (signal_process/demodu/sample_sum_b2[53]) net  (fanout = 1)       0.594 r     6.730      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_207                         path2reg0               0.143       6.873
 Arrival time                                                                        6.873                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_207.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.200      18.516
 clock recovergence pessimism                                                        0.180      18.696
 Required time                                                                      18.696            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.823ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_9 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.034 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_190.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.479ns  (logic 0.109ns, net 0.370ns, 22% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_190.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_190.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] (signal_process/demodu/latch_sample_sum[9]) net  (fanout = 1)       0.370 r     2.508      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.508
 Arrival time                                                                        2.508                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.034ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.075 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_187.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.520ns  (logic 0.109ns, net 0.411ns, 20% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_187.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_187.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] (signal_process/demodu/latch_sample_sum[12]) net  (fanout = 1)       0.411 r     2.549      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.549
 Arrival time                                                                        2.549                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.075ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.101 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_178.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.546ns  (logic 0.109ns, net 0.437ns, 19% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_178.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_178.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] (signal_process/demodu/latch_sample_sum[15]) net  (fanout = 1)       0.437 r     2.575      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.575
 Arrival time                                                                        2.575                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.101ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_9 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.075 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_202.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.520ns  (logic 0.109ns, net 0.411ns, 20% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_202.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_202.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] (signal_process/demodu/latch_sample_sum[2]) net  (fanout = 1)       0.411 r     2.549      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.549
 Arrival time                                                                        2.549                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.075ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.111 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_181.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.556ns  (logic 0.109ns, net 0.447ns, 19% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_181.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_181.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] (signal_process/demodu/latch_sample_sum[7]) net  (fanout = 1)       0.447 r     2.585      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.585
 Arrival time                                                                        2.585                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.111ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.117 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_196.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.562ns  (logic 0.109ns, net 0.453ns, 19% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_196.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_196.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] (signal_process/demodu/latch_sample_sum[3]) net  (fanout = 1)       0.453 r     2.591      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9          path2reg (EMB)          0.000       2.591
 Arrival time                                                                        2.591                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_9.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.117ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_47 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.092 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_211.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.537ns  (logic 0.109ns, net 0.428ns, 20% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_211.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_211.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] (signal_process/demodu/latch_sample_sum[52]) net  (fanout = 1)       0.428 r     2.566      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47         path2reg (EMB)          0.000       2.566
 Arrival time                                                                        2.566                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.092ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.490 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_220.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.935ns  (logic 0.391ns, net 0.544ns, 41% logic)                
 Logic Levels:            2 ( EMB=1 LUT1=1 )                                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_220.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_220.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/latch_sample_sum[46]_syn_3.a[0] (signal_process/demodu/latch_sample_sum[46]) net  (fanout = 1)       0.216 r     2.354      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/latch_sample_sum[46]_syn_3.f[0]       cell (LUT1)             0.282 r     2.636
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] (signal_process/demodu/latch_sample_sum[46]_holdbuf) net  (fanout = 1)       0.328 r     2.964                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47         path2reg (EMB)          0.000       2.964
 Arrival time                                                                        2.964                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.490ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.713 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_211.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.158ns  (logic 0.391ns, net 0.767ns, 33% logic)                
 Logic Levels:            2 ( EMB=1 LUT1=1 )                                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_211.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_211.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/latch_sample_sum[51]_syn_3.a[0] (signal_process/demodu/latch_sample_sum[51]) net  (fanout = 1)       0.331 r     2.469      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/latch_sample_sum[51]_syn_3.f[0]       cell (LUT1)             0.282 r     2.751
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] (signal_process/demodu/latch_sample_sum[51]_holdbuf) net  (fanout = 1)       0.436 r     3.187                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47         path2reg (EMB)          0.000       3.187
 Arrival time                                                                        3.187                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_47.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.200       2.630
 clock recovergence pessimism                                                       -0.156       2.474
 Required time                                                                       2.474            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.713ns          

---------------------------------------------------------------------------------------------------------

Recovery checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  14.614 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.488ns  (logic 0.232ns, net 1.256ns, 15% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      1.256 r     3.812      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24  path2reg                0.086       3.898
 Arrival time                                                                        3.898                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.200      18.332
 clock recovergence pessimism                                                        0.180      18.512
 Required time                                                                      18.512            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              14.614ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  14.769 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.333ns  (logic 0.232ns, net 1.101ns, 17% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      1.101 r     3.657      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16  path2reg                0.086       3.743
 Arrival time                                                                        3.743                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.200      18.332
 clock recovergence pessimism                                                        0.180      18.512
 Required time                                                                      18.512            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              14.769ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/reg1_syn_42 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  14.769 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/reg1_syn_42.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.333ns  (logic 0.232ns, net 1.101ns, 17% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/reg1_syn_42.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      1.101 r     3.657      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/reg1_syn_42                      path2reg                0.086       3.743
 Arrival time                                                                        3.743                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg1_syn_42.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.200      18.332
 clock recovergence pessimism                                                        0.180      18.512
 Required time                                                                      18.512            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              14.769ns          

---------------------------------------------------------------------------------------------------------

Removal checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/sub0_syn_36 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.097 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/sub0_syn_36.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.595ns  (logic 0.161ns, net 0.434ns, 27% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/sub0_syn_36.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      0.434 r     2.572      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/sub0_syn_36                      path2reg                0.052       2.624
 Arrival time                                                                        2.624                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/sub0_syn_36.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.200       2.683
 clock recovergence pessimism                                                       -0.156       2.527
 Required time                                                                       2.527            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.097ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.097 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.595ns  (logic 0.161ns, net 0.434ns, 27% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      0.434 r     2.572      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24  path2reg                0.052       2.624
 Arrival time                                                                        2.624                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.200       2.683
 clock recovergence pessimism                                                       -0.156       2.527
 Required time                                                                       2.527            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.097ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.201 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.699ns  (logic 0.161ns, net 0.538ns, 23% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 13)      0.538 r     2.676      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15  path2reg                0.052       2.728
 Arrival time                                                                        2.728                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.200       2.683
 clock recovergence pessimism                                                       -0.156       2.527
 Required time                                                                       2.527            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.201ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[4]                                  
Clock = CLK120/pll_inst.clkc[4], period 16.666ns, rising at 0ns, falling at 8.333ns

0 endpoints analyzed totally, and 0 paths analyzed
0 errors detected : 0 setup errors (TNS = 0), 0 hold errors (TNS = 0)
Minimum period is 0ns
---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: clk_us                                                   
Clock = clk_us, period 1000ns, rising at 0ns, falling at 500ns

208 endpoints analyzed totally, and 8126 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 8.729ns
---------------------------------------------------------------------------------------------------------

Paths for end point dq_syn_2 (174 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     991.271 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3772.clk (rising edge triggered by clock clk_us)
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         8.405ns  (logic 3.017ns, net 5.388ns, 35% logic)                
 Logic Levels:            5 ( ADDER=2 LUT4=1 LUT3=1 LUT5=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3772.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3772.q[0]                            clk2q                   0.146 r     3.653
 wendu/lt2_syn_96.a[0] (wendu/cnt_us[3])                     net  (fanout = 7)       0.976 r     4.629      ../../Src_al/DS18B20.v(51)
 wendu/lt2_syn_96.fco                                        cell (ADDER)            0.706 r     5.335
 wendu/lt2_syn_99.fci (wendu/lt2_syn_13)                     net  (fanout = 1)       0.000 f     5.335      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_99.fco                                        cell (ADDER)            0.073 r     5.408
 wendu/lt2_syn_102.fci (wendu/lt2_syn_17)                    net  (fanout = 1)       0.000 f     5.408      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_102.fco                                       cell (ADDER)            0.073 r     5.481
 wendu/lt2_syn_105.fci (wendu/lt2_syn_21)                    net  (fanout = 1)       0.000 f     5.481      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_105.fco                                       cell (ADDER)            0.073 r     5.554
 wendu/lt2_syn_108.fci (wendu/lt2_syn_25)                    net  (fanout = 1)       0.000 f     5.554      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_108.fco                                       cell (ADDER)            0.073 r     5.627
 wendu/lt2_syn_111.fci (wendu/lt2_syn_29)                    net  (fanout = 1)       0.000 f     5.627      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_111.fco                                       cell (ADDER)            0.073 r     5.700
 wendu/lt2_syn_114.fci (wendu/lt2_syn_33)                    net  (fanout = 1)       0.000 f     5.700      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_114.fco                                       cell (ADDER)            0.073 r     5.773
 wendu/lt2_syn_117.fci (wendu/lt2_syn_37)                    net  (fanout = 1)       0.000 f     5.773      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_117.fco                                       cell (ADDER)            0.073 r     5.846
 wendu/lt2_syn_120.fci (wendu/lt2_syn_41)                    net  (fanout = 1)       0.000 f     5.846      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_120.f[1]                                      cell (ADDER)            0.355 r     6.201
 wendu/cur_state[0]_syn_3764.b[1] (wendu/dq_en_n9)           net  (fanout = 3)       0.762 r     6.963                    
 wendu/cur_state[0]_syn_3764.fx[0]                           cell (LUT5)             0.543 r     7.506
 wendu/cur_state[0]_syn_3818.a[1] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.358 r     7.864      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3818.f[1]                            cell (LUT3)             0.408 r     8.272
 wendu/cur_state[0]_syn_3774.c[0] (wendu/cur_state[0]_syn_3588) net  (fanout = 1)       0.456 r     8.728      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3774.f[0]                            cell (LUT4)             0.348 r     9.076
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       2.836 r    11.912      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      11.912
 Arrival time                                                                       11.912                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.962
 clock uncertainty                                                                  -0.000    1002.962
 clock recovergence pessimism                                                        0.221    1003.183
 Required time                                                                    1003.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             991.271ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     991.271 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3772.clk (rising edge triggered by clock clk_us)
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         8.405ns  (logic 3.017ns, net 5.388ns, 35% logic)                
 Logic Levels:            5 ( ADDER=2 LUT4=1 LUT3=1 LUT5=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3772.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3772.q[0]                            clk2q                   0.146 r     3.653
 wendu/lt2_syn_96.a[0] (wendu/cnt_us[3])                     net  (fanout = 7)       0.976 r     4.629      ../../Src_al/DS18B20.v(51)
 wendu/lt2_syn_96.fco                                        cell (ADDER)            0.706 r     5.335
 wendu/lt2_syn_99.fci (wendu/lt2_syn_13)                     net  (fanout = 1)       0.000 f     5.335      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_99.fco                                        cell (ADDER)            0.073 r     5.408
 wendu/lt2_syn_102.fci (wendu/lt2_syn_17)                    net  (fanout = 1)       0.000 f     5.408      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_102.fco                                       cell (ADDER)            0.073 r     5.481
 wendu/lt2_syn_105.fci (wendu/lt2_syn_21)                    net  (fanout = 1)       0.000 f     5.481      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_105.fco                                       cell (ADDER)            0.073 r     5.554
 wendu/lt2_syn_108.fci (wendu/lt2_syn_25)                    net  (fanout = 1)       0.000 f     5.554      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_108.fco                                       cell (ADDER)            0.073 r     5.627
 wendu/lt2_syn_111.fci (wendu/lt2_syn_29)                    net  (fanout = 1)       0.000 f     5.627      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_111.fco                                       cell (ADDER)            0.073 r     5.700
 wendu/lt2_syn_114.fci (wendu/lt2_syn_33)                    net  (fanout = 1)       0.000 f     5.700      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_114.fco                                       cell (ADDER)            0.073 r     5.773
 wendu/lt2_syn_117.fci (wendu/lt2_syn_37)                    net  (fanout = 1)       0.000 f     5.773      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_117.fco                                       cell (ADDER)            0.073 r     5.846
 wendu/lt2_syn_120.fci (wendu/lt2_syn_41)                    net  (fanout = 1)       0.000 f     5.846      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_120.f[1]                                      cell (ADDER)            0.355 r     6.201
 wendu/cur_state[0]_syn_3764.b[0] (wendu/dq_en_n9)           net  (fanout = 3)       0.762 r     6.963                    
 wendu/cur_state[0]_syn_3764.fx[0]                           cell (LUT5)             0.543 r     7.506
 wendu/cur_state[0]_syn_3818.a[1] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.358 r     7.864      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3818.f[1]                            cell (LUT3)             0.408 r     8.272
 wendu/cur_state[0]_syn_3774.c[0] (wendu/cur_state[0]_syn_3588) net  (fanout = 1)       0.456 r     8.728      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3774.f[0]                            cell (LUT4)             0.348 r     9.076
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       2.836 r    11.912      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      11.912
 Arrival time                                                                       11.912                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.962
 clock uncertainty                                                                  -0.000    1002.962
 clock recovergence pessimism                                                        0.221    1003.183
 Required time                                                                    1003.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             991.271ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     991.342 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3792.clk (rising edge triggered by clock clk_us)
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         8.334ns  (logic 3.090ns, net 5.244ns, 37% logic)                
 Logic Levels:            5 ( ADDER=2 LUT4=1 LUT3=1 LUT5=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3792.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3792.q[0]                            clk2q                   0.146 r     3.653
 wendu/lt2_syn_93.a[0] (wendu/cnt_us[1])                     net  (fanout = 6)       0.832 r     4.485      ../../Src_al/DS18B20.v(51)
 wendu/lt2_syn_93.fco                                        cell (ADDER)            0.706 r     5.191
 wendu/lt2_syn_96.fci (wendu/lt2_syn_9)                      net  (fanout = 1)       0.000 f     5.191      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_96.fco                                        cell (ADDER)            0.073 r     5.264
 wendu/lt2_syn_99.fci (wendu/lt2_syn_13)                     net  (fanout = 1)       0.000 f     5.264      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_99.fco                                        cell (ADDER)            0.073 r     5.337
 wendu/lt2_syn_102.fci (wendu/lt2_syn_17)                    net  (fanout = 1)       0.000 f     5.337      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_102.fco                                       cell (ADDER)            0.073 r     5.410
 wendu/lt2_syn_105.fci (wendu/lt2_syn_21)                    net  (fanout = 1)       0.000 f     5.410      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_105.fco                                       cell (ADDER)            0.073 r     5.483
 wendu/lt2_syn_108.fci (wendu/lt2_syn_25)                    net  (fanout = 1)       0.000 f     5.483      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_108.fco                                       cell (ADDER)            0.073 r     5.556
 wendu/lt2_syn_111.fci (wendu/lt2_syn_29)                    net  (fanout = 1)       0.000 f     5.556      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_111.fco                                       cell (ADDER)            0.073 r     5.629
 wendu/lt2_syn_114.fci (wendu/lt2_syn_33)                    net  (fanout = 1)       0.000 f     5.629      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_114.fco                                       cell (ADDER)            0.073 r     5.702
 wendu/lt2_syn_117.fci (wendu/lt2_syn_37)                    net  (fanout = 1)       0.000 f     5.702      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_117.fco                                       cell (ADDER)            0.073 r     5.775
 wendu/lt2_syn_120.fci (wendu/lt2_syn_41)                    net  (fanout = 1)       0.000 f     5.775      ../../Src_al/DS18B20.v(206)
 wendu/lt2_syn_120.f[1]                                      cell (ADDER)            0.355 r     6.130
 wendu/cur_state[0]_syn_3764.b[1] (wendu/dq_en_n9)           net  (fanout = 3)       0.762 r     6.892                    
 wendu/cur_state[0]_syn_3764.fx[0]                           cell (LUT5)             0.543 r     7.435
 wendu/cur_state[0]_syn_3818.a[1] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.358 r     7.793      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3818.f[1]                            cell (LUT3)             0.408 r     8.201
 wendu/cur_state[0]_syn_3774.c[0] (wendu/cur_state[0]_syn_3588) net  (fanout = 1)       0.456 r     8.657      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3774.f[0]                            cell (LUT4)             0.348 r     9.005
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       2.836 r    11.841      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      11.841
 Arrival time                                                                       11.841                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.962
 clock uncertainty                                                                  -0.000    1002.962
 clock recovergence pessimism                                                        0.221    1003.183
 Required time                                                                    1003.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             991.342ns          

---------------------------------------------------------------------------------------------------------

Paths for end point dq_syn_2 (15 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     992.945 ns                                                      
 Start Point:             wendu/reg3_syn_39.clk (rising edge triggered by clock clk_us)   
 End Point:               dq_syn_2.ce (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.848ns  (logic 1.427ns, net 5.421ns, 20% logic)                
 Logic Levels:            4 ( LUT4=3 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_39.clk (wendu/clk_us_syn_4)                  net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_39.q[1]                                      clk2q                   0.146 r     3.653
 wendu/next_state[0]_syn_52.b[1] (wendu/cur_state[4])        net  (fanout = 6)       0.797 r     4.450      ../../Src_al/DS18B20.v(44)
 wendu/next_state[0]_syn_52.f[1]                             cell (LUT4)             0.333 r     4.783
 wendu/next_state[0]_syn_60.d[1] (wendu/next_state[0]_syn_2) net  (fanout = 10)      0.515 r     5.298      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_60.f[1]                             cell (LUT4)             0.262 r     5.560
 wendu/next_state[0]_syn_62.d[1] (wendu/next_state[0]_syn_4) net  (fanout = 5)       0.601 r     6.161      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_62.f[1]                             cell (LUT3)             0.262 r     6.423
 wendu/next_state[0]_syn_48.a[1] (wendu/next_state[0]_syn_8) net  (fanout = 2)       0.456 r     6.879      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_48.f[1]                             cell (LUT4)             0.424 r     7.303
 dq_syn_2.ce (wendu/bit_cnt_b_n7)                            net  (fanout = 40)      3.052 r    10.355                    
 dq_syn_2                                                    path2reg                0.000      10.355
 Arrival time                                                                       10.355                  (4 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.117    1003.079
 clock uncertainty                                                                  -0.000    1003.079
 clock recovergence pessimism                                                        0.221    1003.300
 Required time                                                                    1003.300            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             992.945ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     993.110 ns                                                      
 Start Point:             wendu/reg3_syn_39.clk (rising edge triggered by clock clk_us)   
 End Point:               dq_syn_2.ce (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.683ns  (logic 1.299ns, net 5.384ns, 19% logic)                
 Logic Levels:            4 ( LUT4=3 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_39.clk (wendu/clk_us_syn_4)                  net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_39.q[0]                                      clk2q                   0.146 r     3.653
 wendu/next_state[0]_syn_52.d[1] (wendu/cur_state[1])        net  (fanout = 7)       0.760 r     4.413      ../../Src_al/DS18B20.v(44)
 wendu/next_state[0]_syn_52.f[1]                             cell (LUT4)             0.205 r     4.618
 wendu/next_state[0]_syn_60.d[1] (wendu/next_state[0]_syn_2) net  (fanout = 10)      0.515 r     5.133      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_60.f[1]                             cell (LUT4)             0.262 r     5.395
 wendu/next_state[0]_syn_62.d[1] (wendu/next_state[0]_syn_4) net  (fanout = 5)       0.601 r     5.996      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_62.f[1]                             cell (LUT3)             0.262 r     6.258
 wendu/next_state[0]_syn_48.a[1] (wendu/next_state[0]_syn_8) net  (fanout = 2)       0.456 r     6.714      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_48.f[1]                             cell (LUT4)             0.424 r     7.138
 dq_syn_2.ce (wendu/bit_cnt_b_n7)                            net  (fanout = 40)      3.052 r    10.190                    
 dq_syn_2                                                    path2reg                0.000      10.190
 Arrival time                                                                       10.190                  (4 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.117    1003.079
 clock uncertainty                                                                  -0.000    1003.079
 clock recovergence pessimism                                                        0.221    1003.300
 Required time                                                                    1003.300            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.110ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     993.603 ns                                                      
 Start Point:             wendu/reg3_syn_43.clk (rising edge triggered by clock clk_us)   
 End Point:               dq_syn_2.ce (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.190ns  (logic 1.345ns, net 4.845ns, 21% logic)                
 Logic Levels:            4 ( LUT4=3 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_43.clk (wendu/clk_us_syn_4)                  net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_43.q[1]                                      clk2q                   0.146 r     3.653
 wendu/next_state[0]_syn_52.c[1] (wendu/cur_state[5])        net  (fanout = 8)       0.221 r     3.874      ../../Src_al/DS18B20.v(44)
 wendu/next_state[0]_syn_52.f[1]                             cell (LUT4)             0.251 r     4.125
 wendu/next_state[0]_syn_60.d[1] (wendu/next_state[0]_syn_2) net  (fanout = 10)      0.515 r     4.640      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_60.f[1]                             cell (LUT4)             0.262 r     4.902
 wendu/next_state[0]_syn_62.d[1] (wendu/next_state[0]_syn_4) net  (fanout = 5)       0.601 r     5.503      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_62.f[1]                             cell (LUT3)             0.262 r     5.765
 wendu/next_state[0]_syn_48.a[1] (wendu/next_state[0]_syn_8) net  (fanout = 2)       0.456 r     6.221      ../../Src_al/DS18B20.v(45)
 wendu/next_state[0]_syn_48.f[1]                             cell (LUT4)             0.424 r     6.645
 dq_syn_2.ce (wendu/bit_cnt_b_n7)                            net  (fanout = 40)      3.052 r     9.697                    
 dq_syn_2                                                    path2reg                0.000       9.697
 Arrival time                                                                        9.697                  (4 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.962       2.962      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.962
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.117    1003.079
 clock uncertainty                                                                  -0.000    1003.079
 clock recovergence pessimism                                                        0.221    1003.300
 Required time                                                                    1003.300            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.603ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg2_syn_139 (74 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     993.810 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3780.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg2_syn_139.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.010ns  (logic 2.429ns, net 3.581ns, 40% logic)                
 Logic Levels:            7 ( LUT2=3 LUT4=3 LUT3=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3780.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3780.q[0]                            clk2q                   0.146 r     3.653
 wendu/cur_state[0]_syn_3814.a[1] (wendu/cnt_us[16])         net  (fanout = 6)       0.764 r     4.417      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3814.f[1]                            cell (LUT4)             0.424 r     4.841
 wendu/cur_state[0]_syn_3822.a[0] (wendu/cur_state[0]_syn_3503) net  (fanout = 1)       0.456 r     5.297      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3822.f[0]                            cell (LUT4)             0.408 r     5.705
 wendu/cur_state[0]_syn_3772.d[1] (wendu/cur_state[0]_syn_3507) net  (fanout = 5)       0.470 r     6.175      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3772.f[1]                            cell (LUT3)             0.262 r     6.437
 wendu/cur_state[0]_syn_3780.d[1] (wendu/cur_state[0]_syn_3513) net  (fanout = 2)       0.456 r     6.893      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3780.f[1]                            cell (LUT2)             0.262 r     7.155
 wendu/cur_state[0]_syn_3782.a[1] (wendu/bit_cnt_b1_n)       net  (fanout = 4)       0.459 r     7.614                    
 wendu/cur_state[0]_syn_3782.f[1]                            cell (LUT2)             0.408 r     8.022
 wendu/cur_state[0]_syn_3766.d[1] (wendu/cur_state[0]_syn_3581) net  (fanout = 2)       0.307 r     8.329      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3766.f[1]                            cell (LUT4)             0.205 r     8.534
 wendu/reg2_syn_139.d[1] (wendu/cur_state[0]_syn_3583)       net  (fanout = 2)       0.669 r     9.203      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_139                                          path2reg1 (LUT2)        0.314       9.517
 Arrival time                                                                        9.517                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_139.clk (wendu/clk_us_syn_4)                 net                     3.150       3.150      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.150
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1003.034
 clock uncertainty                                                                  -0.000    1003.034
 clock recovergence pessimism                                                        0.293    1003.327
 Required time                                                                    1003.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.810ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     993.837 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3770.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg2_syn_139.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.983ns  (logic 2.353ns, net 3.630ns, 39% logic)                
 Logic Levels:            7 ( LUT2=3 LUT4=3 LUT3=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3770.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3770.q[0]                            clk2q                   0.146 r     3.653
 wendu/cur_state[0]_syn_3814.c[1] (wendu/cnt_us[18])         net  (fanout = 6)       0.813 r     4.466      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3814.f[1]                            cell (LUT4)             0.348 r     4.814
 wendu/cur_state[0]_syn_3822.a[0] (wendu/cur_state[0]_syn_3503) net  (fanout = 1)       0.456 r     5.270      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3822.f[0]                            cell (LUT4)             0.408 r     5.678
 wendu/cur_state[0]_syn_3772.d[1] (wendu/cur_state[0]_syn_3507) net  (fanout = 5)       0.470 r     6.148      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3772.f[1]                            cell (LUT3)             0.262 r     6.410
 wendu/cur_state[0]_syn_3780.d[1] (wendu/cur_state[0]_syn_3513) net  (fanout = 2)       0.456 r     6.866      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3780.f[1]                            cell (LUT2)             0.262 r     7.128
 wendu/cur_state[0]_syn_3782.a[1] (wendu/bit_cnt_b1_n)       net  (fanout = 4)       0.459 r     7.587                    
 wendu/cur_state[0]_syn_3782.f[1]                            cell (LUT2)             0.408 r     7.995
 wendu/cur_state[0]_syn_3766.d[1] (wendu/cur_state[0]_syn_3581) net  (fanout = 2)       0.307 r     8.302      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3766.f[1]                            cell (LUT4)             0.205 r     8.507
 wendu/reg2_syn_139.d[1] (wendu/cur_state[0]_syn_3583)       net  (fanout = 2)       0.669 r     9.176      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_139                                          path2reg1 (LUT2)        0.314       9.490
 Arrival time                                                                        9.490                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_139.clk (wendu/clk_us_syn_4)                 net                     3.150       3.150      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.150
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1003.034
 clock uncertainty                                                                  -0.000    1003.034
 clock recovergence pessimism                                                        0.293    1003.327
 Required time                                                                    1003.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.837ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     994.018 ns                                                      
 Start Point:             wendu/cur_state[0]_syn_3782.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg2_syn_139.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.802ns  (logic 2.267ns, net 3.535ns, 39% logic)                
 Logic Levels:            7 ( LUT2=3 LUT4=3 LUT3=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3782.clk (wendu/clk_us_syn_4)        net                     3.507       3.507      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.507
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3782.q[0]                            clk2q                   0.146 r     3.653
 wendu/cur_state[0]_syn_3814.d[1] (wendu/cnt_us[19])         net  (fanout = 6)       0.718 r     4.371      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3814.f[1]                            cell (LUT4)             0.262 r     4.633
 wendu/cur_state[0]_syn_3822.a[0] (wendu/cur_state[0]_syn_3503) net  (fanout = 1)       0.456 r     5.089      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3822.f[0]                            cell (LUT4)             0.408 r     5.497
 wendu/cur_state[0]_syn_3772.d[1] (wendu/cur_state[0]_syn_3507) net  (fanout = 5)       0.470 r     5.967      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3772.f[1]                            cell (LUT3)             0.262 r     6.229
 wendu/cur_state[0]_syn_3780.d[1] (wendu/cur_state[0]_syn_3513) net  (fanout = 2)       0.456 r     6.685      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3780.f[1]                            cell (LUT2)             0.262 r     6.947
 wendu/cur_state[0]_syn_3782.a[1] (wendu/bit_cnt_b1_n)       net  (fanout = 4)       0.459 r     7.406                    
 wendu/cur_state[0]_syn_3782.f[1]                            cell (LUT2)             0.408 r     7.814
 wendu/cur_state[0]_syn_3766.d[1] (wendu/cur_state[0]_syn_3581) net  (fanout = 2)       0.307 r     8.121      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3766.f[1]                            cell (LUT4)             0.205 r     8.326
 wendu/reg2_syn_139.d[1] (wendu/cur_state[0]_syn_3583)       net  (fanout = 2)       0.669 r     8.995      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_139                                          path2reg1 (LUT2)        0.314       9.309
 Arrival time                                                                        9.309                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_139.clk (wendu/clk_us_syn_4)                 net                     3.150       3.150      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.150
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1003.034
 clock uncertainty                                                                  -0.000    1003.034
 clock recovergence pessimism                                                        0.293    1003.327
 Required time                                                                    1003.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             994.018ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point wendu/reg5_syn_115 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.375 ns                                                        
 Start Point:             wendu/reg5_syn_115.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_115.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.436ns  (logic 0.325ns, net 0.111ns, 74% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_115.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_115.q[0]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_115.d[1] (wendu/data_temp[4])                net  (fanout = 3)       0.111 r     3.026      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_115                                          path2reg1 (LUT4)        0.216       3.242
 Arrival time                                                                        3.242                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_115.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.375ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.619 ns                                                        
 Start Point:             wendu/reg5_syn_115.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_115.c[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.680ns  (logic 0.350ns, net 0.330ns, 51% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_115.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_115.q[1]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_115.c[1] (wendu/data_temp[3])                net  (fanout = 3)       0.330 r     3.245      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_115                                          path2reg1 (LUT4)        0.241       3.486
 Arrival time                                                                        3.486                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_115.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.619ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.256 ns                                                        
 Start Point:             wendu/reg3_syn_43.clk (rising edge triggered by clock clk_us)   
 End Point:               wendu/reg5_syn_115.b[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.362ns  (logic 0.579ns, net 0.783ns, 42% logic)                
 Logic Levels:            2 ( LUT4=1 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_43.clk (wendu/clk_us_syn_4)                  net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_43.q[1]                                      clk2q                   0.109 r     2.915
 wendu/cur_state[0]_syn_3800.c[1] (wendu/cur_state[5])       net  (fanout = 8)       0.442 r     3.357      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3800.f[1]                            cell (LUT3)             0.151 r     3.508
 wendu/reg5_syn_115.b[1] (wendu/cur_state[0]_syn_3562)       net  (fanout = 16)      0.341 r     3.849      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_115                                          path2reg1 (LUT4)        0.319       4.168
 Arrival time                                                                        4.168                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_115.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.231       2.912
 Required time                                                                       2.912            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.256ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg5_syn_112 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.381 ns                                                        
 Start Point:             wendu/reg5_syn_112.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_112.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.442ns  (logic 0.325ns, net 0.117ns, 73% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_112.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_112.q[0]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_112.d[1] (wendu/data_temp[6])                net  (fanout = 3)       0.117 r     3.032      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_112                                          path2reg1 (LUT4)        0.216       3.248
 Arrival time                                                                        3.248                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_112.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.381ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.628 ns                                                        
 Start Point:             wendu/reg5_syn_112.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_112.c[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.689ns  (logic 0.350ns, net 0.339ns, 50% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_112.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_112.q[1]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_112.c[1] (wendu/data_temp[5])                net  (fanout = 3)       0.339 r     3.254      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_112                                          path2reg1 (LUT4)        0.241       3.495
 Arrival time                                                                        3.495                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_112.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.628ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.105 ns                                                        
 Start Point:             wendu/reg3_syn_43.clk (rising edge triggered by clock clk_us)   
 End Point:               wendu/reg5_syn_112.a[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.211ns  (logic 0.626ns, net 0.585ns, 51% logic)                
 Logic Levels:            2 ( LUT4=1 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_43.clk (wendu/clk_us_syn_4)                  net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_43.q[1]                                      clk2q                   0.109 r     2.915
 wendu/cur_state[0]_syn_3809.c[0] (wendu/cur_state[5])       net  (fanout = 8)       0.240 r     3.155      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3809.f[0]                            cell (LUT3)             0.151 r     3.306
 wendu/reg5_syn_112.a[1] (wendu/cur_state[0]_syn_3560)       net  (fanout = 16)      0.345 r     3.651      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_112                                          path2reg1 (LUT4)        0.366       4.017
 Arrival time                                                                        4.017                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_112.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.231       2.912
 Required time                                                                       2.912            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.105ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg5_syn_103 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.381 ns                                                        
 Start Point:             wendu/reg5_syn_103.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_103.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.442ns  (logic 0.325ns, net 0.117ns, 73% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_103.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_103.q[0]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_103.d[1] (wendu/data_temp[13])               net  (fanout = 3)       0.117 r     3.032      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_103                                          path2reg1 (LUT4)        0.216       3.248
 Arrival time                                                                        3.248                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.381ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.814 ns                                                        
 Start Point:             wendu/reg5_syn_103.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_103.c[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.875ns  (logic 0.350ns, net 0.525ns, 40% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_103.clk (wendu/clk_us_syn_4)                 net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_103.q[1]                                     clk2q                   0.109 r     2.915
 wendu/reg5_syn_103.c[1] (wendu/data_temp[12])               net  (fanout = 3)       0.525 r     3.440      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_103                                          path2reg1 (LUT4)        0.241       3.681
 Arrival time                                                                        3.681                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.276       2.867
 Required time                                                                       2.867            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.814ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.105 ns                                                        
 Start Point:             wendu/reg3_syn_43.clk (rising edge triggered by clock clk_us)   
 End Point:               wendu/reg5_syn_103.a[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.211ns  (logic 0.626ns, net 0.585ns, 51% logic)                
 Logic Levels:            2 ( LUT4=1 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg3_syn_43.clk (wendu/clk_us_syn_4)                  net                     2.806       2.806      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.806
---------------------------------------------------------------------------------------------------------
 wendu/reg3_syn_43.q[1]                                      clk2q                   0.109 r     2.915
 wendu/cur_state[0]_syn_3809.c[0] (wendu/cur_state[5])       net  (fanout = 8)       0.240 r     3.155      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3809.f[0]                            cell (LUT3)             0.151 r     3.306
 wendu/reg5_syn_103.a[1] (wendu/cur_state[0]_syn_3560)       net  (fanout = 16)      0.345 r     3.651      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_103                                          path2reg1 (LUT4)        0.366       4.017
 Arrival time                                                                        4.017                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[0]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.082       3.082      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.082
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.143
 clock uncertainty                                                                   0.000       3.143
 clock recovergence pessimism                                                       -0.231       2.912
 Required time                                                                       2.912            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.105ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        Input delay                                                     
Set input delay: 14.5ns max, and 14.5ns min. 

24 endpoints analyzed totally, and 24 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest input path delay 1.224ns
---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[0]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     2.880 ns                                                        
 Start Point:             AD_DATA[0] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[0]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[0]                                                  input                  14.500      14.500
 AD_DATA[0]_syn_4.ipad (AD_DATA[0])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[0]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[0]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.200      18.604
 clock recovergence pessimism                                                        0.000      18.604
 Required time                                                                      18.604            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.880ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[1]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     2.880 ns                                                        
 Start Point:             AD_DATA[1] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[1]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[1]                                                  input                  14.500      14.500
 AD_DATA[1]_syn_4.ipad (AD_DATA[1])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[1]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[1]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.200      18.604
 clock recovergence pessimism                                                        0.000      18.604
 Required time                                                                      18.604            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.880ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[2]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     2.880 ns                                                        
 Start Point:             AD_DATA[2] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[2]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[2]                                                  input                  14.500      14.500
 AD_DATA[2]_syn_4.ipad (AD_DATA[2])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[2]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[2]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.200      18.604
 clock recovergence pessimism                                                        0.000      18.604
 Required time                                                                      18.604            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.880ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------
Paths for end point AD_DATA[0]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      12.926 ns                                                       
 Start Point:             AD_DATA[0] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[0]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[0]                                                  input                  14.500      14.500
 AD_DATA[0]_syn_4.ipad (AD_DATA[0])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[0]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[0]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.200       2.426
 clock recovergence pessimism                                                        0.000       2.426
 Required time                                                                       2.426            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              12.926ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[1]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      12.926 ns                                                       
 Start Point:             AD_DATA[1] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[1]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[1]                                                  input                  14.500      14.500
 AD_DATA[1]_syn_4.ipad (AD_DATA[1])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[1]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[1]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.200       2.426
 clock recovergence pessimism                                                        0.000       2.426
 Required time                                                                       2.426            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              12.926ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[2]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      12.926 ns                                                       
 Start Point:             AD_DATA[2] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[2]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[2]                                                  input                  14.500      14.500
 AD_DATA[2]_syn_4.ipad (AD_DATA[2])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(26)
 AD_DATA[2]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[2]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.200       2.426
 clock recovergence pessimism                                                        0.000       2.426
 Required time                                                                       2.426            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              12.926ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        Path Delay                                                      
Path delay: 8.032ns max

4 endpoints analyzed totally, and 4 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest path delay 0.985ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/sub0_syn_36 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        6.931 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_25.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/sub0_syn_36.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.985ns  (logic 0.289ns, net 0.696ns, 29% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_25.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_25.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/sub0_syn_36.mi[0] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[3]) net  (fanout = 1)       0.696 r     0.842      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/sub0_syn_36                      path2reg0               0.143       0.985
 Arrival time                                                                        0.985                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/sub0_syn_36.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.931ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.004 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_23.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.912ns  (logic 0.289ns, net 0.623ns, 31% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_23.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_23.q[1] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.mi[1] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[2]) net  (fanout = 1)       0.623 r     0.769      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16  path2reg1               0.143       0.912
 Arrival time                                                                        0.912                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_16.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.004ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.148 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_28.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.768ns  (logic 0.289ns, net 0.479ns, 37% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_28.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_28.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17.mi[0] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[0]) net  (fanout = 1)       0.479 r     0.625      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17  path2reg0               0.143       0.768
 Arrival time                                                                        0.768                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.148ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------

=========================================================================================================
Timing constraint:        Path Delay                                                      
Path delay: 8.032ns max

4 endpoints analyzed totally, and 4 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest path delay 0.986ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        6.930 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.986ns  (logic 0.289ns, net 0.697ns, 29% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.q[1] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[1] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[1]) net  (fanout = 1)       0.697 r     0.843      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26  path2reg1               0.143       0.986
 Arrival time                                                                        0.986                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.930ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.080 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_26.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.836ns  (logic 0.289ns, net 0.547ns, 34% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_26.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_26.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.mi[0] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[3]) net  (fanout = 1)       0.547 r     0.693      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26  path2reg0               0.143       0.836
 Arrival time                                                                        0.836                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_26.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.080ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.085 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.831ns  (logic 0.289ns, net 0.542ns, 34% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_24.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.mi[1] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0]) net  (fanout = 1)       0.542 r     0.688      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23  path2reg1               0.143       0.831
 Arrival time                                                                        0.831                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_23.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.085ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------

=========================================================================================================
Timing summary:                                                                           
---------------------------------------------------------------------------------------------------------
Constraint path number: 53982 (STA coverage = 96.02%)
Timing violations: 0 setup errors, and 0 hold errors.
Minimal setup slack: 0.582, minimal hold slack: 0.034

Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (120.0MHz)             7.751ns     129.000MHz        0.480ns       525        0.000ns
	  CLK120/pll_inst.clkc[3] (60.0MHz)             13.786ns      72.537MHz        0.326ns       103        0.000ns
	  clk_us (1000.0KHz)                             8.729ns     114.561MHz        0.326ns        44        0.000ns
	Minimum input arrival time before clock: 1.224ns
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              4     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              4     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

---------------------------------------------------------------------------------------------------------

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 23 18:42:07 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1563 instances
RUN-0007 : 376 luts, 946 seqs, 120 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2082 nets
RUN-1001 : 1536 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1561 instances, 376 luts, 946 seqs, 190 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7427, tnet num: 2080, tinst num: 1561, tnode num: 10528, tedge num: 12541.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.251225s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 533071
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1561.
PHY-3001 : End clustering;  0.000025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461089, overlap = 20.25
PHY-3002 : Step(2): len = 437058, overlap = 18
PHY-3002 : Step(3): len = 423241, overlap = 20.25
PHY-3002 : Step(4): len = 412133, overlap = 13.5
PHY-3002 : Step(5): len = 399227, overlap = 18
PHY-3002 : Step(6): len = 384749, overlap = 13.5
PHY-3002 : Step(7): len = 376976, overlap = 11.25
PHY-3002 : Step(8): len = 367478, overlap = 13.5
PHY-3002 : Step(9): len = 352447, overlap = 15.75
PHY-3002 : Step(10): len = 345222, overlap = 15.75
PHY-3002 : Step(11): len = 338258, overlap = 15.75
PHY-3002 : Step(12): len = 321904, overlap = 13.5
PHY-3002 : Step(13): len = 313823, overlap = 13.5
PHY-3002 : Step(14): len = 308677, overlap = 15.75
PHY-3002 : Step(15): len = 297259, overlap = 15.75
PHY-3002 : Step(16): len = 290044, overlap = 13.5
PHY-3002 : Step(17): len = 285524, overlap = 13.5
PHY-3002 : Step(18): len = 276672, overlap = 13.5
PHY-3002 : Step(19): len = 268499, overlap = 13.5
PHY-3002 : Step(20): len = 263688, overlap = 13.5
PHY-3002 : Step(21): len = 259205, overlap = 13.5
PHY-3002 : Step(22): len = 249233, overlap = 20.25
PHY-3002 : Step(23): len = 243575, overlap = 20.25
PHY-3002 : Step(24): len = 240477, overlap = 20.25
PHY-3002 : Step(25): len = 228189, overlap = 20.25
PHY-3002 : Step(26): len = 218784, overlap = 20.25
PHY-3002 : Step(27): len = 216342, overlap = 20.25
PHY-3002 : Step(28): len = 209453, overlap = 20.25
PHY-3002 : Step(29): len = 190158, overlap = 20.25
PHY-3002 : Step(30): len = 185322, overlap = 20.25
PHY-3002 : Step(31): len = 183357, overlap = 20.25
PHY-3002 : Step(32): len = 144784, overlap = 18
PHY-3002 : Step(33): len = 131666, overlap = 20.25
PHY-3002 : Step(34): len = 130400, overlap = 20.25
PHY-3002 : Step(35): len = 126411, overlap = 20.25
PHY-3002 : Step(36): len = 122327, overlap = 20.25
PHY-3002 : Step(37): len = 120583, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100918
PHY-3002 : Step(38): len = 120755, overlap = 15.75
PHY-3002 : Step(39): len = 119458, overlap = 15.75
PHY-3002 : Step(40): len = 118146, overlap = 18
PHY-3002 : Step(41): len = 117446, overlap = 15.75
PHY-3002 : Step(42): len = 115437, overlap = 15.75
PHY-3002 : Step(43): len = 111385, overlap = 13.5
PHY-3002 : Step(44): len = 109280, overlap = 9
PHY-3002 : Step(45): len = 108101, overlap = 9
PHY-3002 : Step(46): len = 104711, overlap = 13.5
PHY-3002 : Step(47): len = 103914, overlap = 11.25
PHY-3002 : Step(48): len = 101511, overlap = 11.25
PHY-3002 : Step(49): len = 99249.3, overlap = 13.5
PHY-3002 : Step(50): len = 96178.5, overlap = 13.5
PHY-3002 : Step(51): len = 95499.7, overlap = 13.5
PHY-3002 : Step(52): len = 93735, overlap = 13.5
PHY-3002 : Step(53): len = 89632.6, overlap = 13.5
PHY-3002 : Step(54): len = 86467, overlap = 13.5
PHY-3002 : Step(55): len = 86357, overlap = 13.5
PHY-3002 : Step(56): len = 84984.4, overlap = 13.5
PHY-3002 : Step(57): len = 82986.4, overlap = 18
PHY-3002 : Step(58): len = 83059.2, overlap = 15.75
PHY-3002 : Step(59): len = 81145.7, overlap = 15.75
PHY-3002 : Step(60): len = 79442.3, overlap = 15.75
PHY-3002 : Step(61): len = 76794.6, overlap = 15.75
PHY-3002 : Step(62): len = 76486.1, overlap = 15.75
PHY-3002 : Step(63): len = 74782.7, overlap = 18.25
PHY-3002 : Step(64): len = 72168.3, overlap = 18.1875
PHY-3002 : Step(65): len = 70634.4, overlap = 18.5625
PHY-3002 : Step(66): len = 69303.5, overlap = 18.8125
PHY-3002 : Step(67): len = 68249.3, overlap = 18.8125
PHY-3002 : Step(68): len = 68042.1, overlap = 18.75
PHY-3002 : Step(69): len = 67118.6, overlap = 19.3125
PHY-3002 : Step(70): len = 65545.4, overlap = 19.25
PHY-3002 : Step(71): len = 65260.3, overlap = 19.25
PHY-3002 : Step(72): len = 64778.5, overlap = 19.375
PHY-3002 : Step(73): len = 64362.1, overlap = 19.5625
PHY-3002 : Step(74): len = 64107.7, overlap = 19.4375
PHY-3002 : Step(75): len = 63751.1, overlap = 19.3125
PHY-3002 : Step(76): len = 62985.9, overlap = 19.125
PHY-3002 : Step(77): len = 61968.8, overlap = 18.9375
PHY-3002 : Step(78): len = 61627.8, overlap = 19.25
PHY-3002 : Step(79): len = 61187.9, overlap = 19.0625
PHY-3002 : Step(80): len = 61045.9, overlap = 18.8125
PHY-3002 : Step(81): len = 60924.8, overlap = 16.75
PHY-3002 : Step(82): len = 60162.9, overlap = 16.875
PHY-3002 : Step(83): len = 59627.3, overlap = 16.625
PHY-3002 : Step(84): len = 58537.7, overlap = 16.9375
PHY-3002 : Step(85): len = 57091, overlap = 16.5625
PHY-3002 : Step(86): len = 57091, overlap = 17
PHY-3002 : Step(87): len = 56442.3, overlap = 17
PHY-3002 : Step(88): len = 55918, overlap = 14.9375
PHY-3002 : Step(89): len = 55856.2, overlap = 14.625
PHY-3002 : Step(90): len = 55453.5, overlap = 14.6875
PHY-3002 : Step(91): len = 54909.4, overlap = 16.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000201837
PHY-3002 : Step(92): len = 55335.8, overlap = 16.875
PHY-3002 : Step(93): len = 55478.7, overlap = 16.875
PHY-3002 : Step(94): len = 55588.5, overlap = 16.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000403674
PHY-3002 : Step(95): len = 55602.8, overlap = 16.875
PHY-3002 : Step(96): len = 55610.9, overlap = 16.875
PHY-3001 : Before Legalized: Len = 55610.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008013s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 60118, Over = 3.375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059972s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(97): len = 60175.9, overlap = 15.5312
PHY-3002 : Step(98): len = 60371.8, overlap = 15.4062
PHY-3002 : Step(99): len = 59518.9, overlap = 15.2188
PHY-3002 : Step(100): len = 59597.5, overlap = 15.0312
PHY-3002 : Step(101): len = 59093.6, overlap = 14.6562
PHY-3002 : Step(102): len = 58452, overlap = 14.9062
PHY-3002 : Step(103): len = 58032.9, overlap = 14.875
PHY-3002 : Step(104): len = 57221.9, overlap = 15.4688
PHY-3002 : Step(105): len = 56218.1, overlap = 16.6562
PHY-3002 : Step(106): len = 55983.6, overlap = 17.1562
PHY-3002 : Step(107): len = 55595.6, overlap = 17.4062
PHY-3002 : Step(108): len = 54915, overlap = 17.9688
PHY-3002 : Step(109): len = 54167.9, overlap = 19.0625
PHY-3002 : Step(110): len = 53862.3, overlap = 19.0625
PHY-3002 : Step(111): len = 53132.9, overlap = 20.875
PHY-3002 : Step(112): len = 52950.5, overlap = 24.3438
PHY-3002 : Step(113): len = 52238.7, overlap = 28.6875
PHY-3002 : Step(114): len = 51660.2, overlap = 29.5
PHY-3002 : Step(115): len = 51394.2, overlap = 29.2812
PHY-3002 : Step(116): len = 50695.2, overlap = 28.6562
PHY-3002 : Step(117): len = 50456.8, overlap = 28.2188
PHY-3002 : Step(118): len = 50250, overlap = 27.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00234831
PHY-3002 : Step(119): len = 50075.1, overlap = 27.4375
PHY-3002 : Step(120): len = 50090.2, overlap = 27.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00469663
PHY-3002 : Step(121): len = 49961, overlap = 27.125
PHY-3002 : Step(122): len = 49955.1, overlap = 25.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058526s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.33434e-05
PHY-3002 : Step(123): len = 50081.6, overlap = 64.75
PHY-3002 : Step(124): len = 51655.6, overlap = 59.2188
PHY-3002 : Step(125): len = 52123.8, overlap = 57.2812
PHY-3002 : Step(126): len = 51440.9, overlap = 48.9062
PHY-3002 : Step(127): len = 51346.7, overlap = 49.75
PHY-3002 : Step(128): len = 51744.6, overlap = 48.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106687
PHY-3002 : Step(129): len = 51457.8, overlap = 48.0625
PHY-3002 : Step(130): len = 51809.7, overlap = 47.3125
PHY-3002 : Step(131): len = 52309.9, overlap = 47.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000213374
PHY-3002 : Step(132): len = 52253.8, overlap = 46.875
PHY-3002 : Step(133): len = 52403.8, overlap = 47.0625
PHY-3002 : Step(134): len = 52366.5, overlap = 46.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000399851
PHY-3002 : Step(135): len = 53339.7, overlap = 45.8125
PHY-3002 : Step(136): len = 53915.9, overlap = 45.0312
PHY-3002 : Step(137): len = 54704, overlap = 42.3438
PHY-3002 : Step(138): len = 54952.9, overlap = 37.6562
PHY-3002 : Step(139): len = 54573.2, overlap = 36.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7427, tnet num: 2080, tinst num: 1561, tnode num: 10528, tedge num: 12541.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.69 peak overflow 2.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57736, over cnt = 227(0%), over = 961, worst = 17
PHY-1001 : End global iterations;  0.073739s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (127.1%)

PHY-1001 : Congestion index: top1 = 42.20, top5 = 24.84, top10 = 15.95, top15 = 11.38.
PHY-1001 : End incremental global routing;  0.123007s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (114.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064070s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216278s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.4%)

OPT-1001 : Current memory(MB): used = 209, reserve = 175, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1609/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57736, over cnt = 227(0%), over = 961, worst = 17
PHY-1002 : len = 66488, over cnt = 145(0%), over = 242, worst = 7
PHY-1002 : len = 68904, over cnt = 30(0%), over = 56, worst = 6
PHY-1002 : len = 69360, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089235s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (140.1%)

PHY-1001 : Congestion index: top1 = 36.92, top5 = 24.03, top10 = 17.50, top15 = 13.09.
OPT-1001 : End congestion update;  0.132463s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (118.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059458s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.195009s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.2%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.653973s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (107.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 191 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 650 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1026/1331 primitive instances ...
PHY-3001 : End packing;  0.051432s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 777 instances
RUN-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1899 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 68 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 775 instances, 726 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54783, Over = 58.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6191, tnet num: 1897, tinst num: 775, tnode num: 8422, tedge num: 10892.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276532s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.63282e-05
PHY-3002 : Step(140): len = 54277.4, overlap = 57.5
PHY-3002 : Step(141): len = 53706.8, overlap = 59.5
PHY-3002 : Step(142): len = 53513.9, overlap = 57.75
PHY-3002 : Step(143): len = 53599.3, overlap = 55.5
PHY-3002 : Step(144): len = 53482.4, overlap = 53.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.26565e-05
PHY-3002 : Step(145): len = 53764.2, overlap = 52.75
PHY-3002 : Step(146): len = 54519.8, overlap = 50
PHY-3002 : Step(147): len = 55014.7, overlap = 49.25
PHY-3002 : Step(148): len = 55257.1, overlap = 47
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000185313
PHY-3002 : Step(149): len = 55933.8, overlap = 46
PHY-3002 : Step(150): len = 56552, overlap = 40.25
PHY-3001 : Before Legalized: Len = 56552
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.112325s wall, 0.140625s user + 0.093750s system = 0.234375s CPU (208.7%)

PHY-3001 : After Legalized: Len = 69113.8, Over = 0
PHY-3001 : Trial Legalized: Len = 69113.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048426s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00181968
PHY-3002 : Step(151): len = 65947.4, overlap = 2.5
PHY-3002 : Step(152): len = 64237.3, overlap = 8.5
PHY-3002 : Step(153): len = 62378.6, overlap = 13.5
PHY-3002 : Step(154): len = 61012.5, overlap = 16
PHY-3002 : Step(155): len = 59917.3, overlap = 20
PHY-3002 : Step(156): len = 59409, overlap = 23.5
PHY-3002 : Step(157): len = 58801.9, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00363937
PHY-3002 : Step(158): len = 58943.8, overlap = 24.5
PHY-3002 : Step(159): len = 58968.1, overlap = 24.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00727873
PHY-3002 : Step(160): len = 58998.9, overlap = 24.5
PHY-3002 : Step(161): len = 58998.9, overlap = 24.5
PHY-3001 : Before Legalized: Len = 58998.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005019s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (311.3%)

PHY-3001 : After Legalized: Len = 63372.3, Over = 0
PHY-3001 : Legalized: Len = 63372.3, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006144s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 2, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 63508.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6191, tnet num: 1897, tinst num: 775, tnode num: 8422, tedge num: 10892.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 66/1899.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70192, over cnt = 160(0%), over = 260, worst = 6
PHY-1002 : len = 71368, over cnt = 70(0%), over = 88, worst = 4
PHY-1002 : len = 72536, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109654s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (171.0%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.07, top10 = 17.73, top15 = 13.69.
PHY-1001 : End incremental global routing;  0.161172s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (155.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055992s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.245366s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (133.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1694/1899.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006932s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.07, top10 = 17.73, top15 = 13.69.
OPT-1001 : End congestion update;  0.055475s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055390s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 735 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 775 instances, 726 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63508, Over = 0
PHY-3001 : End spreading;  0.004915s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63508, Over = 0
PHY-3001 : End incremental legalization;  0.035370s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (220.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.158939s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (118.0%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051925s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1690/1899.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007042s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.03, top10 = 17.70, top15 = 13.67.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046630s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.833632s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (110.6%)

RUN-1003 : finish command "place" in  5.211308s wall, 6.843750s user + 3.281250s system = 10.125000s CPU (194.3%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 777 instances
RUN-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1899 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 68 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6191, tnet num: 1897, tinst num: 775, tnode num: 8422, tedge num: 10892.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 363 mslices, 363 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1897 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69208, over cnt = 155(0%), over = 249, worst = 6
PHY-1002 : len = 70504, over cnt = 63(0%), over = 77, worst = 4
PHY-1002 : len = 71552, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127763s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (171.2%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 22.76, top10 = 17.48, top15 = 13.50.
PHY-1001 : End global routing;  0.176811s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (150.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 203, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.076195s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 28856, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.081766s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.088463s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (101.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 172456, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End initial routed; 1.725404s wall, 2.328125s user + 0.187500s system = 2.515625s CPU (145.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1690(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.738  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.326354s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 529, reserve = 502, peak = 529.
PHY-1001 : End phase 2; 2.051865s wall, 2.640625s user + 0.203125s system = 2.843750s CPU (138.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 172456, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014959s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 172360, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029567s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (158.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 172312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026697s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (175.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1690(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.738  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.337908s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.163799s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.4%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 0.703786s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (104.3%)

PHY-1003 : Routed, final wirelength = 172312
PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End export database. 0.009658s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (161.8%)

PHY-1001 : End detail routing;  7.099926s wall, 7.625000s user + 0.312500s system = 7.937500s CPU (111.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6191, tnet num: 1897, tinst num: 775, tnode num: 8422, tedge num: 10892.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg1_syn_218.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg1_syn_220.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_100.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_100.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_80.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_91.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_91.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_94.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_94.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_97.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_97.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6315, tnet num: 1959, tinst num: 837, tnode num: 8546, tedge num: 11016.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -313ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -485ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[0] slack -274ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[0] slack -448ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[1] slack -290ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[1] slack -48ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[1] slack -667ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[0] slack -468ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -167ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -461ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -470ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -904ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg1_syn_218_mi[0] slack -221ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_100_mi[1] slack -360ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_100_mi[0] slack -617ps
RUN-1001 : End hold fix;  3.078544s wall, 3.125000s user + 0.343750s system = 3.468750s CPU (112.7%)

RUN-1003 : finish command "route" in  10.670702s wall, 11.296875s user + 0.687500s system = 11.984375s CPU (112.3%)

RUN-1004 : used memory is 535 MB, reserved memory is 509 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      882   out of  19600    4.50%
#reg                     1019   out of  19600    5.20%
#le                      1532
  #lut only               513   out of   1532   33.49%
  #reg only               650   out of   1532   42.43%
  #lut&reg                369   out of   1532   24.09%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         456
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         103
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1532   |692     |190     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1028   |288     |126     |828     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |20      |4       |21      |0       |0       |
|    demodu                  |Demodulation                                     |460    |109     |46      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |53     |31      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |7       |0       |12      |0       |0       |
|    integ                   |Integration                                      |141    |35      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |64     |41      |7       |62      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |66      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |17      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |172    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |109    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |308    |263     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1379  
    #2          2       306   
    #3          3       107   
    #4          4        20   
    #5        5-10       74   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6315, tnet num: 1959, tinst num: 837, tnode num: 8546, tedge num: 11016.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1959 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 837
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1961, pip num: 14370
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1307 valid insts, and 38493 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.091396s wall, 17.687500s user + 0.078125s system = 17.765625s CPU (574.7%)

RUN-1004 : used memory is 545 MB, reserved memory is 515 MB, peak memory is 679 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250723_184207.log"

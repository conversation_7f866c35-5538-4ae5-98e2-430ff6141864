============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Mon Aug 18 16:28:21 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(77)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6717328850944"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1387274436608"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1849 instances
RUN-0007 : 489 luts, 1065 seqs, 158 mslices, 86 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2436 nets
RUN-1001 : 1748 nets have 2 pins
RUN-1001 : 563 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     276     
RUN-1001 :   No   |  No   |  Yes  |     240     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     307     
RUN-1001 :   Yes  |  No   |  Yes  |     185     
RUN-1001 :   Yes  |  Yes  |  No   |     56      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  14   |     5      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1847 instances, 489 luts, 1065 seqs, 244 slices, 31 macros(244 instances: 158 mslices 86 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8714, tnet num: 2434, tinst num: 1847, tnode num: 12104, tedge num: 14647.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.356214s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 656723
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1847.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 580984, overlap = 18
PHY-3002 : Step(2): len = 548297, overlap = 20.25
PHY-3002 : Step(3): len = 531239, overlap = 18
PHY-3002 : Step(4): len = 507361, overlap = 15.75
PHY-3002 : Step(5): len = 492027, overlap = 18
PHY-3002 : Step(6): len = 481614, overlap = 18
PHY-3002 : Step(7): len = 466419, overlap = 18
PHY-3002 : Step(8): len = 453953, overlap = 20.25
PHY-3002 : Step(9): len = 442033, overlap = 13.5
PHY-3002 : Step(10): len = 431770, overlap = 18
PHY-3002 : Step(11): len = 420733, overlap = 11.25
PHY-3002 : Step(12): len = 410162, overlap = 15.75
PHY-3002 : Step(13): len = 400780, overlap = 9
PHY-3002 : Step(14): len = 391801, overlap = 11.25
PHY-3002 : Step(15): len = 381743, overlap = 11.25
PHY-3002 : Step(16): len = 372682, overlap = 11.25
PHY-3002 : Step(17): len = 364325, overlap = 11.25
PHY-3002 : Step(18): len = 355938, overlap = 11.25
PHY-3002 : Step(19): len = 345319, overlap = 11.25
PHY-3002 : Step(20): len = 338130, overlap = 11.25
PHY-3002 : Step(21): len = 331498, overlap = 11.25
PHY-3002 : Step(22): len = 291188, overlap = 9
PHY-3002 : Step(23): len = 269764, overlap = 13.5
PHY-3002 : Step(24): len = 260953, overlap = 6.75
PHY-3002 : Step(25): len = 259013, overlap = 6.75
PHY-3002 : Step(26): len = 254757, overlap = 9
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000353708
PHY-3002 : Step(27): len = 256033, overlap = 9
PHY-3002 : Step(28): len = 252708, overlap = 6.75
PHY-3002 : Step(29): len = 251438, overlap = 4.5
PHY-3002 : Step(30): len = 249272, overlap = 6.75
PHY-3002 : Step(31): len = 244624, overlap = 6.75
PHY-3002 : Step(32): len = 241696, overlap = 6.75
PHY-3002 : Step(33): len = 238342, overlap = 6.75
PHY-3002 : Step(34): len = 234463, overlap = 6.75
PHY-3002 : Step(35): len = 227268, overlap = 4.5
PHY-3002 : Step(36): len = 226357, overlap = 4.5
PHY-3002 : Step(37): len = 224978, overlap = 4.5
PHY-3002 : Step(38): len = 219143, overlap = 4.5
PHY-3002 : Step(39): len = 214309, overlap = 6.75
PHY-3002 : Step(40): len = 211280, overlap = 2.25
PHY-3002 : Step(41): len = 209424, overlap = 4.5
PHY-3002 : Step(42): len = 206233, overlap = 4.5
PHY-3002 : Step(43): len = 204543, overlap = 2.25
PHY-3002 : Step(44): len = 201641, overlap = 0
PHY-3002 : Step(45): len = 200388, overlap = 4.5
PHY-3002 : Step(46): len = 197084, overlap = 2.25
PHY-3002 : Step(47): len = 194741, overlap = 2.25
PHY-3002 : Step(48): len = 192058, overlap = 2.25
PHY-3002 : Step(49): len = 190887, overlap = 4.5
PHY-3002 : Step(50): len = 186381, overlap = 0
PHY-3002 : Step(51): len = 184447, overlap = 4.5
PHY-3002 : Step(52): len = 182867, overlap = 4.5
PHY-3002 : Step(53): len = 181479, overlap = 4.5
PHY-3002 : Step(54): len = 173603, overlap = 4.5
PHY-3002 : Step(55): len = 171802, overlap = 4.5
PHY-3002 : Step(56): len = 170141, overlap = 4.5
PHY-3002 : Step(57): len = 168066, overlap = 2.25
PHY-3002 : Step(58): len = 164008, overlap = 2.25
PHY-3002 : Step(59): len = 162814, overlap = 2.25
PHY-3002 : Step(60): len = 160828, overlap = 2.25
PHY-3002 : Step(61): len = 160048, overlap = 4.5
PHY-3002 : Step(62): len = 156508, overlap = 4.5
PHY-3002 : Step(63): len = 154280, overlap = 4.5
PHY-3002 : Step(64): len = 152227, overlap = 4.5
PHY-3002 : Step(65): len = 151550, overlap = 4.5
PHY-3002 : Step(66): len = 148372, overlap = 2.25
PHY-3002 : Step(67): len = 147192, overlap = 2.25
PHY-3002 : Step(68): len = 144895, overlap = 0
PHY-3002 : Step(69): len = 143737, overlap = 4.5
PHY-3002 : Step(70): len = 141737, overlap = 4.5
PHY-3002 : Step(71): len = 141086, overlap = 4.5
PHY-3002 : Step(72): len = 136353, overlap = 4.5
PHY-3002 : Step(73): len = 135673, overlap = 2.25
PHY-3002 : Step(74): len = 134300, overlap = 2.25
PHY-3002 : Step(75): len = 132475, overlap = 4.5
PHY-3002 : Step(76): len = 130261, overlap = 4.5
PHY-3002 : Step(77): len = 129656, overlap = 4.5
PHY-3002 : Step(78): len = 127652, overlap = 2.25
PHY-3002 : Step(79): len = 126672, overlap = 2.25
PHY-3002 : Step(80): len = 124071, overlap = 2.25
PHY-3002 : Step(81): len = 118833, overlap = 4.5
PHY-3002 : Step(82): len = 116667, overlap = 4.5
PHY-3002 : Step(83): len = 116385, overlap = 2.25
PHY-3002 : Step(84): len = 115637, overlap = 2.25
PHY-3002 : Step(85): len = 114508, overlap = 2.25
PHY-3002 : Step(86): len = 112259, overlap = 4.5
PHY-3002 : Step(87): len = 111591, overlap = 4.5
PHY-3002 : Step(88): len = 110357, overlap = 4.5
PHY-3002 : Step(89): len = 109611, overlap = 4.5
PHY-3002 : Step(90): len = 107862, overlap = 2.25
PHY-3002 : Step(91): len = 104058, overlap = 4.5
PHY-3002 : Step(92): len = 102689, overlap = 4.5
PHY-3002 : Step(93): len = 102053, overlap = 2.25
PHY-3002 : Step(94): len = 101506, overlap = 2.25
PHY-3002 : Step(95): len = 100660, overlap = 2.25
PHY-3002 : Step(96): len = 100058, overlap = 4.5
PHY-3002 : Step(97): len = 99227.5, overlap = 4.5
PHY-3002 : Step(98): len = 97989.6, overlap = 4.5
PHY-3002 : Step(99): len = 97145.2, overlap = 6.75
PHY-3002 : Step(100): len = 95769.6, overlap = 4.5
PHY-3002 : Step(101): len = 95239.2, overlap = 4.5
PHY-3002 : Step(102): len = 91785, overlap = 4.5
PHY-3002 : Step(103): len = 90546, overlap = 4.5
PHY-3002 : Step(104): len = 89369.7, overlap = 4.5
PHY-3002 : Step(105): len = 89112.2, overlap = 4.5
PHY-3002 : Step(106): len = 88063.7, overlap = 4.5
PHY-3002 : Step(107): len = 85883.2, overlap = 4.5
PHY-3002 : Step(108): len = 84945.1, overlap = 4.5
PHY-3002 : Step(109): len = 84501, overlap = 4.5
PHY-3002 : Step(110): len = 83996.3, overlap = 6.75
PHY-3002 : Step(111): len = 83212.9, overlap = 4.5
PHY-3002 : Step(112): len = 82344.8, overlap = 6.75
PHY-3002 : Step(113): len = 82059, overlap = 4.5
PHY-3002 : Step(114): len = 80711.3, overlap = 4.5
PHY-3002 : Step(115): len = 79279.3, overlap = 4.5
PHY-3002 : Step(116): len = 79131.9, overlap = 4.5
PHY-3002 : Step(117): len = 78497.7, overlap = 4.5
PHY-3002 : Step(118): len = 77246.3, overlap = 4.5
PHY-3002 : Step(119): len = 76868.6, overlap = 4.5
PHY-3002 : Step(120): len = 76815.4, overlap = 4.5
PHY-3002 : Step(121): len = 76600.6, overlap = 4.5
PHY-3002 : Step(122): len = 76212.3, overlap = 4.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000707416
PHY-3002 : Step(123): len = 76469.4, overlap = 4.5
PHY-3002 : Step(124): len = 76436, overlap = 4.5
PHY-3002 : Step(125): len = 76355.4, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00141483
PHY-3002 : Step(126): len = 76396.6, overlap = 4.5
PHY-3002 : Step(127): len = 76369.1, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011661s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (134.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078474s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(128): len = 77021.4, overlap = 2.625
PHY-3002 : Step(129): len = 74249.1, overlap = 3.09375
PHY-3002 : Step(130): len = 72324.1, overlap = 2.90625
PHY-3002 : Step(131): len = 71006.5, overlap = 4
PHY-3002 : Step(132): len = 68769.7, overlap = 4.71875
PHY-3002 : Step(133): len = 67476.8, overlap = 6.46875
PHY-3002 : Step(134): len = 65513.5, overlap = 7.1875
PHY-3002 : Step(135): len = 64353, overlap = 7.625
PHY-3002 : Step(136): len = 62660.9, overlap = 8.4375
PHY-3002 : Step(137): len = 60619.4, overlap = 9.1875
PHY-3002 : Step(138): len = 58267.9, overlap = 12.3125
PHY-3002 : Step(139): len = 57256.3, overlap = 12.3125
PHY-3002 : Step(140): len = 56292.3, overlap = 12
PHY-3002 : Step(141): len = 54955.7, overlap = 12
PHY-3002 : Step(142): len = 54827, overlap = 12.0625
PHY-3002 : Step(143): len = 54052.5, overlap = 12.4375
PHY-3002 : Step(144): len = 53673.1, overlap = 11.6875
PHY-3002 : Step(145): len = 52751.9, overlap = 12.25
PHY-3002 : Step(146): len = 52464.4, overlap = 12.375
PHY-3002 : Step(147): len = 52418.7, overlap = 12.5625
PHY-3002 : Step(148): len = 52385.5, overlap = 12.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00792419
PHY-3002 : Step(149): len = 52106.3, overlap = 12.5
PHY-3002 : Step(150): len = 51968.7, overlap = 12.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0158484
PHY-3002 : Step(151): len = 51871.1, overlap = 12.5625
PHY-3002 : Step(152): len = 51894.2, overlap = 12.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.080896s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000106736
PHY-3002 : Step(153): len = 51818.9, overlap = 71.375
PHY-3002 : Step(154): len = 51863.9, overlap = 69.625
PHY-3002 : Step(155): len = 52028.1, overlap = 73.875
PHY-3002 : Step(156): len = 51697.8, overlap = 73.5
PHY-3002 : Step(157): len = 51503.4, overlap = 73.6875
PHY-3002 : Step(158): len = 51580.1, overlap = 73.5
PHY-3002 : Step(159): len = 51377.5, overlap = 73.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000213472
PHY-3002 : Step(160): len = 51297.5, overlap = 73.3438
PHY-3002 : Step(161): len = 51889.4, overlap = 71.6875
PHY-3002 : Step(162): len = 52837.1, overlap = 68.8438
PHY-3002 : Step(163): len = 52662.6, overlap = 67.9375
PHY-3002 : Step(164): len = 52893, overlap = 68.1875
PHY-3002 : Step(165): len = 53255.9, overlap = 67.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000426944
PHY-3002 : Step(166): len = 53173.8, overlap = 66.5938
PHY-3002 : Step(167): len = 53275.9, overlap = 63.5625
PHY-3002 : Step(168): len = 53349.6, overlap = 62.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000853888
PHY-3002 : Step(169): len = 53564.8, overlap = 58.8438
PHY-3002 : Step(170): len = 54438.7, overlap = 56.6562
PHY-3002 : Step(171): len = 55304.1, overlap = 54.5625
PHY-3002 : Step(172): len = 55202.8, overlap = 54.25
PHY-3002 : Step(173): len = 55178.3, overlap = 53.9375
PHY-3002 : Step(174): len = 55066, overlap = 52.2188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00170778
PHY-3002 : Step(175): len = 55153.2, overlap = 52.0625
PHY-3002 : Step(176): len = 55360.8, overlap = 51
PHY-3002 : Step(177): len = 55551, overlap = 49.7188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00341555
PHY-3002 : Step(178): len = 55920.6, overlap = 40.2812
PHY-3002 : Step(179): len = 56186.2, overlap = 34.875
PHY-3002 : Step(180): len = 56391.4, overlap = 34.3438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8714, tnet num: 2434, tinst num: 1847, tnode num: 12104, tedge num: 14647.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 106.31 peak overflow 2.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2436.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 60816, over cnt = 267(0%), over = 1098, worst = 20
PHY-1001 : End global iterations;  0.121368s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 44.66, top5 = 26.71, top10 = 17.68, top15 = 12.83.
PHY-1001 : End incremental global routing;  0.192636s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (113.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098296s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (111.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.330705s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1902/2436.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 60816, over cnt = 267(0%), over = 1098, worst = 20
PHY-1002 : len = 68784, over cnt = 208(0%), over = 417, worst = 12
PHY-1002 : len = 72704, over cnt = 58(0%), over = 89, worst = 7
PHY-1002 : len = 74296, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 74512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136908s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (114.1%)

PHY-1001 : Congestion index: top1 = 40.65, top5 = 26.33, top10 = 19.32, top15 = 14.65.
OPT-1001 : End congestion update;  0.190326s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076551s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.271010s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (109.5%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : End physical optimization;  0.954777s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (104.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 489 LUT to BLE ...
SYN-4008 : Packed 489 LUT and 280 SEQ to BLE.
SYN-4003 : Packing 785 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 127 single LUT's are left
SYN-4006 : 678 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1167/1526 primitive instances ...
PHY-3001 : End packing;  0.102546s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (91.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 915 instances
RUN-1001 : 432 mslices, 432 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2221 nets
RUN-1001 : 1592 nets have 2 pins
RUN-1001 : 504 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 913 instances, 864 slices, 31 macros(244 instances: 158 mslices 86 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : After packing: Len = 55725.8, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7317, tnet num: 2219, tinst num: 913, tnode num: 9777, tedge num: 13003.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.418404s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (97.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.20418e-05
PHY-3002 : Step(181): len = 54796.4, overlap = 57
PHY-3002 : Step(182): len = 53738.8, overlap = 59.25
PHY-3002 : Step(183): len = 53004.3, overlap = 64.25
PHY-3002 : Step(184): len = 52444.8, overlap = 65
PHY-3002 : Step(185): len = 52077.3, overlap = 66.25
PHY-3002 : Step(186): len = 51706, overlap = 66.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.40837e-05
PHY-3002 : Step(187): len = 52194.9, overlap = 66.25
PHY-3002 : Step(188): len = 52855.2, overlap = 65
PHY-3002 : Step(189): len = 54113.1, overlap = 62.75
PHY-3002 : Step(190): len = 54373, overlap = 62
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000168167
PHY-3002 : Step(191): len = 55908, overlap = 53.75
PHY-3002 : Step(192): len = 57040.1, overlap = 50.75
PHY-3002 : Step(193): len = 57235.3, overlap = 50.5
PHY-3002 : Step(194): len = 57047.1, overlap = 49
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.142551s wall, 0.171875s user + 0.203125s system = 0.375000s CPU (263.1%)

PHY-3001 : Trial Legalized: Len = 70940.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066381s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000740525
PHY-3002 : Step(195): len = 67514.4, overlap = 5
PHY-3002 : Step(196): len = 65400.4, overlap = 12
PHY-3002 : Step(197): len = 63247.6, overlap = 17
PHY-3002 : Step(198): len = 62028.8, overlap = 19.75
PHY-3002 : Step(199): len = 61324.2, overlap = 23.5
PHY-3002 : Step(200): len = 60829, overlap = 24.5
PHY-3002 : Step(201): len = 60488.3, overlap = 26.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00148105
PHY-3002 : Step(202): len = 61043.1, overlap = 26.25
PHY-3002 : Step(203): len = 61168.8, overlap = 25.75
PHY-3002 : Step(204): len = 60956.8, overlap = 27
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0029621
PHY-3002 : Step(205): len = 61338.6, overlap = 26.25
PHY-3002 : Step(206): len = 61513.7, overlap = 26
PHY-3002 : Step(207): len = 61545.4, overlap = 25.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006593s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 67122.3, Over = 0
PHY-3001 : Spreading special nets. 26 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009146s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 37 instances has been re-located, deltaX = 24, deltaY = 16, maxDist = 2.
PHY-3001 : Final: Len = 67676.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7317, tnet num: 2219, tinst num: 913, tnode num: 9777, tedge num: 13003.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/2221.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 75416, over cnt = 157(0%), over = 280, worst = 8
PHY-1002 : len = 76816, over cnt = 104(0%), over = 135, worst = 5
PHY-1002 : len = 78352, over cnt = 8(0%), over = 10, worst = 2
PHY-1002 : len = 78512, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 78536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.212625s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (117.6%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.93, top10 = 18.71, top15 = 14.94.
PHY-1001 : End incremental global routing;  0.281756s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (110.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.078645s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.403365s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (108.5%)

OPT-1001 : Current memory(MB): used = 230, reserve = 194, peak = 230.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1974/2221.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 78536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009205s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.93, top10 = 18.71, top15 = 14.94.
OPT-1001 : End congestion update;  0.071258s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080403s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 873 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 913 instances, 864 slices, 31 macros(244 instances: 158 mslices 86 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Initial: Len = 67677.8, Over = 0
PHY-3001 : End spreading;  0.007120s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 67677.8, Over = 0
PHY-3001 : End incremental legalization;  0.044184s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (282.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.212256s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (220.8%)

OPT-1001 : Current memory(MB): used = 235, reserve = 199, peak = 235.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066098s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1970/2221.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 78536, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 78560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018096s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.3%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.94, top10 = 18.71, top15 = 14.94.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066800s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.209731s wall, 1.500000s user + 0.062500s system = 1.562500s CPU (129.2%)

RUN-1003 : finish command "place" in  8.841109s wall, 18.671875s user + 5.265625s system = 23.937500s CPU (270.8%)

RUN-1004 : used memory is 209 MB, reserved memory is 173 MB, peak memory is 235 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 915 instances
RUN-1001 : 432 mslices, 432 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2221 nets
RUN-1001 : 1592 nets have 2 pins
RUN-1001 : 504 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7317, tnet num: 2219, tinst num: 913, tnode num: 9777, tedge num: 13003.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 432 mslices, 432 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 74808, over cnt = 157(0%), over = 291, worst = 8
PHY-1002 : len = 76312, over cnt = 103(0%), over = 144, worst = 5
PHY-1002 : len = 78048, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 78176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.190416s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (139.5%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 23.93, top10 = 18.68, top15 = 14.89.
PHY-1001 : End global routing;  0.253645s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (123.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 256, reserve = 221, peak = 256.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 513, reserve = 481, peak = 513.
PHY-1001 : End build detailed router design. 3.793662s wall, 3.656250s user + 0.140625s system = 3.796875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29480, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.083932s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 514, peak = 545.
PHY-1001 : End phase 1; 1.091446s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 197304, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End initial routed; 1.472663s wall, 3.234375s user + 0.187500s system = 3.421875s CPU (232.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1969(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.582   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.202   |   3   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.464770s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 2; 1.937555s wall, 3.703125s user + 0.187500s system = 3.890625s CPU (200.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 197304, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017693s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 197240, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032645s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 197368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.029539s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1969(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.582   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.202   |   3   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.462983s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (97.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.220479s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 564, reserve = 533, peak = 564.
PHY-1001 : End phase 3; 0.941214s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 197368
PHY-1001 : Current memory(MB): used = 564, reserve = 533, peak = 564.
PHY-1001 : End export database. 0.011713s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (133.4%)

PHY-1001 : End detail routing;  8.007145s wall, 9.609375s user + 0.359375s system = 9.968750s CPU (124.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7317, tnet num: 2219, tinst num: 913, tnode num: 9777, tedge num: 13003.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7343, tnet num: 2232, tinst num: 926, tnode num: 9803, tedge num: 13029.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.834613s wall, 3.812500s user + 0.156250s system = 3.968750s CPU (103.5%)

RUN-1003 : finish command "route" in  12.538792s wall, 14.156250s user + 0.531250s system = 14.687500s CPU (117.1%)

RUN-1004 : used memory is 518 MB, reserved memory is 489 MB, peak memory is 564 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                     1010   out of  19600    5.15%
#reg                     1118   out of  19600    5.70%
#le                      1688
  #lut only               570   out of   1688   33.77%
  #reg only               678   out of   1688   40.17%
  #lut&reg                440   out of   1688   26.07%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         525
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         103
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    44
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1688   |766     |244     |1152    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1266   |456     |178     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |23      |4       |23      |0       |0       |
|    demodu                  |Demodulation                                     |458    |119     |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |36      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |8       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |13      |0       |13      |0       |0       |
|    integ                   |Integration                                      |144    |19      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |102    |41      |16      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |503    |230     |89      |327     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |24      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |179    |126     |7       |120     |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |21      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |20     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |129    |87      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |221    |173     |48      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1566  
    #2          2       328   
    #3          3       141   
    #4          4        35   
    #5        5-10       86   
    #6        11-50      31   
    #7       51-100      2    
    #8       101-500     1    
  Average     2.01            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7343, tnet num: 2232, tinst num: 926, tnode num: 9803, tedge num: 13029.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2232 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9549d9e8c17d9eab739910e390fc99924bce9ea777a46fd31ec4cbf1d9377ef0 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 926
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 2234, pip num: 16161
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 1382 valid insts, and 44127 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000101110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.606276s wall, 20.281250s user + 0.046875s system = 20.328125s CPU (780.0%)

RUN-1004 : used memory is 534 MB, reserved memory is 503 MB, peak memory is 700 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250818_162820.log"
RUN-1001 : Backing up run's log file succeed.

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 16 17:32:19 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1583 instances
RUN-0007 : 372 luts, 963 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2115 nets
RUN-1001 : 1537 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1581 instances, 372 luts, 963 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7547, tnet num: 2113, tinst num: 1581, tnode num: 10713, tedge num: 12792.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.252995s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (98.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 527466
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1581.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 460186, overlap = 18
PHY-3002 : Step(2): len = 442195, overlap = 11.25
PHY-3002 : Step(3): len = 429311, overlap = 20.25
PHY-3002 : Step(4): len = 417978, overlap = 13.5
PHY-3002 : Step(5): len = 403825, overlap = 20.25
PHY-3002 : Step(6): len = 390307, overlap = 20.25
PHY-3002 : Step(7): len = 379941, overlap = 13.5
PHY-3002 : Step(8): len = 371180, overlap = 13.5
PHY-3002 : Step(9): len = 357838, overlap = 13.5
PHY-3002 : Step(10): len = 349158, overlap = 11.25
PHY-3002 : Step(11): len = 342489, overlap = 11.25
PHY-3002 : Step(12): len = 328892, overlap = 11.25
PHY-3002 : Step(13): len = 320768, overlap = 11.25
PHY-3002 : Step(14): len = 315625, overlap = 13.5
PHY-3002 : Step(15): len = 304744, overlap = 13.5
PHY-3002 : Step(16): len = 296907, overlap = 13.5
PHY-3002 : Step(17): len = 292962, overlap = 13.5
PHY-3002 : Step(18): len = 285590, overlap = 13.5
PHY-3002 : Step(19): len = 276580, overlap = 13.5
PHY-3002 : Step(20): len = 271174, overlap = 13.5
PHY-3002 : Step(21): len = 266774, overlap = 13.5
PHY-3002 : Step(22): len = 257780, overlap = 11.25
PHY-3002 : Step(23): len = 252075, overlap = 11.25
PHY-3002 : Step(24): len = 249028, overlap = 11.25
PHY-3002 : Step(25): len = 236641, overlap = 13.5
PHY-3002 : Step(26): len = 228316, overlap = 13.5
PHY-3002 : Step(27): len = 225974, overlap = 15.75
PHY-3002 : Step(28): len = 217839, overlap = 18
PHY-3002 : Step(29): len = 202311, overlap = 20.25
PHY-3002 : Step(30): len = 197827, overlap = 20.25
PHY-3002 : Step(31): len = 195418, overlap = 20.25
PHY-3002 : Step(32): len = 149774, overlap = 18
PHY-3002 : Step(33): len = 141053, overlap = 20.25
PHY-3002 : Step(34): len = 140032, overlap = 20.25
PHY-3002 : Step(35): len = 136468, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106994
PHY-3002 : Step(36): len = 137188, overlap = 15.75
PHY-3002 : Step(37): len = 136576, overlap = 13.5
PHY-3002 : Step(38): len = 135918, overlap = 13.5
PHY-3002 : Step(39): len = 134831, overlap = 11.25
PHY-3002 : Step(40): len = 130191, overlap = 11.25
PHY-3002 : Step(41): len = 128822, overlap = 11.25
PHY-3002 : Step(42): len = 125722, overlap = 11.25
PHY-3002 : Step(43): len = 124208, overlap = 11.25
PHY-3002 : Step(44): len = 120789, overlap = 11.25
PHY-3002 : Step(45): len = 119614, overlap = 9
PHY-3002 : Step(46): len = 117480, overlap = 11.25
PHY-3002 : Step(47): len = 115906, overlap = 13.5
PHY-3002 : Step(48): len = 113376, overlap = 13.5
PHY-3002 : Step(49): len = 112767, overlap = 13.5
PHY-3002 : Step(50): len = 109872, overlap = 11.25
PHY-3002 : Step(51): len = 104754, overlap = 13.5
PHY-3002 : Step(52): len = 101799, overlap = 11.25
PHY-3002 : Step(53): len = 101634, overlap = 13.5
PHY-3002 : Step(54): len = 100576, overlap = 13.5
PHY-3002 : Step(55): len = 99259.7, overlap = 13.5
PHY-3002 : Step(56): len = 96629.2, overlap = 11.25
PHY-3002 : Step(57): len = 95267.2, overlap = 13.5
PHY-3002 : Step(58): len = 92697.6, overlap = 11.25
PHY-3002 : Step(59): len = 89795.7, overlap = 15.75
PHY-3002 : Step(60): len = 89097.1, overlap = 15.75
PHY-3002 : Step(61): len = 88751.5, overlap = 13.5
PHY-3002 : Step(62): len = 88296.5, overlap = 11.25
PHY-3002 : Step(63): len = 87318.2, overlap = 13.5
PHY-3002 : Step(64): len = 86293.7, overlap = 15.75
PHY-3002 : Step(65): len = 85253.1, overlap = 15.75
PHY-3002 : Step(66): len = 83504.6, overlap = 11.25
PHY-3002 : Step(67): len = 82489.6, overlap = 15.75
PHY-3002 : Step(68): len = 80878.1, overlap = 16.125
PHY-3002 : Step(69): len = 79615.4, overlap = 13.875
PHY-3002 : Step(70): len = 76938.1, overlap = 16.25
PHY-3002 : Step(71): len = 75911.4, overlap = 16.0625
PHY-3002 : Step(72): len = 74552.1, overlap = 13.8125
PHY-3002 : Step(73): len = 73646.7, overlap = 13.5625
PHY-3002 : Step(74): len = 71770.5, overlap = 15.75
PHY-3002 : Step(75): len = 70033.5, overlap = 11.25
PHY-3002 : Step(76): len = 68713.9, overlap = 11.25
PHY-3002 : Step(77): len = 68577.9, overlap = 9
PHY-3002 : Step(78): len = 68320.6, overlap = 11.25
PHY-3002 : Step(79): len = 67163.9, overlap = 9
PHY-3002 : Step(80): len = 66676.1, overlap = 6.75
PHY-3002 : Step(81): len = 66366.1, overlap = 6.75
PHY-3002 : Step(82): len = 66037.5, overlap = 9
PHY-3002 : Step(83): len = 65365.7, overlap = 6.75
PHY-3002 : Step(84): len = 64705.3, overlap = 6.75
PHY-3002 : Step(85): len = 64334.5, overlap = 4.5
PHY-3002 : Step(86): len = 63379.9, overlap = 6.75
PHY-3002 : Step(87): len = 62938.7, overlap = 6.75
PHY-3002 : Step(88): len = 62832.1, overlap = 4.5
PHY-3002 : Step(89): len = 61836.4, overlap = 6.75
PHY-3002 : Step(90): len = 61905.6, overlap = 4.5
PHY-3002 : Step(91): len = 61389.6, overlap = 4.5
PHY-3002 : Step(92): len = 60525.4, overlap = 4.5
PHY-3002 : Step(93): len = 59801.8, overlap = 9
PHY-3002 : Step(94): len = 58702.4, overlap = 6.75
PHY-3002 : Step(95): len = 58737.3, overlap = 4.5
PHY-3002 : Step(96): len = 57376.2, overlap = 6.75
PHY-3002 : Step(97): len = 56901.1, overlap = 6.75
PHY-3002 : Step(98): len = 55854.3, overlap = 6.75
PHY-3002 : Step(99): len = 55413.1, overlap = 4.5
PHY-3002 : Step(100): len = 55342, overlap = 6.75
PHY-3002 : Step(101): len = 54946.8, overlap = 6.75
PHY-3002 : Step(102): len = 54262.1, overlap = 4.5
PHY-3002 : Step(103): len = 53953, overlap = 4.5
PHY-3002 : Step(104): len = 53448.8, overlap = 6.75
PHY-3002 : Step(105): len = 53122.6, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000213988
PHY-3002 : Step(106): len = 53326.1, overlap = 6.75
PHY-3002 : Step(107): len = 53335.6, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000427976
PHY-3002 : Step(108): len = 53395, overlap = 6.75
PHY-3002 : Step(109): len = 53441.2, overlap = 6.75
PHY-3001 : Before Legalized: Len = 53441.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.021080s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (148.2%)

PHY-3001 : After Legalized: Len = 55800.7, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.125366s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (74.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000162606
PHY-3002 : Step(110): len = 55750, overlap = 12.9062
PHY-3002 : Step(111): len = 55638, overlap = 13.0938
PHY-3002 : Step(112): len = 54834.4, overlap = 13.75
PHY-3002 : Step(113): len = 54681.3, overlap = 14.125
PHY-3002 : Step(114): len = 53107.2, overlap = 16.25
PHY-3002 : Step(115): len = 52764.9, overlap = 16.8125
PHY-3002 : Step(116): len = 51572.1, overlap = 21.75
PHY-3002 : Step(117): len = 50198.7, overlap = 23.1875
PHY-3002 : Step(118): len = 48759.6, overlap = 22.9062
PHY-3002 : Step(119): len = 48246.1, overlap = 23.5
PHY-3002 : Step(120): len = 46740.9, overlap = 24.6562
PHY-3002 : Step(121): len = 45772.2, overlap = 26.3125
PHY-3002 : Step(122): len = 45249.1, overlap = 26.8438
PHY-3002 : Step(123): len = 45018.1, overlap = 27.1562
PHY-3002 : Step(124): len = 44509.1, overlap = 26.5625
PHY-3002 : Step(125): len = 44201.2, overlap = 26.1875
PHY-3002 : Step(126): len = 43880, overlap = 25.9375
PHY-3002 : Step(127): len = 43711.5, overlap = 26.1875
PHY-3002 : Step(128): len = 43651.1, overlap = 26.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000325212
PHY-3002 : Step(129): len = 43602.7, overlap = 25.9375
PHY-3002 : Step(130): len = 43717.6, overlap = 25.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000650423
PHY-3002 : Step(131): len = 43584.1, overlap = 25.6875
PHY-3002 : Step(132): len = 43578.2, overlap = 25.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061626s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.06081e-05
PHY-3002 : Step(133): len = 43646, overlap = 76.7188
PHY-3002 : Step(134): len = 45324.5, overlap = 64.2812
PHY-3002 : Step(135): len = 45865.3, overlap = 57.375
PHY-3002 : Step(136): len = 45460.3, overlap = 56.375
PHY-3002 : Step(137): len = 45494.9, overlap = 55.2812
PHY-3002 : Step(138): len = 45477.5, overlap = 52.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000121216
PHY-3002 : Step(139): len = 45498.8, overlap = 50.8438
PHY-3002 : Step(140): len = 45920.4, overlap = 48.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000242432
PHY-3002 : Step(141): len = 46611.2, overlap = 43.9688
PHY-3002 : Step(142): len = 47830.2, overlap = 41.7188
PHY-3002 : Step(143): len = 48849.1, overlap = 38.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000484865
PHY-3002 : Step(144): len = 48799.6, overlap = 37.4062
PHY-3002 : Step(145): len = 49091.2, overlap = 35.75
PHY-3002 : Step(146): len = 49833.2, overlap = 34.25
PHY-3002 : Step(147): len = 49816, overlap = 28.6875
PHY-3002 : Step(148): len = 49767.7, overlap = 26.6562
PHY-3002 : Step(149): len = 49808.2, overlap = 26.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7547, tnet num: 2113, tinst num: 1581, tnode num: 10713, tedge num: 12792.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.16 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52672, over cnt = 234(0%), over = 938, worst = 27
PHY-1001 : End global iterations;  0.070640s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.5%)

PHY-1001 : Congestion index: top1 = 41.66, top5 = 24.43, top10 = 15.65, top15 = 11.21.
PHY-1001 : End incremental global routing;  0.122313s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066878s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.220166s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.4%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1580/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52672, over cnt = 234(0%), over = 938, worst = 27
PHY-1002 : len = 57344, over cnt = 180(0%), over = 440, worst = 13
PHY-1002 : len = 62576, over cnt = 40(0%), over = 55, worst = 8
PHY-1002 : len = 62952, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 63672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097411s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (112.3%)

PHY-1001 : Congestion index: top1 = 36.19, top5 = 23.81, top10 = 17.11, top15 = 12.66.
OPT-1001 : End congestion update;  0.144263s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (108.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057115s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.204643s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (106.9%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.673595s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (102.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 372 LUT to BLE ...
SYN-4008 : Packed 372 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 93 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 682 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1054/1366 primitive instances ...
PHY-3001 : End packing;  0.046807s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 805 instances
RUN-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 803 instances, 754 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49871.8, Over = 52.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6345, tnet num: 1933, tinst num: 803, tnode num: 8649, tedge num: 11206.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283912s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.58413e-05
PHY-3002 : Step(150): len = 49313.4, overlap = 54.75
PHY-3002 : Step(151): len = 48938.1, overlap = 56.5
PHY-3002 : Step(152): len = 48620.8, overlap = 56.5
PHY-3002 : Step(153): len = 48675.1, overlap = 54.5
PHY-3002 : Step(154): len = 48534.8, overlap = 58.75
PHY-3002 : Step(155): len = 48040.7, overlap = 56.25
PHY-3002 : Step(156): len = 47432.6, overlap = 53.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.16825e-05
PHY-3002 : Step(157): len = 47904.6, overlap = 52
PHY-3002 : Step(158): len = 48297.8, overlap = 50
PHY-3002 : Step(159): len = 48843.4, overlap = 49.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000143365
PHY-3002 : Step(160): len = 49488.7, overlap = 48.5
PHY-3002 : Step(161): len = 49762.2, overlap = 47
PHY-3002 : Step(162): len = 50517.9, overlap = 44
PHY-3001 : Before Legalized: Len = 50517.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090355s wall, 0.093750s user + 0.093750s system = 0.187500s CPU (207.5%)

PHY-3001 : After Legalized: Len = 62894.2, Over = 0
PHY-3001 : Trial Legalized: Len = 62894.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055103s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000968206
PHY-3002 : Step(163): len = 59167.4, overlap = 10.5
PHY-3002 : Step(164): len = 58372.1, overlap = 10.75
PHY-3002 : Step(165): len = 56837.3, overlap = 14
PHY-3002 : Step(166): len = 55526.9, overlap = 17
PHY-3002 : Step(167): len = 55144.3, overlap = 18
PHY-3002 : Step(168): len = 54630.9, overlap = 18.25
PHY-3002 : Step(169): len = 54293, overlap = 19.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00193641
PHY-3002 : Step(170): len = 54627.5, overlap = 17.75
PHY-3002 : Step(171): len = 54682.5, overlap = 17.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00387282
PHY-3002 : Step(172): len = 54735.7, overlap = 17
PHY-3002 : Step(173): len = 54735.7, overlap = 17
PHY-3001 : Before Legalized: Len = 54735.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005251s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59557.7, Over = 0
PHY-3001 : Legalized: Len = 59557.7, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005498s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 6, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 59667.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6345, tnet num: 1933, tinst num: 803, tnode num: 8649, tedge num: 11206.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 94/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65992, over cnt = 150(0%), over = 233, worst = 5
PHY-1002 : len = 67176, over cnt = 81(0%), over = 95, worst = 3
PHY-1002 : len = 68080, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 68240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.102953s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (227.7%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 22.92, top10 = 17.49, top15 = 13.54.
PHY-1001 : End incremental global routing;  0.153637s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (183.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066960s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.249656s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (150.2%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005324s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 22.92, top10 = 17.49, top15 = 13.54.
OPT-1001 : End congestion update;  0.051822s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (120.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057469s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 763 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 803 instances, 754 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59711.8, Over = 0
PHY-3001 : End spreading;  0.004813s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (324.6%)

PHY-3001 : Final: Len = 59711.8, Over = 0
PHY-3001 : End incremental legalization;  0.035109s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.0%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.161145s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (106.7%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047195s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68248, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 68264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017304s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (90.3%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 22.84, top10 = 17.45, top15 = 13.52.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052579s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.844329s wall, 0.921875s user + 0.046875s system = 0.968750s CPU (114.7%)

RUN-1003 : finish command "place" in  5.695198s wall, 7.734375s user + 3.546875s system = 11.281250s CPU (198.1%)

RUN-1004 : used memory is 194 MB, reserved memory is 161 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 805 instances
RUN-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6345, tnet num: 1933, tinst num: 803, tnode num: 8649, tedge num: 11206.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64920, over cnt = 154(0%), over = 230, worst = 4
PHY-1002 : len = 66016, over cnt = 79(0%), over = 91, worst = 2
PHY-1002 : len = 67008, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 67040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132117s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.53, top10 = 17.20, top15 = 13.33.
PHY-1001 : End global routing;  0.182925s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 200, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.141751s wall, 3.078125s user + 0.062500s system = 3.140625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32696, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.032694s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (96.8%)

PHY-1001 : Current memory(MB): used = 526, reserve = 500, peak = 526.
PHY-1001 : End phase 1; 1.038991s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (96.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 44% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177296, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.250706s wall, 2.234375s user + 0.125000s system = 2.359375s CPU (188.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1719(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.549   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.773  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.336183s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.586988s wall, 2.578125s user + 0.125000s system = 2.703125s CPU (170.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177296, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014831s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176992, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.040332s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177048, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027002s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1719(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.549   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.761  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338621s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.159483s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 0.704659s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 177048
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.010879s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (143.6%)

PHY-1001 : End detail routing;  6.661518s wall, 7.546875s user + 0.203125s system = 7.750000s CPU (116.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6345, tnet num: 1933, tinst num: 803, tnode num: 8649, tedge num: 11206.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/crc_data_b1[6]_syn_8.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_65.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6461, tnet num: 1991, tinst num: 861, tnode num: 8765, tedge num: 11322.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -635ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -457ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -249ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -619ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -673ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -635ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -624ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -489ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -891ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -1001ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/crc_data_b1[6]_syn_8_mi[0] slack -388ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -397ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -306ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -429ps
RUN-1001 : End hold fix;  3.029189s wall, 3.125000s user + 0.390625s system = 3.515625s CPU (116.1%)

RUN-1003 : finish command "route" in  10.195195s wall, 11.187500s user + 0.609375s system = 11.796875s CPU (115.7%)

RUN-1004 : used memory is 522 MB, reserved memory is 493 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      889   out of  19600    4.54%
#reg                     1052   out of  19600    5.37%
#le                      1571
  #lut only               519   out of   1571   33.04%
  #reg only               682   out of   1571   43.41%
  #lut&reg                370   out of   1571   23.55%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         482
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1571   |692     |197     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1063   |281     |135     |860     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |452    |98      |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |141    |25      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |100    |52      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |320    |70      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |17      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |173    |131     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |110    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |315    |270     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1375  
    #2          2       339   
    #3          3       109   
    #4          4        23   
    #5        5-10       70   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6461, tnet num: 1991, tinst num: 861, tnode num: 8765, tedge num: 11322.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1991 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 861
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1993, pip num: 14442
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1269 valid insts, and 38780 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.959000s wall, 16.593750s user + 0.046875s system = 16.640625s CPU (562.4%)

RUN-1004 : used memory is 542 MB, reserved memory is 512 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_173219.log"

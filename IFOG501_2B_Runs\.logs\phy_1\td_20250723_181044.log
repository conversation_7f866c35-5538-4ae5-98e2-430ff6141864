============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 23 18:10:44 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1555 instances
RUN-0007 : 370 luts, 944 seqs, 120 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2074 nets
RUN-1001 : 1531 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1553 instances, 370 luts, 944 seqs, 190 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7395, tnet num: 2072, tinst num: 1553, tnode num: 10494, tedge num: 12494.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2072 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.242118s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 512787
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1553.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 447395, overlap = 18
PHY-3002 : Step(2): len = 429693, overlap = 11.25
PHY-3002 : Step(3): len = 418457, overlap = 20.25
PHY-3002 : Step(4): len = 408170, overlap = 15.75
PHY-3002 : Step(5): len = 390661, overlap = 18
PHY-3002 : Step(6): len = 378253, overlap = 13.5
PHY-3002 : Step(7): len = 370364, overlap = 13.5
PHY-3002 : Step(8): len = 362105, overlap = 13.5
PHY-3002 : Step(9): len = 350953, overlap = 13.5
PHY-3002 : Step(10): len = 342259, overlap = 13.5
PHY-3002 : Step(11): len = 336569, overlap = 13.5
PHY-3002 : Step(12): len = 325404, overlap = 11.25
PHY-3002 : Step(13): len = 317418, overlap = 11.25
PHY-3002 : Step(14): len = 312108, overlap = 11.25
PHY-3002 : Step(15): len = 301313, overlap = 11.25
PHY-3002 : Step(16): len = 292812, overlap = 11.25
PHY-3002 : Step(17): len = 288704, overlap = 11.25
PHY-3002 : Step(18): len = 278816, overlap = 13.5
PHY-3002 : Step(19): len = 266402, overlap = 13.5
PHY-3002 : Step(20): len = 262599, overlap = 13.5
PHY-3002 : Step(21): len = 257440, overlap = 13.5
PHY-3002 : Step(22): len = 229660, overlap = 18
PHY-3002 : Step(23): len = 222130, overlap = 20.25
PHY-3002 : Step(24): len = 220429, overlap = 20.25
PHY-3002 : Step(25): len = 211970, overlap = 20.25
PHY-3002 : Step(26): len = 195491, overlap = 20.25
PHY-3002 : Step(27): len = 191066, overlap = 20.25
PHY-3002 : Step(28): len = 188361, overlap = 20.25
PHY-3002 : Step(29): len = 171519, overlap = 20.25
PHY-3002 : Step(30): len = 169765, overlap = 20.25
PHY-3002 : Step(31): len = 160595, overlap = 18
PHY-3002 : Step(32): len = 154791, overlap = 20.25
PHY-3002 : Step(33): len = 152475, overlap = 20.25
PHY-3002 : Step(34): len = 149284, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108531
PHY-3002 : Step(35): len = 149930, overlap = 15.75
PHY-3002 : Step(36): len = 148381, overlap = 15.75
PHY-3002 : Step(37): len = 147917, overlap = 13.5
PHY-3002 : Step(38): len = 143415, overlap = 13.5
PHY-3002 : Step(39): len = 139757, overlap = 11.25
PHY-3002 : Step(40): len = 137505, overlap = 9
PHY-3002 : Step(41): len = 135066, overlap = 11.25
PHY-3002 : Step(42): len = 133249, overlap = 11.25
PHY-3002 : Step(43): len = 130985, overlap = 11.25
PHY-3002 : Step(44): len = 126190, overlap = 9
PHY-3002 : Step(45): len = 124165, overlap = 11.25
PHY-3002 : Step(46): len = 122595, overlap = 13.5
PHY-3002 : Step(47): len = 120290, overlap = 15.75
PHY-3002 : Step(48): len = 117776, overlap = 15.75
PHY-3002 : Step(49): len = 115290, overlap = 13.5
PHY-3002 : Step(50): len = 113880, overlap = 13.5
PHY-3002 : Step(51): len = 110317, overlap = 13.5
PHY-3002 : Step(52): len = 105606, overlap = 13.5
PHY-3002 : Step(53): len = 104250, overlap = 13.5
PHY-3002 : Step(54): len = 101723, overlap = 11.25
PHY-3002 : Step(55): len = 100563, overlap = 15.75
PHY-3002 : Step(56): len = 98791.8, overlap = 18
PHY-3002 : Step(57): len = 95348.1, overlap = 11.25
PHY-3002 : Step(58): len = 93608.9, overlap = 13.5
PHY-3002 : Step(59): len = 91911.6, overlap = 13.5
PHY-3002 : Step(60): len = 88772.3, overlap = 11.25
PHY-3002 : Step(61): len = 84767, overlap = 15.75
PHY-3002 : Step(62): len = 82564.8, overlap = 15.75
PHY-3002 : Step(63): len = 81589.8, overlap = 15.75
PHY-3002 : Step(64): len = 81260, overlap = 15.75
PHY-3002 : Step(65): len = 80382.8, overlap = 9
PHY-3002 : Step(66): len = 79172, overlap = 11.25
PHY-3002 : Step(67): len = 77044, overlap = 9
PHY-3002 : Step(68): len = 74433.4, overlap = 11.375
PHY-3002 : Step(69): len = 72759.2, overlap = 11.625
PHY-3002 : Step(70): len = 72382.4, overlap = 11.8125
PHY-3002 : Step(71): len = 70801.2, overlap = 10.125
PHY-3002 : Step(72): len = 69115.1, overlap = 10.8125
PHY-3002 : Step(73): len = 68567.1, overlap = 8.4375
PHY-3002 : Step(74): len = 67545.4, overlap = 10.5625
PHY-3002 : Step(75): len = 66439.1, overlap = 10.9375
PHY-3002 : Step(76): len = 66415.8, overlap = 10.9375
PHY-3002 : Step(77): len = 66128.7, overlap = 8.75
PHY-3002 : Step(78): len = 65698.4, overlap = 8.75
PHY-3002 : Step(79): len = 65557.1, overlap = 8.4375
PHY-3002 : Step(80): len = 65037.9, overlap = 10.25
PHY-3002 : Step(81): len = 64627.4, overlap = 12.375
PHY-3002 : Step(82): len = 64012.5, overlap = 9.75
PHY-3002 : Step(83): len = 62609.9, overlap = 9.5625
PHY-3002 : Step(84): len = 62387.4, overlap = 9.4375
PHY-3002 : Step(85): len = 61706.3, overlap = 7.0625
PHY-3002 : Step(86): len = 61449.1, overlap = 9.125
PHY-3002 : Step(87): len = 61358.7, overlap = 6.875
PHY-3002 : Step(88): len = 61011.6, overlap = 6.8125
PHY-3002 : Step(89): len = 60468.2, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217063
PHY-3002 : Step(90): len = 60951.9, overlap = 9
PHY-3002 : Step(91): len = 61039.5, overlap = 9
PHY-3002 : Step(92): len = 61063.8, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000434125
PHY-3002 : Step(93): len = 61184.8, overlap = 9
PHY-3002 : Step(94): len = 61358.3, overlap = 9
PHY-3001 : Before Legalized: Len = 61358.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009284s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63504.5, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2072 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062606s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(95): len = 63723.6, overlap = 7.96875
PHY-3002 : Step(96): len = 62532.4, overlap = 6.8125
PHY-3002 : Step(97): len = 62108.5, overlap = 5.4375
PHY-3002 : Step(98): len = 60755.9, overlap = 4.5625
PHY-3002 : Step(99): len = 59544.6, overlap = 4.46875
PHY-3002 : Step(100): len = 59017.1, overlap = 5.46875
PHY-3002 : Step(101): len = 57670.4, overlap = 6.8125
PHY-3002 : Step(102): len = 55798.5, overlap = 10.6875
PHY-3002 : Step(103): len = 54400.9, overlap = 11.25
PHY-3002 : Step(104): len = 53450, overlap = 12.0625
PHY-3002 : Step(105): len = 52450.9, overlap = 12.75
PHY-3002 : Step(106): len = 50795.8, overlap = 15.125
PHY-3002 : Step(107): len = 49394.9, overlap = 14.5
PHY-3002 : Step(108): len = 48739.1, overlap = 15
PHY-3002 : Step(109): len = 47976.6, overlap = 15.4688
PHY-3002 : Step(110): len = 46461.6, overlap = 17.7188
PHY-3002 : Step(111): len = 45361.4, overlap = 18.875
PHY-3002 : Step(112): len = 44811.8, overlap = 18.6562
PHY-3002 : Step(113): len = 44291.9, overlap = 19.5312
PHY-3002 : Step(114): len = 43411.2, overlap = 19.1875
PHY-3002 : Step(115): len = 43324.6, overlap = 20.7812
PHY-3002 : Step(116): len = 42881.9, overlap = 21.375
PHY-3002 : Step(117): len = 42416.2, overlap = 21.9062
PHY-3002 : Step(118): len = 42019.4, overlap = 23.0625
PHY-3002 : Step(119): len = 41958.9, overlap = 23.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000128481
PHY-3002 : Step(120): len = 41848.7, overlap = 23.4375
PHY-3002 : Step(121): len = 41775.2, overlap = 24.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000256962
PHY-3002 : Step(122): len = 41778.8, overlap = 23.7188
PHY-3002 : Step(123): len = 41811.3, overlap = 23.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2072 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056846s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.27191e-05
PHY-3002 : Step(124): len = 42468.1, overlap = 68.4688
PHY-3002 : Step(125): len = 43189.6, overlap = 68.8438
PHY-3002 : Step(126): len = 44497.3, overlap = 55.4375
PHY-3002 : Step(127): len = 44545.1, overlap = 54.9062
PHY-3002 : Step(128): len = 44444.5, overlap = 48
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000125438
PHY-3002 : Step(129): len = 44558.6, overlap = 47.3438
PHY-3002 : Step(130): len = 45504.8, overlap = 41.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000250877
PHY-3002 : Step(131): len = 45635.7, overlap = 42.5312
PHY-3002 : Step(132): len = 46949.7, overlap = 41.1875
PHY-3002 : Step(133): len = 47095.3, overlap = 43.7812
PHY-3002 : Step(134): len = 48746.6, overlap = 34.6562
PHY-3002 : Step(135): len = 49211.9, overlap = 30.0625
PHY-3002 : Step(136): len = 49116.7, overlap = 33.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000501753
PHY-3002 : Step(137): len = 49347.4, overlap = 32.7188
PHY-3002 : Step(138): len = 49938.7, overlap = 26.6875
PHY-3002 : Step(139): len = 50437.5, overlap = 25.625
PHY-3002 : Step(140): len = 50176.4, overlap = 25.2188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00100351
PHY-3002 : Step(141): len = 50166.6, overlap = 25.1875
PHY-3002 : Step(142): len = 49993.3, overlap = 25.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7395, tnet num: 2072, tinst num: 1553, tnode num: 10494, tedge num: 12494.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.59 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2074.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52336, over cnt = 259(0%), over = 1037, worst = 17
PHY-1001 : End global iterations;  0.071414s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (109.4%)

PHY-1001 : Congestion index: top1 = 40.86, top5 = 24.61, top10 = 15.91, top15 = 11.37.
PHY-1001 : End incremental global routing;  0.123101s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (114.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2072 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065998s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217146s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (107.9%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1621/2074.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52336, over cnt = 259(0%), over = 1037, worst = 17
PHY-1002 : len = 59688, over cnt = 141(0%), over = 389, worst = 17
PHY-1002 : len = 61432, over cnt = 69(0%), over = 150, worst = 17
PHY-1002 : len = 63816, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 63912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094066s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (116.3%)

PHY-1001 : Congestion index: top1 = 35.95, top5 = 24.42, top10 = 17.69, top15 = 13.05.
OPT-1001 : End congestion update;  0.137261s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (113.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2072 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054612s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.195311s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.0%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : End physical optimization;  0.646161s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (106.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 98 single LUT's are left
SYN-4006 : 657 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1027/1332 primitive instances ...
PHY-3001 : End packing;  0.047387s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 783 instances
RUN-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1893 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 781 instances, 732 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 49729.8, Over = 52.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6171, tnet num: 1891, tinst num: 781, tnode num: 8400, tedge num: 10861.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.275946s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.7584e-05
PHY-3002 : Step(143): len = 49133.7, overlap = 55
PHY-3002 : Step(144): len = 48444.2, overlap = 56.75
PHY-3002 : Step(145): len = 47878.8, overlap = 60
PHY-3002 : Step(146): len = 47875.2, overlap = 63.5
PHY-3002 : Step(147): len = 47676.9, overlap = 64
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.5168e-05
PHY-3002 : Step(148): len = 48039.8, overlap = 58.75
PHY-3002 : Step(149): len = 48194.3, overlap = 57.75
PHY-3002 : Step(150): len = 48920.4, overlap = 54.25
PHY-3002 : Step(151): len = 49262.3, overlap = 52.25
PHY-3002 : Step(152): len = 49122.4, overlap = 51.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000150336
PHY-3002 : Step(153): len = 49259.7, overlap = 52
PHY-3002 : Step(154): len = 49605.4, overlap = 52
PHY-3001 : Before Legalized: Len = 49605.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.084465s wall, 0.109375s user + 0.093750s system = 0.203125s CPU (240.5%)

PHY-3001 : After Legalized: Len = 64043.7, Over = 0
PHY-3001 : Trial Legalized: Len = 64043.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049303s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00120221
PHY-3002 : Step(155): len = 61090.4, overlap = 5.75
PHY-3002 : Step(156): len = 59196.3, overlap = 11.75
PHY-3002 : Step(157): len = 56597.3, overlap = 12.75
PHY-3002 : Step(158): len = 55475.6, overlap = 18.5
PHY-3002 : Step(159): len = 55017.3, overlap = 20.75
PHY-3002 : Step(160): len = 54594.3, overlap = 23
PHY-3002 : Step(161): len = 54193, overlap = 23.5
PHY-3002 : Step(162): len = 53946.4, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00240441
PHY-3002 : Step(163): len = 54157.4, overlap = 22
PHY-3002 : Step(164): len = 54158, overlap = 21.5
PHY-3002 : Step(165): len = 54158, overlap = 21.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00480882
PHY-3002 : Step(166): len = 54322, overlap = 21.25
PHY-3002 : Step(167): len = 54382.4, overlap = 21
PHY-3001 : Before Legalized: Len = 54382.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006055s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 58753, Over = 0
PHY-3001 : Legalized: Len = 58753, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006166s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.4%)

PHY-3001 : 14 instances has been re-located, deltaX = 2, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 58937, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6171, tnet num: 1891, tinst num: 781, tnode num: 8400, tedge num: 10861.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 37/1893.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64104, over cnt = 165(0%), over = 253, worst = 7
PHY-1002 : len = 65272, over cnt = 104(0%), over = 122, worst = 4
PHY-1002 : len = 66376, over cnt = 33(0%), over = 40, worst = 4
PHY-1002 : len = 67008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117535s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (119.6%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.10, top10 = 17.70, top15 = 13.50.
PHY-1001 : End incremental global routing;  0.171124s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (118.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063078s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.264938s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (112.1%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1672/1893.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007262s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.10, top10 = 17.70, top15 = 13.50.
OPT-1001 : End congestion update;  0.054345s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047355s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 741 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 781 instances, 732 slices, 20 macros(190 instances: 120 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 58934.8, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007615s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (205.2%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 58944.8, Over = 0
PHY-3001 : End incremental legalization;  0.038610s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (202.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.154326s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (121.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047747s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1668/1893.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006687s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (233.7%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.12, top10 = 17.70, top15 = 13.50.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049106s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.832640s wall, 0.859375s user + 0.109375s system = 0.968750s CPU (116.3%)

RUN-1003 : finish command "place" in  5.304521s wall, 7.265625s user + 3.656250s system = 10.921875s CPU (205.9%)

RUN-1004 : used memory is 193 MB, reserved memory is 160 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 783 instances
RUN-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1893 nets
RUN-1001 : 1356 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6171, tnet num: 1891, tinst num: 781, tnode num: 8400, tedge num: 10861.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 366 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1891 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63720, over cnt = 170(0%), over = 260, worst = 7
PHY-1002 : len = 65032, over cnt = 102(0%), over = 119, worst = 3
PHY-1002 : len = 66416, over cnt = 16(0%), over = 21, worst = 3
PHY-1002 : len = 66776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118900s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (157.7%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.00, top10 = 17.65, top15 = 13.40.
PHY-1001 : End global routing;  0.167826s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (139.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.141408s wall, 3.046875s user + 0.093750s system = 3.140625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31784, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.036976s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (97.9%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.043442s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (97.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174528, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.227908s wall, 2.187500s user + 0.109375s system = 2.296875s CPU (187.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.368  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.331634s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.559677s wall, 2.515625s user + 0.109375s system = 2.625000s CPU (168.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174528, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013586s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (115.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 174536, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025927s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (120.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 174576, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019233s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.368  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.336604s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.164700s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.689072s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 174576
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.011083s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.0%)

PHY-1001 : End detail routing;  6.618641s wall, 7.453125s user + 0.218750s system = 7.671875s CPU (115.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6171, tnet num: 1891, tinst num: 781, tnode num: 8400, tedge num: 10861.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[28] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg1_syn_235.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_76.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_76.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_81.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_84.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_84.mi[1] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_87.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_90.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_90.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_93.mi[0] slack -2909ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_96.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_96.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin wendu/cur_state[0]_syn_3747.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1952, tinst num: 842, tnode num: 8522, tedge num: 10983.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -807ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[0] slack -71ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg1_syn_235_mi[0] slack -305ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -637ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[1] slack -289ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[0] slack -229ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -511ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -348ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -442ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -586ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_96_mi[1] slack -461ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -578ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -491ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -925ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_96_mi[0] slack -352ps
RUN-1001 : End hold fix;  3.001906s wall, 3.031250s user + 0.359375s system = 3.390625s CPU (112.9%)

RUN-1003 : finish command "route" in  10.113107s wall, 11.000000s user + 0.609375s system = 11.609375s CPU (114.8%)

RUN-1004 : used memory is 523 MB, reserved memory is 495 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      876   out of  19600    4.47%
#reg                     1019   out of  19600    5.20%
#le                      1533
  #lut only               514   out of   1533   33.53%
  #reg only               657   out of   1533   42.86%
  #lut&reg                362   out of   1533   23.61%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         463
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    37
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1533   |686     |190     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1043   |285     |128     |829     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |458    |104     |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |8       |0       |13      |0       |0       |
|    integ                   |Integration                                      |142    |29      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |67     |39      |7       |65      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |76      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |163    |129     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |26     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |103    |82      |0       |83      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1378  
    #2          2       298   
    #3          3       109   
    #4          4        23   
    #5        5-10       71   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1952, tinst num: 842, tnode num: 8522, tedge num: 10983.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1954, pip num: 14386
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1268 valid insts, and 38521 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.001641s wall, 16.828125s user + 0.078125s system = 16.906250s CPU (563.2%)

RUN-1004 : used memory is 544 MB, reserved memory is 514 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250723_181044.log"

============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul 16 17:38:28 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1585 instances
RUN-0007 : 374 luts, 963 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2117 nets
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1583 instances, 374 luts, 963 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7553, tnet num: 2115, tinst num: 1583, tnode num: 10719, tedge num: 12800.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.255070s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (104.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 530298
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1583.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453680, overlap = 20.25
PHY-3002 : Step(2): len = 435516, overlap = 18
PHY-3002 : Step(3): len = 420210, overlap = 18
PHY-3002 : Step(4): len = 409443, overlap = 15.75
PHY-3002 : Step(5): len = 395164, overlap = 13.5
PHY-3002 : Step(6): len = 385864, overlap = 13.5
PHY-3002 : Step(7): len = 376367, overlap = 15.75
PHY-3002 : Step(8): len = 366651, overlap = 15.75
PHY-3002 : Step(9): len = 359845, overlap = 15.75
PHY-3002 : Step(10): len = 350858, overlap = 15.75
PHY-3002 : Step(11): len = 343097, overlap = 13.5
PHY-3002 : Step(12): len = 336410, overlap = 13.5
PHY-3002 : Step(13): len = 328682, overlap = 13.5
PHY-3002 : Step(14): len = 319815, overlap = 13.5
PHY-3002 : Step(15): len = 313798, overlap = 13.5
PHY-3002 : Step(16): len = 307916, overlap = 13.5
PHY-3002 : Step(17): len = 298152, overlap = 13.5
PHY-3002 : Step(18): len = 290536, overlap = 13.5
PHY-3002 : Step(19): len = 286887, overlap = 13.5
PHY-3002 : Step(20): len = 279019, overlap = 13.5
PHY-3002 : Step(21): len = 270678, overlap = 13.5
PHY-3002 : Step(22): len = 266908, overlap = 13.5
PHY-3002 : Step(23): len = 261552, overlap = 13.5
PHY-3002 : Step(24): len = 252387, overlap = 13.5
PHY-3002 : Step(25): len = 248229, overlap = 13.5
PHY-3002 : Step(26): len = 244025, overlap = 18
PHY-3002 : Step(27): len = 233656, overlap = 20.25
PHY-3002 : Step(28): len = 228043, overlap = 20.25
PHY-3002 : Step(29): len = 225469, overlap = 20.25
PHY-3002 : Step(30): len = 209298, overlap = 20.25
PHY-3002 : Step(31): len = 197578, overlap = 20.25
PHY-3002 : Step(32): len = 195616, overlap = 20.25
PHY-3002 : Step(33): len = 188130, overlap = 20.25
PHY-3002 : Step(34): len = 131719, overlap = 15.75
PHY-3002 : Step(35): len = 130141, overlap = 18
PHY-3002 : Step(36): len = 127692, overlap = 20.25
PHY-3002 : Step(37): len = 124723, overlap = 20.25
PHY-3002 : Step(38): len = 118676, overlap = 18
PHY-3002 : Step(39): len = 117027, overlap = 20.25
PHY-3002 : Step(40): len = 113779, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.80623e-05
PHY-3002 : Step(41): len = 114758, overlap = 13.5
PHY-3002 : Step(42): len = 113967, overlap = 13.5
PHY-3002 : Step(43): len = 112667, overlap = 15.75
PHY-3002 : Step(44): len = 111569, overlap = 11.25
PHY-3002 : Step(45): len = 107199, overlap = 15.75
PHY-3002 : Step(46): len = 104796, overlap = 15.75
PHY-3002 : Step(47): len = 102885, overlap = 13.5
PHY-3002 : Step(48): len = 101810, overlap = 15.75
PHY-3002 : Step(49): len = 98265.1, overlap = 15.75
PHY-3002 : Step(50): len = 96442.9, overlap = 13.5
PHY-3002 : Step(51): len = 94046.2, overlap = 15.75
PHY-3002 : Step(52): len = 93998.7, overlap = 13.5
PHY-3002 : Step(53): len = 92167.1, overlap = 13.5
PHY-3002 : Step(54): len = 90612, overlap = 15.75
PHY-3002 : Step(55): len = 90011.3, overlap = 15.75
PHY-3002 : Step(56): len = 87971.5, overlap = 18
PHY-3002 : Step(57): len = 85791.4, overlap = 18
PHY-3002 : Step(58): len = 85800.5, overlap = 18
PHY-3002 : Step(59): len = 84468.5, overlap = 13.5
PHY-3002 : Step(60): len = 83158.9, overlap = 11.25
PHY-3002 : Step(61): len = 81667.1, overlap = 15.75
PHY-3002 : Step(62): len = 80788.3, overlap = 15.8125
PHY-3002 : Step(63): len = 78867.7, overlap = 18.125
PHY-3002 : Step(64): len = 77842.1, overlap = 18.125
PHY-3002 : Step(65): len = 77664.5, overlap = 16.125
PHY-3002 : Step(66): len = 76533.7, overlap = 18.625
PHY-3002 : Step(67): len = 74128.2, overlap = 14.5
PHY-3002 : Step(68): len = 73715.2, overlap = 14.5
PHY-3002 : Step(69): len = 72036, overlap = 14.375
PHY-3002 : Step(70): len = 71124.3, overlap = 14.3125
PHY-3002 : Step(71): len = 71203.3, overlap = 14.3125
PHY-3002 : Step(72): len = 70942.5, overlap = 16.5625
PHY-3002 : Step(73): len = 70082.3, overlap = 18.75
PHY-3002 : Step(74): len = 69718.3, overlap = 16.4375
PHY-3002 : Step(75): len = 69149.8, overlap = 14.0625
PHY-3002 : Step(76): len = 68693.1, overlap = 13.875
PHY-3002 : Step(77): len = 67902.8, overlap = 13.8125
PHY-3002 : Step(78): len = 67692, overlap = 13.8125
PHY-3002 : Step(79): len = 67546.8, overlap = 13.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000176125
PHY-3002 : Step(80): len = 67572.9, overlap = 13.75
PHY-3002 : Step(81): len = 67574.5, overlap = 13.75
PHY-3002 : Step(82): len = 67503.4, overlap = 13.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000352249
PHY-3002 : Step(83): len = 67752.3, overlap = 13.6875
PHY-3002 : Step(84): len = 67837.7, overlap = 13.5
PHY-3001 : Before Legalized: Len = 67837.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007465s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 71757, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063694s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 71906.7, overlap = 5.625
PHY-3002 : Step(86): len = 71189.6, overlap = 3
PHY-3002 : Step(87): len = 69760.4, overlap = 3.1875
PHY-3002 : Step(88): len = 68644.5, overlap = 3.4375
PHY-3002 : Step(89): len = 67575.9, overlap = 3.25
PHY-3002 : Step(90): len = 65047.6, overlap = 4
PHY-3002 : Step(91): len = 63704.2, overlap = 4.5
PHY-3002 : Step(92): len = 62826.6, overlap = 1.5625
PHY-3002 : Step(93): len = 62067.6, overlap = 4
PHY-3002 : Step(94): len = 61421.9, overlap = 1.4375
PHY-3002 : Step(95): len = 60283.7, overlap = 4.75
PHY-3002 : Step(96): len = 59131.4, overlap = 6.46875
PHY-3002 : Step(97): len = 58189, overlap = 6.9375
PHY-3002 : Step(98): len = 57441.5, overlap = 7.28125
PHY-3002 : Step(99): len = 56637.4, overlap = 7.34375
PHY-3002 : Step(100): len = 55984.2, overlap = 8.28125
PHY-3002 : Step(101): len = 54931, overlap = 10
PHY-3002 : Step(102): len = 54217.9, overlap = 10.9375
PHY-3002 : Step(103): len = 54039.6, overlap = 11.6875
PHY-3002 : Step(104): len = 53343.4, overlap = 12.0938
PHY-3002 : Step(105): len = 52735.3, overlap = 12.3125
PHY-3002 : Step(106): len = 52021.4, overlap = 13.5625
PHY-3002 : Step(107): len = 51667.3, overlap = 13.75
PHY-3002 : Step(108): len = 51177.9, overlap = 14.6875
PHY-3002 : Step(109): len = 50673, overlap = 15.3438
PHY-3002 : Step(110): len = 49930.1, overlap = 16.4062
PHY-3002 : Step(111): len = 49432.5, overlap = 20.8438
PHY-3002 : Step(112): len = 49186, overlap = 21.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000171414
PHY-3002 : Step(113): len = 49010.2, overlap = 18.6562
PHY-3002 : Step(114): len = 49054.1, overlap = 18.5
PHY-3002 : Step(115): len = 49135.8, overlap = 18.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000342829
PHY-3002 : Step(116): len = 48987.3, overlap = 18.75
PHY-3002 : Step(117): len = 49274.3, overlap = 17.8438
PHY-3002 : Step(118): len = 49283.3, overlap = 15.8438
PHY-3002 : Step(119): len = 49195.1, overlap = 15.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066550s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.85976e-05
PHY-3002 : Step(120): len = 50070.4, overlap = 57.75
PHY-3002 : Step(121): len = 50616.3, overlap = 56.0312
PHY-3002 : Step(122): len = 51038.7, overlap = 55.3438
PHY-3002 : Step(123): len = 50616.9, overlap = 55.25
PHY-3002 : Step(124): len = 50514.4, overlap = 45.8438
PHY-3002 : Step(125): len = 50554.3, overlap = 44.5625
PHY-3002 : Step(126): len = 50799.4, overlap = 43.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000177195
PHY-3002 : Step(127): len = 51416.1, overlap = 42.8438
PHY-3002 : Step(128): len = 51327.2, overlap = 42.6562
PHY-3002 : Step(129): len = 52092.5, overlap = 40.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00035439
PHY-3002 : Step(130): len = 52518.2, overlap = 38.9375
PHY-3002 : Step(131): len = 53014, overlap = 38.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000665844
PHY-3002 : Step(132): len = 53374.9, overlap = 36.875
PHY-3002 : Step(133): len = 53692.9, overlap = 35.8438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7553, tnet num: 2115, tinst num: 1583, tnode num: 10719, tedge num: 12800.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.84 peak overflow 2.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56216, over cnt = 232(0%), over = 1042, worst = 30
PHY-1001 : End global iterations;  0.075827s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.0%)

PHY-1001 : Congestion index: top1 = 45.50, top5 = 26.29, top10 = 16.58, top15 = 11.70.
PHY-1001 : End incremental global routing;  0.129053s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (72.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071613s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (87.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.231634s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (80.9%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1666/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56216, over cnt = 232(0%), over = 1042, worst = 30
PHY-1002 : len = 63296, over cnt = 172(0%), over = 443, worst = 13
PHY-1002 : len = 68800, over cnt = 25(0%), over = 28, worst = 3
PHY-1002 : len = 69256, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113150s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (124.3%)

PHY-1001 : Congestion index: top1 = 39.03, top5 = 26.05, top10 = 18.73, top15 = 13.69.
OPT-1001 : End congestion update;  0.159867s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (127.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071940s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.235540s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (112.8%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.726197s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (101.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 672 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1046/1358 primitive instances ...
PHY-3001 : End packing;  0.063279s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1937 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 795 instances, 746 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53789.2, Over = 64.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1935, tinst num: 795, tnode num: 8623, tedge num: 11199.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.291431s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.2542e-05
PHY-3002 : Step(134): len = 52913.5, overlap = 62.75
PHY-3002 : Step(135): len = 52336.2, overlap = 63
PHY-3002 : Step(136): len = 52006.3, overlap = 64.25
PHY-3002 : Step(137): len = 52150.4, overlap = 64
PHY-3002 : Step(138): len = 51865.3, overlap = 63.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.50839e-05
PHY-3002 : Step(139): len = 52549.8, overlap = 64
PHY-3002 : Step(140): len = 52793, overlap = 61.75
PHY-3002 : Step(141): len = 53713.8, overlap = 60.75
PHY-3002 : Step(142): len = 54013.2, overlap = 59.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000130168
PHY-3002 : Step(143): len = 54665.5, overlap = 59.5
PHY-3002 : Step(144): len = 55344.7, overlap = 58.25
PHY-3001 : Before Legalized: Len = 55344.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071062s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (219.9%)

PHY-3001 : After Legalized: Len = 70031.5, Over = 0
PHY-3001 : Trial Legalized: Len = 70031.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049482s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00104181
PHY-3002 : Step(145): len = 66351.9, overlap = 7.25
PHY-3002 : Step(146): len = 64476.5, overlap = 13.5
PHY-3002 : Step(147): len = 62428.3, overlap = 17.25
PHY-3002 : Step(148): len = 61191.9, overlap = 19.75
PHY-3002 : Step(149): len = 60422.2, overlap = 23.75
PHY-3002 : Step(150): len = 60084.6, overlap = 24.5
PHY-3002 : Step(151): len = 59628, overlap = 26
PHY-3002 : Step(152): len = 59463.3, overlap = 26.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00207019
PHY-3002 : Step(153): len = 59758.1, overlap = 25.75
PHY-3002 : Step(154): len = 59803.1, overlap = 25.5
PHY-3002 : Step(155): len = 59757.6, overlap = 25.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00414037
PHY-3002 : Step(156): len = 59885.2, overlap = 25
PHY-3002 : Step(157): len = 59928.1, overlap = 24.5
PHY-3001 : Before Legalized: Len = 59928.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005485s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64173.4, Over = 0
PHY-3001 : Legalized: Len = 64173.4, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004991s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64361.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1935, tinst num: 795, tnode num: 8623, tedge num: 11199.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 54/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71456, over cnt = 127(0%), over = 183, worst = 7
PHY-1002 : len = 72304, over cnt = 65(0%), over = 75, worst = 3
PHY-1002 : len = 73080, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 73232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115883s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (121.4%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.47, top10 = 18.30, top15 = 14.19.
PHY-1001 : End incremental global routing;  0.167571s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (111.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060915s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.258691s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1715/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008622s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (181.2%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.47, top10 = 18.30, top15 = 14.19.
OPT-1001 : End congestion update;  0.057330s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052690s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.111965s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.7%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 215.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050097s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1715/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006954s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.47, top10 = 18.30, top15 = 14.19.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049219s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.812905s wall, 0.796875s user + 0.046875s system = 0.843750s CPU (103.8%)

RUN-1003 : finish command "place" in  5.225387s wall, 7.781250s user + 3.156250s system = 10.937500s CPU (209.3%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1937 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1935, tinst num: 795, tnode num: 8623, tedge num: 11199.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70608, over cnt = 134(0%), over = 195, worst = 6
PHY-1002 : len = 71544, over cnt = 69(0%), over = 82, worst = 2
PHY-1002 : len = 72216, over cnt = 30(0%), over = 36, worst = 2
PHY-1002 : len = 72672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132384s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (129.8%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 23.29, top10 = 18.20, top15 = 14.14.
PHY-1001 : End global routing;  0.182891s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (119.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 467, peak = 495.
PHY-1001 : End build detailed router design. 3.215038s wall, 3.078125s user + 0.125000s system = 3.203125s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.124025s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 527, reserve = 500, peak = 527.
PHY-1001 : End phase 1; 1.131107s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180800, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End initial routed; 1.506133s wall, 2.562500s user + 0.140625s system = 2.703125s CPU (179.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.961   |  -45.706  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350876s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 2; 1.857103s wall, 2.906250s user + 0.140625s system = 3.046875s CPU (164.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180800, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015319s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180624, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030146s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022952s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (204.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.961   |  -45.706  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.356254s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.187489s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 547, reserve = 518, peak = 547.
PHY-1001 : End phase 3; 0.724731s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (103.5%)

PHY-1003 : Routed, final wirelength = 180720
PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End export database. 0.011245s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.117779s wall, 8.031250s user + 0.281250s system = 8.312500s CPU (116.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1935, tinst num: 795, tnode num: 8623, tedge num: 11199.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_17.sr slack -46ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_22.sr slack -46ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_15.sr slack -46ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_60.mi[0] slack -2590ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_60.mi[1] slack -2961ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_63.mi[0] slack -2721ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2721ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2949ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2961ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2961ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2803ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2949ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2803ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2949ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2721ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2817ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2817ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6461, tnet num: 1996, tinst num: 856, tnode num: 8745, tedge num: 11321.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -362ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -520ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -171ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -91ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -468ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -403ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -312ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -881ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -664ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -517ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -543ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -605ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -163ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -136ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[1] slack -186ps
RUN-1001 : End hold fix;  3.208817s wall, 3.281250s user + 0.250000s system = 3.531250s CPU (110.0%)

RUN-1003 : finish command "route" in  10.849430s wall, 11.890625s user + 0.531250s system = 12.421875s CPU (114.5%)

RUN-1004 : used memory is 524 MB, reserved memory is 495 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      899   out of  19600    4.59%
#reg                     1052   out of  19600    5.37%
#le                      1571
  #lut only               519   out of   1571   33.04%
  #reg only               672   out of   1571   42.78%
  #lut&reg                380   out of   1571   24.19%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1571   |702     |197     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1069   |293     |135     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |457    |102     |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |61     |33      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |5       |0       |13      |0       |0       |
|    integ                   |Integration                                      |139    |28      |15      |110     |0       |0       |
|    modu                    |Modulation                                       |101    |52      |14      |99      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |67      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |176    |138     |7       |113     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |22      |0       |11      |0       |0       |
|    U2                      |Ctrl_Data                                        |116    |88      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1383  
    #2          2       329   
    #3          3       115   
    #4          4        24   
    #5        5-10       70   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6461, tnet num: 1996, tinst num: 856, tnode num: 8745, tedge num: 11321.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1996 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 856
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1998, pip num: 14775
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1319 valid insts, and 39603 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.162267s wall, 18.093750s user + 0.093750s system = 18.187500s CPU (575.1%)

RUN-1004 : used memory is 521 MB, reserved memory is 495 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_173828.log"

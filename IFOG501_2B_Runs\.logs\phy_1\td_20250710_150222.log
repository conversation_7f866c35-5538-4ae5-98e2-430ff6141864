============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 10 15:02:22 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1526 instances
RUN-0007 : 381 luts, 898 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2059 nets
RUN-1001 : 1512 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1524 instances, 381 luts, 898 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7328, tnet num: 2057, tinst num: 1524, tnode num: 10309, tedge num: 12469.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.258945s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (102.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 523493
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1524.
PHY-3001 : End clustering;  0.000045s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 455087, overlap = 18
PHY-3002 : Step(2): len = 427246, overlap = 15.75
PHY-3002 : Step(3): len = 412731, overlap = 20.25
PHY-3002 : Step(4): len = 399039, overlap = 11.25
PHY-3002 : Step(5): len = 388636, overlap = 13.5
PHY-3002 : Step(6): len = 364040, overlap = 11.25
PHY-3002 : Step(7): len = 354435, overlap = 15.75
PHY-3002 : Step(8): len = 341096, overlap = 13.5
PHY-3002 : Step(9): len = 331227, overlap = 15.75
PHY-3002 : Step(10): len = 317905, overlap = 11.25
PHY-3002 : Step(11): len = 311154, overlap = 13.5
PHY-3002 : Step(12): len = 302629, overlap = 11.25
PHY-3002 : Step(13): len = 296521, overlap = 13.5
PHY-3002 : Step(14): len = 288793, overlap = 13.5
PHY-3002 : Step(15): len = 283587, overlap = 13.5
PHY-3002 : Step(16): len = 276325, overlap = 13.5
PHY-3002 : Step(17): len = 271034, overlap = 13.5
PHY-3002 : Step(18): len = 263803, overlap = 13.5
PHY-3002 : Step(19): len = 259450, overlap = 13.5
PHY-3002 : Step(20): len = 252856, overlap = 13.5
PHY-3002 : Step(21): len = 247488, overlap = 13.5
PHY-3002 : Step(22): len = 242762, overlap = 15.75
PHY-3002 : Step(23): len = 238031, overlap = 15.75
PHY-3002 : Step(24): len = 228518, overlap = 20.25
PHY-3002 : Step(25): len = 224698, overlap = 20.25
PHY-3002 : Step(26): len = 220668, overlap = 20.25
PHY-3002 : Step(27): len = 208008, overlap = 20.25
PHY-3002 : Step(28): len = 198177, overlap = 20.25
PHY-3002 : Step(29): len = 196389, overlap = 20.25
PHY-3002 : Step(30): len = 184113, overlap = 20.25
PHY-3002 : Step(31): len = 165748, overlap = 20.25
PHY-3002 : Step(32): len = 161910, overlap = 20.25
PHY-3002 : Step(33): len = 157615, overlap = 20.25
PHY-3002 : Step(34): len = 127810, overlap = 18
PHY-3002 : Step(35): len = 123949, overlap = 20.25
PHY-3002 : Step(36): len = 122370, overlap = 20.25
PHY-3002 : Step(37): len = 117980, overlap = 20.25
PHY-3002 : Step(38): len = 115271, overlap = 20.25
PHY-3002 : Step(39): len = 112887, overlap = 20.25
PHY-3002 : Step(40): len = 111011, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.89368e-05
PHY-3002 : Step(41): len = 112484, overlap = 20.25
PHY-3002 : Step(42): len = 111920, overlap = 18
PHY-3002 : Step(43): len = 110632, overlap = 20.25
PHY-3002 : Step(44): len = 110033, overlap = 15.75
PHY-3002 : Step(45): len = 106662, overlap = 13.5
PHY-3002 : Step(46): len = 104850, overlap = 11.25
PHY-3002 : Step(47): len = 102183, overlap = 13.5
PHY-3002 : Step(48): len = 98538.2, overlap = 11.25
PHY-3002 : Step(49): len = 95552.9, overlap = 13.5
PHY-3002 : Step(50): len = 94625, overlap = 11.25
PHY-3002 : Step(51): len = 92476.7, overlap = 15.75
PHY-3002 : Step(52): len = 91860, overlap = 15.75
PHY-3002 : Step(53): len = 89419.4, overlap = 15.75
PHY-3002 : Step(54): len = 88893.5, overlap = 15.75
PHY-3002 : Step(55): len = 86400.3, overlap = 13.5
PHY-3002 : Step(56): len = 85004.9, overlap = 13.5
PHY-3002 : Step(57): len = 83657.5, overlap = 13.5
PHY-3002 : Step(58): len = 82903.7, overlap = 13.5
PHY-3002 : Step(59): len = 78973.4, overlap = 13.5
PHY-3002 : Step(60): len = 76251, overlap = 15.75
PHY-3002 : Step(61): len = 75231.2, overlap = 15.75
PHY-3002 : Step(62): len = 74820.1, overlap = 15.75
PHY-3002 : Step(63): len = 74309.2, overlap = 13.5
PHY-3002 : Step(64): len = 73744.7, overlap = 13.5
PHY-3002 : Step(65): len = 73347, overlap = 13.5
PHY-3002 : Step(66): len = 72159.5, overlap = 15.75
PHY-3002 : Step(67): len = 70701.6, overlap = 16.9375
PHY-3002 : Step(68): len = 69941.3, overlap = 15.0625
PHY-3002 : Step(69): len = 69031.6, overlap = 15.0625
PHY-3002 : Step(70): len = 68532.3, overlap = 15.0625
PHY-3002 : Step(71): len = 67015.6, overlap = 15.3125
PHY-3002 : Step(72): len = 65646.6, overlap = 15.125
PHY-3002 : Step(73): len = 64410.9, overlap = 15.3125
PHY-3002 : Step(74): len = 64039.8, overlap = 15.4375
PHY-3002 : Step(75): len = 63402, overlap = 15.8438
PHY-3002 : Step(76): len = 63244.5, overlap = 16.2188
PHY-3002 : Step(77): len = 62529.2, overlap = 14.4688
PHY-3002 : Step(78): len = 61932, overlap = 19.3125
PHY-3002 : Step(79): len = 61370.3, overlap = 19.3125
PHY-3002 : Step(80): len = 60204.6, overlap = 19.0625
PHY-3002 : Step(81): len = 59692.9, overlap = 19.0625
PHY-3002 : Step(82): len = 59466.4, overlap = 19.0625
PHY-3002 : Step(83): len = 58872.8, overlap = 19.0625
PHY-3002 : Step(84): len = 58799.2, overlap = 19.25
PHY-3002 : Step(85): len = 58289.7, overlap = 17.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000197874
PHY-3002 : Step(86): len = 58715.1, overlap = 17.4688
PHY-3002 : Step(87): len = 58793.8, overlap = 19.9375
PHY-3002 : Step(88): len = 58890.7, overlap = 20.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000395747
PHY-3002 : Step(89): len = 59104.8, overlap = 19.9375
PHY-3002 : Step(90): len = 59188.5, overlap = 19.9375
PHY-3001 : Before Legalized: Len = 59188.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008537s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (183.0%)

PHY-3001 : After Legalized: Len = 63211.2, Over = 4.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062637s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 63076.6, overlap = 15.3125
PHY-3002 : Step(92): len = 63085.5, overlap = 15.1875
PHY-3002 : Step(93): len = 62501.5, overlap = 14.8125
PHY-3002 : Step(94): len = 62422.3, overlap = 14.4375
PHY-3002 : Step(95): len = 62398.7, overlap = 14.6875
PHY-3002 : Step(96): len = 60872.1, overlap = 15
PHY-3002 : Step(97): len = 60192.8, overlap = 15.625
PHY-3002 : Step(98): len = 58697.6, overlap = 15.2188
PHY-3002 : Step(99): len = 57683.1, overlap = 14.8125
PHY-3002 : Step(100): len = 56945, overlap = 14.25
PHY-3002 : Step(101): len = 56110.4, overlap = 13.6875
PHY-3002 : Step(102): len = 55447.6, overlap = 12.9062
PHY-3002 : Step(103): len = 54207.5, overlap = 13.8125
PHY-3002 : Step(104): len = 53322.4, overlap = 13.3438
PHY-3002 : Step(105): len = 53097.9, overlap = 13.3438
PHY-3002 : Step(106): len = 52107, overlap = 15.7188
PHY-3002 : Step(107): len = 51172.4, overlap = 16.7188
PHY-3002 : Step(108): len = 51059.5, overlap = 16.8438
PHY-3002 : Step(109): len = 50439.6, overlap = 17.375
PHY-3002 : Step(110): len = 49949.9, overlap = 17.625
PHY-3002 : Step(111): len = 49766.4, overlap = 18.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0564294
PHY-3002 : Step(112): len = 49805.1, overlap = 17.3438
PHY-3002 : Step(113): len = 49558.4, overlap = 17.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.101069s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (108.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.06202e-05
PHY-3002 : Step(114): len = 50293.5, overlap = 41.5
PHY-3002 : Step(115): len = 51016.3, overlap = 42.375
PHY-3002 : Step(116): len = 51389.9, overlap = 40.8438
PHY-3002 : Step(117): len = 51354.9, overlap = 39.5625
PHY-3002 : Step(118): len = 51174.2, overlap = 37.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00012124
PHY-3002 : Step(119): len = 51350.5, overlap = 36.6562
PHY-3002 : Step(120): len = 52231.3, overlap = 33.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000242481
PHY-3002 : Step(121): len = 52068.1, overlap = 35.0312
PHY-3002 : Step(122): len = 53284.7, overlap = 33.0312
PHY-3002 : Step(123): len = 53997, overlap = 31.4375
PHY-3002 : Step(124): len = 53912.9, overlap = 31.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000484962
PHY-3002 : Step(125): len = 54227.7, overlap = 31.0312
PHY-3002 : Step(126): len = 54755.5, overlap = 30.25
PHY-3002 : Step(127): len = 55291.9, overlap = 28.4688
PHY-3002 : Step(128): len = 55389.6, overlap = 27.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7328, tnet num: 2057, tinst num: 1524, tnode num: 10309, tedge num: 12469.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 74.66 peak overflow 1.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59000, over cnt = 267(0%), over = 994, worst = 17
PHY-1001 : End global iterations;  0.086125s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (145.1%)

PHY-1001 : Congestion index: top1 = 43.49, top5 = 26.31, top10 = 17.18, top15 = 12.21.
PHY-1001 : End incremental global routing;  0.139091s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (134.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077944s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.252463s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (117.6%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1596/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59000, over cnt = 267(0%), over = 994, worst = 17
PHY-1002 : len = 65592, over cnt = 184(0%), over = 378, worst = 15
PHY-1002 : len = 68864, over cnt = 39(0%), over = 96, worst = 9
PHY-1002 : len = 69632, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107497s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 36.66, top5 = 25.53, top10 = 18.59, top15 = 13.71.
OPT-1001 : End congestion update;  0.158276s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (138.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066707s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.228659s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (129.8%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.749649s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (114.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 381 LUT to BLE ...
SYN-4008 : Packed 381 LUT and 180 SEQ to BLE.
SYN-4003 : Packing 718 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 618 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 999/1310 primitive instances ...
PHY-3001 : End packing;  0.057518s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 765 instances
RUN-1001 : 357 mslices, 357 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-1001 : 1343 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 763 instances, 714 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55544.4, Over = 52.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6177, tnet num: 1885, tinst num: 763, tnode num: 8343, tedge num: 10945.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299195s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.37162e-05
PHY-3002 : Step(129): len = 54454.4, overlap = 52.25
PHY-3002 : Step(130): len = 53804.4, overlap = 52
PHY-3002 : Step(131): len = 53208.9, overlap = 54
PHY-3002 : Step(132): len = 53011.9, overlap = 47.75
PHY-3002 : Step(133): len = 53149.2, overlap = 47.75
PHY-3002 : Step(134): len = 53013.8, overlap = 50.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.74324e-05
PHY-3002 : Step(135): len = 53351.8, overlap = 51
PHY-3002 : Step(136): len = 53772.5, overlap = 49.5
PHY-3002 : Step(137): len = 54148.2, overlap = 48.5
PHY-3002 : Step(138): len = 54340.6, overlap = 48
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000174865
PHY-3002 : Step(139): len = 55139.4, overlap = 46.25
PHY-3002 : Step(140): len = 55527.2, overlap = 45
PHY-3001 : Before Legalized: Len = 55527.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083195s wall, 0.031250s user + 0.093750s system = 0.125000s CPU (150.2%)

PHY-3001 : After Legalized: Len = 69588.4, Over = 0
PHY-3001 : Trial Legalized: Len = 69588.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058780s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00133742
PHY-3002 : Step(141): len = 66298.8, overlap = 5.75
PHY-3002 : Step(142): len = 64891.9, overlap = 8.5
PHY-3002 : Step(143): len = 62769.4, overlap = 10
PHY-3002 : Step(144): len = 61817.7, overlap = 11.25
PHY-3002 : Step(145): len = 61226, overlap = 14
PHY-3002 : Step(146): len = 60611.8, overlap = 15.25
PHY-3002 : Step(147): len = 60168.9, overlap = 15.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00267485
PHY-3002 : Step(148): len = 60434.8, overlap = 16.25
PHY-3002 : Step(149): len = 60442.8, overlap = 17.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00534969
PHY-3002 : Step(150): len = 60426.6, overlap = 17.75
PHY-3002 : Step(151): len = 60386.2, overlap = 17.25
PHY-3001 : Before Legalized: Len = 60386.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006789s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.1%)

PHY-3001 : After Legalized: Len = 64216.5, Over = 0
PHY-3001 : Legalized: Len = 64216.5, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006161s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.6%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 64314.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6177, tnet num: 1885, tinst num: 763, tnode num: 8343, tedge num: 10945.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 44/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70352, over cnt = 154(0%), over = 217, worst = 5
PHY-1002 : len = 71168, over cnt = 75(0%), over = 94, worst = 3
PHY-1002 : len = 71808, over cnt = 35(0%), over = 44, worst = 2
PHY-1002 : len = 72400, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.150672s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (124.4%)

PHY-1001 : Congestion index: top1 = 32.78, top5 = 23.43, top10 = 17.98, top15 = 13.86.
PHY-1001 : End incremental global routing;  0.206734s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (128.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071499s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.308043s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (116.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1651/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006067s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.78, top5 = 23.43, top10 = 17.98, top15 = 13.86.
OPT-1001 : End congestion update;  0.057930s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055044s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 723 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 763 instances, 714 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64319.4, Over = 0
PHY-3001 : End spreading;  0.004804s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64319.4, Over = 0
PHY-3001 : End incremental legalization;  0.041579s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (263.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.169051s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (138.6%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052626s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1647/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008476s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.4%)

PHY-1001 : Congestion index: top1 = 32.78, top5 = 23.43, top10 = 17.98, top15 = 13.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068672s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.009038s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (109.9%)

RUN-1003 : finish command "place" in  5.539509s wall, 6.906250s user + 3.015625s system = 9.921875s CPU (179.1%)

RUN-1004 : used memory is 202 MB, reserved memory is 169 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 765 instances
RUN-1001 : 357 mslices, 357 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-1001 : 1343 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6177, tnet num: 1885, tinst num: 763, tnode num: 8343, tedge num: 10945.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 357 mslices, 357 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69912, over cnt = 161(0%), over = 220, worst = 5
PHY-1002 : len = 70760, over cnt = 81(0%), over = 97, worst = 3
PHY-1002 : len = 71568, over cnt = 29(0%), over = 32, worst = 2
PHY-1002 : len = 72088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151381s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (123.9%)

PHY-1001 : Congestion index: top1 = 32.61, top5 = 23.25, top10 = 17.90, top15 = 13.78.
PHY-1001 : End global routing;  0.206006s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (113.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.908084s wall, 3.781250s user + 0.093750s system = 3.875000s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.300856s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 525, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.308681s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178392, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.991862s wall, 3.015625s user + 0.281250s system = 3.296875s CPU (165.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.288   |  -14.536  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366256s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.1%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End phase 2; 2.358212s wall, 3.375000s user + 0.281250s system = 3.656250s CPU (155.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178392, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.024716s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (126.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178328, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033022s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (47.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019887s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.288   |  -14.536  |  12   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.367707s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.241508s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.0%)

PHY-1001 : Current memory(MB): used = 541, reserve = 513, peak = 541.
PHY-1001 : End phase 3; 0.842382s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (96.5%)

PHY-1003 : Routed, final wirelength = 178360
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.010891s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (143.5%)

PHY-1001 : End detail routing;  8.666844s wall, 9.500000s user + 0.406250s system = 9.906250s CPU (114.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6177, tnet num: 1885, tinst num: 763, tnode num: 8343, tedge num: 10945.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  9.749793s wall, 10.578125s user + 0.437500s system = 11.015625s CPU (113.0%)

RUN-1004 : used memory is 518 MB, reserved memory is 492 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      777   out of  19600    3.96%
#reg                      988   out of  19600    5.04%
#le                      1395
  #lut only               407   out of   1395   29.18%
  #reg only               618   out of   1395   44.30%
  #lut&reg                370   out of   1395   26.52%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         440
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1395   |581     |196     |1021    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1040   |288     |135     |858     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |21      |7       |23      |0       |0       |
|    demodu                  |Demodulation                                     |429    |76      |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |140    |34      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |100    |39      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |310    |93      |46      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |33     |25      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |127    |119     |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |21      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |21      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |77     |77      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1304  
    #2          2       298   
    #3          3       121   
    #4          4        18   
    #5        5-10       71   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.99            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6177, tnet num: 1885, tinst num: 763, tnode num: 8343, tedge num: 10945.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 763
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1887, pip num: 14155
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1233 valid insts, and 37236 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.158768s wall, 17.062500s user + 0.046875s system = 17.109375s CPU (541.6%)

RUN-1004 : used memory is 539 MB, reserved memory is 511 MB, peak memory is 661 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_150222.log"

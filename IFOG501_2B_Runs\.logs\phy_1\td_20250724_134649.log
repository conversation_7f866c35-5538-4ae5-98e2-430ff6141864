============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 24 13:46:49 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1606 instances
RUN-0007 : 384 luts, 974 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2139 nets
RUN-1001 : 1561 nets have 2 pins
RUN-1001 : 473 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     271     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1604 instances, 384 luts, 974 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7599, tnet num: 2137, tinst num: 1604, tnode num: 10766, tedge num: 12847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2137 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278563s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 581703
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1604.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 513083, overlap = 13.5
PHY-3002 : Step(2): len = 467445, overlap = 15.75
PHY-3002 : Step(3): len = 447385, overlap = 15.75
PHY-3002 : Step(4): len = 428905, overlap = 9
PHY-3002 : Step(5): len = 419949, overlap = 6.75
PHY-3002 : Step(6): len = 395158, overlap = 15.75
PHY-3002 : Step(7): len = 381958, overlap = 18
PHY-3002 : Step(8): len = 375471, overlap = 15.75
PHY-3002 : Step(9): len = 358097, overlap = 13.5
PHY-3002 : Step(10): len = 349373, overlap = 11.25
PHY-3002 : Step(11): len = 342685, overlap = 11.25
PHY-3002 : Step(12): len = 331058, overlap = 13.5
PHY-3002 : Step(13): len = 321387, overlap = 13.5
PHY-3002 : Step(14): len = 316495, overlap = 13.5
PHY-3002 : Step(15): len = 302850, overlap = 18
PHY-3002 : Step(16): len = 294146, overlap = 18
PHY-3002 : Step(17): len = 289479, overlap = 20.25
PHY-3002 : Step(18): len = 279437, overlap = 20.25
PHY-3002 : Step(19): len = 265743, overlap = 20.25
PHY-3002 : Step(20): len = 262116, overlap = 20.25
PHY-3002 : Step(21): len = 254448, overlap = 20.25
PHY-3002 : Step(22): len = 225505, overlap = 20.25
PHY-3002 : Step(23): len = 217668, overlap = 20.25
PHY-3002 : Step(24): len = 216038, overlap = 20.25
PHY-3002 : Step(25): len = 182070, overlap = 13.5
PHY-3002 : Step(26): len = 169127, overlap = 18
PHY-3002 : Step(27): len = 166645, overlap = 18
PHY-3002 : Step(28): len = 162937, overlap = 18
PHY-3002 : Step(29): len = 157848, overlap = 18
PHY-3002 : Step(30): len = 155236, overlap = 20.25
PHY-3002 : Step(31): len = 150585, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000131655
PHY-3002 : Step(32): len = 151260, overlap = 13.5
PHY-3002 : Step(33): len = 150286, overlap = 15.75
PHY-3002 : Step(34): len = 149031, overlap = 13.5
PHY-3002 : Step(35): len = 147047, overlap = 13.5
PHY-3002 : Step(36): len = 140845, overlap = 9
PHY-3002 : Step(37): len = 137702, overlap = 9
PHY-3002 : Step(38): len = 136431, overlap = 9
PHY-3002 : Step(39): len = 134524, overlap = 9
PHY-3002 : Step(40): len = 130189, overlap = 13.5
PHY-3002 : Step(41): len = 128160, overlap = 11.25
PHY-3002 : Step(42): len = 126559, overlap = 11.25
PHY-3002 : Step(43): len = 122570, overlap = 13.5
PHY-3002 : Step(44): len = 120251, overlap = 13.5
PHY-3002 : Step(45): len = 119272, overlap = 13.5
PHY-3002 : Step(46): len = 116758, overlap = 13.5
PHY-3002 : Step(47): len = 114260, overlap = 13.5
PHY-3002 : Step(48): len = 113383, overlap = 13.5
PHY-3002 : Step(49): len = 108758, overlap = 13.5
PHY-3002 : Step(50): len = 106163, overlap = 13.5
PHY-3002 : Step(51): len = 104532, overlap = 13.5
PHY-3002 : Step(52): len = 103038, overlap = 15.75
PHY-3002 : Step(53): len = 102115, overlap = 13.5
PHY-3002 : Step(54): len = 100347, overlap = 15.75
PHY-3002 : Step(55): len = 93838.7, overlap = 15.75
PHY-3002 : Step(56): len = 91754.7, overlap = 13.5
PHY-3002 : Step(57): len = 90985.8, overlap = 13.5
PHY-3002 : Step(58): len = 89771, overlap = 15.75
PHY-3002 : Step(59): len = 88149, overlap = 13.5
PHY-3002 : Step(60): len = 87070.1, overlap = 15.75
PHY-3002 : Step(61): len = 85547.7, overlap = 15.75
PHY-3002 : Step(62): len = 84075.2, overlap = 13.5
PHY-3002 : Step(63): len = 82270.5, overlap = 15.75
PHY-3002 : Step(64): len = 81326.2, overlap = 15.75
PHY-3002 : Step(65): len = 79880.6, overlap = 13.5
PHY-3002 : Step(66): len = 78019.3, overlap = 15.75
PHY-3002 : Step(67): len = 77012.1, overlap = 15.75
PHY-3002 : Step(68): len = 76300.4, overlap = 13.5
PHY-3002 : Step(69): len = 75802.7, overlap = 13.5
PHY-3002 : Step(70): len = 75257.6, overlap = 13.5
PHY-3002 : Step(71): len = 74205.3, overlap = 13.5
PHY-3002 : Step(72): len = 73908, overlap = 13.5
PHY-3002 : Step(73): len = 73187.2, overlap = 13.5
PHY-3002 : Step(74): len = 71563.6, overlap = 15.75
PHY-3002 : Step(75): len = 70451.8, overlap = 13.5
PHY-3002 : Step(76): len = 69627.4, overlap = 13.5
PHY-3002 : Step(77): len = 69235.7, overlap = 13.5
PHY-3002 : Step(78): len = 69008, overlap = 13.5
PHY-3002 : Step(79): len = 68340.5, overlap = 13.5
PHY-3002 : Step(80): len = 67861.8, overlap = 13.5
PHY-3002 : Step(81): len = 67407.8, overlap = 13.5
PHY-3002 : Step(82): len = 66487.1, overlap = 13.5
PHY-3002 : Step(83): len = 66204.3, overlap = 15.75
PHY-3002 : Step(84): len = 65459.5, overlap = 13.5
PHY-3002 : Step(85): len = 64790.8, overlap = 13.5
PHY-3002 : Step(86): len = 64414.3, overlap = 13.5
PHY-3002 : Step(87): len = 64230.9, overlap = 13.5
PHY-3002 : Step(88): len = 64134.3, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00026331
PHY-3002 : Step(89): len = 64424.8, overlap = 13.5
PHY-3002 : Step(90): len = 64416.3, overlap = 13.5
PHY-3002 : Step(91): len = 64326.8, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00052662
PHY-3002 : Step(92): len = 64536.2, overlap = 13.5
PHY-3002 : Step(93): len = 64567.3, overlap = 13.5
PHY-3001 : Before Legalized: Len = 64567.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009755s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.2%)

PHY-3001 : After Legalized: Len = 69836, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2137 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063641s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(94): len = 69751.6, overlap = 5.5625
PHY-3002 : Step(95): len = 69761.5, overlap = 4.6875
PHY-3002 : Step(96): len = 68604.1, overlap = 4.3125
PHY-3002 : Step(97): len = 68504.9, overlap = 3.6875
PHY-3002 : Step(98): len = 66560.4, overlap = 3.9375
PHY-3002 : Step(99): len = 66026, overlap = 4
PHY-3002 : Step(100): len = 64814.9, overlap = 5.125
PHY-3002 : Step(101): len = 63770.5, overlap = 6.0625
PHY-3002 : Step(102): len = 62393.6, overlap = 6.4375
PHY-3002 : Step(103): len = 61802.6, overlap = 5.78125
PHY-3002 : Step(104): len = 60836.1, overlap = 5.28125
PHY-3002 : Step(105): len = 60336.5, overlap = 4.78125
PHY-3002 : Step(106): len = 59739.2, overlap = 5.5625
PHY-3002 : Step(107): len = 59545, overlap = 7.40625
PHY-3002 : Step(108): len = 58722.8, overlap = 9.3125
PHY-3002 : Step(109): len = 58513.7, overlap = 9.375
PHY-3002 : Step(110): len = 58194.4, overlap = 9.75
PHY-3002 : Step(111): len = 58095.6, overlap = 10.7188
PHY-3002 : Step(112): len = 57598.1, overlap = 10.625
PHY-3002 : Step(113): len = 57466.2, overlap = 10.4375
PHY-3002 : Step(114): len = 56782.5, overlap = 12.375
PHY-3002 : Step(115): len = 56282.2, overlap = 12.3125
PHY-3002 : Step(116): len = 55677.2, overlap = 12.2812
PHY-3002 : Step(117): len = 55622.5, overlap = 12.3438
PHY-3002 : Step(118): len = 55180.7, overlap = 12.25
PHY-3002 : Step(119): len = 55013.9, overlap = 13.25
PHY-3002 : Step(120): len = 54397.7, overlap = 14.5
PHY-3002 : Step(121): len = 53956.7, overlap = 15.25
PHY-3002 : Step(122): len = 53617.5, overlap = 15.9375
PHY-3002 : Step(123): len = 53205, overlap = 16
PHY-3002 : Step(124): len = 52927.4, overlap = 16.0625
PHY-3002 : Step(125): len = 52761.1, overlap = 15.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000916817
PHY-3002 : Step(126): len = 52653, overlap = 15.7812
PHY-3002 : Step(127): len = 52651.5, overlap = 16.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00183363
PHY-3002 : Step(128): len = 52547.7, overlap = 16.2812
PHY-3002 : Step(129): len = 52549.2, overlap = 16.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2137 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058418s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.52423e-05
PHY-3002 : Step(130): len = 52738.1, overlap = 50.9062
PHY-3002 : Step(131): len = 53639, overlap = 48.1562
PHY-3002 : Step(132): len = 53952.8, overlap = 49.5938
PHY-3002 : Step(133): len = 53650.1, overlap = 49.0625
PHY-3002 : Step(134): len = 53501.9, overlap = 48.6875
PHY-3002 : Step(135): len = 53488.2, overlap = 48.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000150485
PHY-3002 : Step(136): len = 53474.7, overlap = 48.2812
PHY-3002 : Step(137): len = 54407.2, overlap = 45.9375
PHY-3002 : Step(138): len = 55060.7, overlap = 43.1875
PHY-3002 : Step(139): len = 55046.4, overlap = 38.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000300969
PHY-3002 : Step(140): len = 55098.5, overlap = 37.125
PHY-3002 : Step(141): len = 55447, overlap = 34.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7599, tnet num: 2137, tinst num: 1604, tnode num: 10766, tedge num: 12847.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.47 peak overflow 2.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2139.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58632, over cnt = 229(0%), over = 1098, worst = 28
PHY-1001 : End global iterations;  0.076774s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (162.8%)

PHY-1001 : Congestion index: top1 = 44.78, top5 = 26.35, top10 = 16.41, top15 = 11.63.
PHY-1001 : End incremental global routing;  0.129422s wall, 0.109375s user + 0.078125s system = 0.187500s CPU (144.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2137 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.082867s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (94.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.241387s wall, 0.203125s user + 0.093750s system = 0.296875s CPU (123.0%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1620/2139.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58632, over cnt = 229(0%), over = 1098, worst = 28
PHY-1002 : len = 65216, over cnt = 182(0%), over = 503, worst = 17
PHY-1002 : len = 70912, over cnt = 68(0%), over = 109, worst = 13
PHY-1002 : len = 72448, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 72880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.093948s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (116.4%)

PHY-1001 : Congestion index: top1 = 38.19, top5 = 26.06, top10 = 18.67, top15 = 13.83.
OPT-1001 : End congestion update;  0.139188s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2137 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060284s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203064s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (107.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.709170s wall, 0.687500s user + 0.109375s system = 0.796875s CPU (112.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 199 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 85 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 690 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1074/1386 primitive instances ...
PHY-3001 : End packing;  0.052661s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 815 instances
RUN-1001 : 382 mslices, 382 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1948 nets
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 813 instances, 764 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55118.4, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1946, tinst num: 813, tnode num: 8656, tedge num: 11208.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294611s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.9218e-05
PHY-3002 : Step(142): len = 54382.3, overlap = 62.25
PHY-3002 : Step(143): len = 54307.4, overlap = 62.25
PHY-3002 : Step(144): len = 53892.7, overlap = 61
PHY-3002 : Step(145): len = 53986.7, overlap = 62
PHY-3002 : Step(146): len = 53726.4, overlap = 63.25
PHY-3002 : Step(147): len = 53784.9, overlap = 64
PHY-3002 : Step(148): len = 53708.2, overlap = 68.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.8436e-05
PHY-3002 : Step(149): len = 54018.5, overlap = 61.25
PHY-3002 : Step(150): len = 54684.9, overlap = 60
PHY-3002 : Step(151): len = 55306.5, overlap = 57.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116872
PHY-3002 : Step(152): len = 55526.5, overlap = 54.5
PHY-3002 : Step(153): len = 55943.4, overlap = 51.5
PHY-3002 : Step(154): len = 56619.7, overlap = 49.5
PHY-3001 : Before Legalized: Len = 56619.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088373s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (176.8%)

PHY-3001 : After Legalized: Len = 71500.8, Over = 0
PHY-3001 : Trial Legalized: Len = 71500.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050191s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (93.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00126421
PHY-3002 : Step(155): len = 67135.7, overlap = 4.75
PHY-3002 : Step(156): len = 65433.8, overlap = 10.25
PHY-3002 : Step(157): len = 63133.2, overlap = 13.25
PHY-3002 : Step(158): len = 61802.5, overlap = 18
PHY-3002 : Step(159): len = 60963.4, overlap = 25
PHY-3002 : Step(160): len = 60264.2, overlap = 25.25
PHY-3002 : Step(161): len = 59891.5, overlap = 25.75
PHY-3002 : Step(162): len = 59611.5, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00252842
PHY-3002 : Step(163): len = 59822.9, overlap = 25.5
PHY-3002 : Step(164): len = 59808.1, overlap = 24.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00505684
PHY-3002 : Step(165): len = 59792.1, overlap = 24.75
PHY-3002 : Step(166): len = 59766.7, overlap = 24.25
PHY-3001 : Before Legalized: Len = 59766.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005277s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64646.8, Over = 0
PHY-3001 : Legalized: Len = 64646.8, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005178s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (301.8%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 10, maxDist = 2.
PHY-3001 : Final: Len = 64728.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1946, tinst num: 813, tnode num: 8656, tedge num: 11208.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 37/1948.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70736, over cnt = 158(0%), over = 242, worst = 5
PHY-1002 : len = 71840, over cnt = 98(0%), over = 125, worst = 4
PHY-1002 : len = 73064, over cnt = 20(0%), over = 25, worst = 4
PHY-1002 : len = 73360, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 73472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134715s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (127.6%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 22.61, top10 = 17.62, top15 = 13.73.
PHY-1001 : End incremental global routing;  0.188326s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (124.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059657s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.281600s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (116.5%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1718/1948.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006306s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 22.61, top10 = 17.62, top15 = 13.73.
OPT-1001 : End congestion update;  0.056466s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052099s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 773 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 813 instances, 764 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64740.4, Over = 0
PHY-3001 : End spreading;  0.004904s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64740.4, Over = 0
PHY-3001 : End incremental legalization;  0.036631s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159203s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.1%)

OPT-1001 : Current memory(MB): used = 219, reserve = 187, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057203s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1714/1948.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73472, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 73488, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025895s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (120.7%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 22.61, top10 = 17.61, top15 = 13.73.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050734s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.902483s wall, 0.984375s user + 0.062500s system = 1.046875s CPU (116.0%)

RUN-1003 : finish command "place" in  5.525155s wall, 8.015625s user + 3.296875s system = 11.312500s CPU (204.7%)

RUN-1004 : used memory is 199 MB, reserved memory is 166 MB, peak memory is 219 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 815 instances
RUN-1001 : 382 mslices, 382 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1948 nets
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1946, tinst num: 813, tnode num: 8656, tedge num: 11208.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 382 mslices, 382 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1946 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70112, over cnt = 154(0%), over = 240, worst = 6
PHY-1002 : len = 71288, over cnt = 97(0%), over = 126, worst = 4
PHY-1002 : len = 72888, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118402s wall, 0.156250s user + 0.062500s system = 0.218750s CPU (184.8%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.45, top10 = 17.51, top15 = 13.62.
PHY-1001 : End global routing;  0.171282s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (155.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 466, peak = 494.
PHY-1001 : End build detailed router design. 3.290299s wall, 3.187500s user + 0.109375s system = 3.296875s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.119443s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.126070s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (98.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182232, over cnt = 38(0%), over = 38, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 1.817029s wall, 2.546875s user + 0.312500s system = 2.859375s CPU (157.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1732(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -47.296  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349558s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.8%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 2.166684s wall, 2.906250s user + 0.312500s system = 3.218750s CPU (148.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182232, over cnt = 38(0%), over = 38, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015343s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182120, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.034310s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (136.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182136, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.020491s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (152.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 182144, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021178s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1732(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -47.296  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.358281s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.177371s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.9%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.765864s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (102.0%)

PHY-1003 : Routed, final wirelength = 182144
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.009859s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (158.5%)

PHY-1001 : End detail routing;  7.542183s wall, 8.156250s user + 0.437500s system = 8.593750s CPU (113.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1946, tinst num: 813, tnode num: 8656, tedge num: 11208.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_55.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_61.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2857ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2857ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_73.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/tx_data_dy_b[2]_syn_20.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6477, tnet num: 2006, tinst num: 873, tnode num: 8776, tedge num: 11328.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -444ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -679ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -694ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -577ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -535ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -241ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -572ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -799ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -666ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -662ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -137ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -590ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -720ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -258ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -828ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[2]_syn_20_mi[0] slack -450ps
RUN-1001 : End hold fix;  3.221155s wall, 3.109375s user + 0.328125s system = 3.437500s CPU (106.7%)

RUN-1003 : finish command "route" in  11.270371s wall, 11.781250s user + 0.828125s system = 12.609375s CPU (111.9%)

RUN-1004 : used memory is 523 MB, reserved memory is 495 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      902   out of  19600    4.60%
#reg                     1053   out of  19600    5.37%
#le                      1592
  #lut only               539   out of   1592   33.86%
  #reg only               690   out of   1592   43.34%
  #lut&reg                363   out of   1592   22.80%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    44
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1592   |705     |197     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1081   |298     |133     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |17      |4       |18      |0       |0       |
|    demodu                  |Demodulation                                     |465    |109     |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |137    |24      |15      |109     |0       |0       |
|    modu                    |Modulation                                       |106    |53      |14      |104     |0       |1       |
|    rs422                   |Rs422Output                                      |315    |66      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |37     |29      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |176    |129     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |16      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |118    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |311    |266     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1392  
    #2          2       336   
    #3          3       114   
    #4          4        22   
    #5        5-10       68   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6477, tnet num: 2006, tinst num: 873, tnode num: 8776, tedge num: 11328.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 873
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14702
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1358 valid insts, and 39517 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.434747s wall, 18.890625s user + 0.125000s system = 19.015625s CPU (553.6%)

RUN-1004 : used memory is 520 MB, reserved memory is 493 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250724_134649.log"

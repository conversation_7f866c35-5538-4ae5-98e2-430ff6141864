============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Jul  9 16:20:44 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1536 instances
RUN-0007 : 391 luts, 898 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2067 nets
RUN-1001 : 1518 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     281     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1534 instances, 391 luts, 898 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7367, tnet num: 2065, tinst num: 1534, tnode num: 10344, tedge num: 12525.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.259607s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (102.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 545842
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1534.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473073, overlap = 20.25
PHY-3002 : Step(2): len = 450704, overlap = 18
PHY-3002 : Step(3): len = 439224, overlap = 18
PHY-3002 : Step(4): len = 424192, overlap = 13.5
PHY-3002 : Step(5): len = 399581, overlap = 11.25
PHY-3002 : Step(6): len = 353625, overlap = 11.25
PHY-3002 : Step(7): len = 342824, overlap = 13.5
PHY-3002 : Step(8): len = 336470, overlap = 9
PHY-3002 : Step(9): len = 327228, overlap = 9
PHY-3002 : Step(10): len = 317004, overlap = 9
PHY-3002 : Step(11): len = 311848, overlap = 11.25
PHY-3002 : Step(12): len = 302638, overlap = 13.5
PHY-3002 : Step(13): len = 294195, overlap = 13.5
PHY-3002 : Step(14): len = 289201, overlap = 13.5
PHY-3002 : Step(15): len = 282773, overlap = 13.5
PHY-3002 : Step(16): len = 275535, overlap = 13.5
PHY-3002 : Step(17): len = 270115, overlap = 15.75
PHY-3002 : Step(18): len = 264394, overlap = 15.75
PHY-3002 : Step(19): len = 258100, overlap = 20.25
PHY-3002 : Step(20): len = 252392, overlap = 20.25
PHY-3002 : Step(21): len = 247258, overlap = 20.25
PHY-3002 : Step(22): len = 241706, overlap = 20.25
PHY-3002 : Step(23): len = 236777, overlap = 20.25
PHY-3002 : Step(24): len = 230398, overlap = 20.25
PHY-3002 : Step(25): len = 225268, overlap = 20.25
PHY-3002 : Step(26): len = 220965, overlap = 20.25
PHY-3002 : Step(27): len = 215391, overlap = 20.25
PHY-3002 : Step(28): len = 207215, overlap = 20.25
PHY-3002 : Step(29): len = 202758, overlap = 20.25
PHY-3002 : Step(30): len = 199843, overlap = 20.25
PHY-3002 : Step(31): len = 189373, overlap = 20.25
PHY-3002 : Step(32): len = 179873, overlap = 20.25
PHY-3002 : Step(33): len = 177675, overlap = 20.25
PHY-3002 : Step(34): len = 172416, overlap = 20.25
PHY-3002 : Step(35): len = 146920, overlap = 20.25
PHY-3002 : Step(36): len = 139884, overlap = 20.25
PHY-3002 : Step(37): len = 138675, overlap = 20.25
PHY-3002 : Step(38): len = 127563, overlap = 20.25
PHY-3002 : Step(39): len = 121018, overlap = 20.25
PHY-3002 : Step(40): len = 119826, overlap = 20.25
PHY-3002 : Step(41): len = 115433, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000112894
PHY-3002 : Step(42): len = 117521, overlap = 18
PHY-3002 : Step(43): len = 117814, overlap = 15.75
PHY-3002 : Step(44): len = 115597, overlap = 15.75
PHY-3002 : Step(45): len = 114321, overlap = 18
PHY-3002 : Step(46): len = 114374, overlap = 15.75
PHY-3002 : Step(47): len = 110883, overlap = 13.5
PHY-3002 : Step(48): len = 109108, overlap = 9
PHY-3002 : Step(49): len = 105975, overlap = 11.25
PHY-3002 : Step(50): len = 104563, overlap = 11.25
PHY-3002 : Step(51): len = 101261, overlap = 13.5
PHY-3002 : Step(52): len = 100634, overlap = 11.25
PHY-3002 : Step(53): len = 98542, overlap = 13.5
PHY-3002 : Step(54): len = 96206.4, overlap = 15.75
PHY-3002 : Step(55): len = 92479.5, overlap = 15.75
PHY-3002 : Step(56): len = 91822.6, overlap = 15.75
PHY-3002 : Step(57): len = 90289.8, overlap = 15.75
PHY-3002 : Step(58): len = 88423.7, overlap = 13.5
PHY-3002 : Step(59): len = 85684, overlap = 13.5
PHY-3002 : Step(60): len = 84148.7, overlap = 13.5
PHY-3002 : Step(61): len = 82458.2, overlap = 13.5625
PHY-3002 : Step(62): len = 82495.1, overlap = 13.6875
PHY-3002 : Step(63): len = 81184.2, overlap = 16.125
PHY-3002 : Step(64): len = 78593.5, overlap = 16.8125
PHY-3002 : Step(65): len = 76246.6, overlap = 17
PHY-3002 : Step(66): len = 75490.5, overlap = 17.3125
PHY-3002 : Step(67): len = 73511.3, overlap = 17.5938
PHY-3002 : Step(68): len = 71604, overlap = 15.9688
PHY-3002 : Step(69): len = 70923.4, overlap = 16.25
PHY-3002 : Step(70): len = 70257, overlap = 18.9688
PHY-3002 : Step(71): len = 69529, overlap = 19.3125
PHY-3002 : Step(72): len = 68942.1, overlap = 19.5625
PHY-3002 : Step(73): len = 68060.1, overlap = 19.5625
PHY-3002 : Step(74): len = 67162.7, overlap = 19.4062
PHY-3002 : Step(75): len = 65801.9, overlap = 17.5312
PHY-3002 : Step(76): len = 64681.8, overlap = 20.0938
PHY-3002 : Step(77): len = 64616.7, overlap = 20.0938
PHY-3002 : Step(78): len = 63326.9, overlap = 18.2188
PHY-3002 : Step(79): len = 62776.7, overlap = 20.4688
PHY-3002 : Step(80): len = 61987.7, overlap = 18.2188
PHY-3002 : Step(81): len = 61066.7, overlap = 18.0938
PHY-3002 : Step(82): len = 60750.6, overlap = 18.9688
PHY-3002 : Step(83): len = 59678.9, overlap = 16.7188
PHY-3002 : Step(84): len = 59327.4, overlap = 16.7188
PHY-3002 : Step(85): len = 58392.3, overlap = 16.4688
PHY-3002 : Step(86): len = 57138.9, overlap = 16.4688
PHY-3002 : Step(87): len = 57153.9, overlap = 16.4688
PHY-3002 : Step(88): len = 56905.9, overlap = 16.4688
PHY-3002 : Step(89): len = 56461.7, overlap = 16.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000225789
PHY-3002 : Step(90): len = 56979.1, overlap = 16.4688
PHY-3002 : Step(91): len = 57075.3, overlap = 16.4688
PHY-3002 : Step(92): len = 57040.2, overlap = 16.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000451577
PHY-3002 : Step(93): len = 57265.6, overlap = 16.4688
PHY-3002 : Step(94): len = 57438.8, overlap = 14.2188
PHY-3001 : Before Legalized: Len = 57438.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008702s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (538.6%)

PHY-3001 : After Legalized: Len = 60997.7, Over = 2.96875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067354s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(95): len = 60675.1, overlap = 15.9688
PHY-3002 : Step(96): len = 60667.1, overlap = 15.7188
PHY-3002 : Step(97): len = 59736.5, overlap = 15.4688
PHY-3002 : Step(98): len = 59484.7, overlap = 14.9688
PHY-3002 : Step(99): len = 58417.4, overlap = 14.2812
PHY-3002 : Step(100): len = 57156.6, overlap = 14.4688
PHY-3002 : Step(101): len = 56348.7, overlap = 14.2812
PHY-3002 : Step(102): len = 55269.6, overlap = 14.6562
PHY-3002 : Step(103): len = 53587.9, overlap = 14.0312
PHY-3002 : Step(104): len = 52679.9, overlap = 13.9062
PHY-3002 : Step(105): len = 52063.9, overlap = 15.5625
PHY-3002 : Step(106): len = 51529.7, overlap = 15.3125
PHY-3002 : Step(107): len = 51071.7, overlap = 14.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000414891
PHY-3002 : Step(108): len = 50833, overlap = 16.4375
PHY-3002 : Step(109): len = 50541.4, overlap = 16.4375
PHY-3002 : Step(110): len = 50622.4, overlap = 16.7188
PHY-3002 : Step(111): len = 50431.1, overlap = 21.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000829781
PHY-3002 : Step(112): len = 50282.9, overlap = 21.1562
PHY-3002 : Step(113): len = 50298.4, overlap = 21.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067946s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.64622e-05
PHY-3002 : Step(114): len = 50647.4, overlap = 65.4375
PHY-3002 : Step(115): len = 51943.5, overlap = 54.6562
PHY-3002 : Step(116): len = 52336.2, overlap = 54.0312
PHY-3002 : Step(117): len = 52114.2, overlap = 53.0312
PHY-3002 : Step(118): len = 52253.8, overlap = 52.5938
PHY-3002 : Step(119): len = 52349.6, overlap = 52.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000152924
PHY-3002 : Step(120): len = 52652.7, overlap = 51.4688
PHY-3002 : Step(121): len = 53180.1, overlap = 52.9375
PHY-3002 : Step(122): len = 53280.8, overlap = 52.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000305849
PHY-3002 : Step(123): len = 54294, overlap = 49.7812
PHY-3002 : Step(124): len = 54780.3, overlap = 47.9062
PHY-3002 : Step(125): len = 55143.2, overlap = 45.8438
PHY-3002 : Step(126): len = 55184.2, overlap = 44.4688
PHY-3002 : Step(127): len = 55306.1, overlap = 40.4688
PHY-3002 : Step(128): len = 55392.8, overlap = 39.5312
PHY-3002 : Step(129): len = 54961.2, overlap = 39.4062
PHY-3002 : Step(130): len = 54614, overlap = 40.2812
PHY-3002 : Step(131): len = 54307.3, overlap = 38.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7367, tnet num: 2065, tinst num: 1534, tnode num: 10344, tedge num: 12525.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.56 peak overflow 2.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57336, over cnt = 220(0%), over = 802, worst = 14
PHY-1001 : End global iterations;  0.071819s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.0%)

PHY-1001 : Congestion index: top1 = 40.95, top5 = 24.38, top10 = 15.77, top15 = 11.21.
PHY-1001 : End incremental global routing;  0.130410s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (71.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071547s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (109.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.232924s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (87.2%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1468/2067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57336, over cnt = 220(0%), over = 802, worst = 14
PHY-1002 : len = 63440, over cnt = 119(0%), over = 200, worst = 6
PHY-1002 : len = 65296, over cnt = 25(0%), over = 33, worst = 4
PHY-1002 : len = 65680, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 65696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.081465s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (172.6%)

PHY-1001 : Congestion index: top1 = 34.74, top5 = 23.52, top10 = 16.92, top15 = 12.50.
OPT-1001 : End congestion update;  0.128361s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (133.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057862s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.189939s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (123.4%)

OPT-1001 : Current memory(MB): used = 211, reserve = 179, peak = 211.
OPT-1001 : End physical optimization;  0.682314s wall, 0.625000s user + 0.078125s system = 0.703125s CPU (103.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 391 LUT to BLE ...
SYN-4008 : Packed 391 LUT and 180 SEQ to BLE.
SYN-4003 : Packing 718 remaining SEQ's ...
SYN-4005 : Packed 96 SEQ with LUT/SLICE
SYN-4006 : 131 single LUT's are left
SYN-4006 : 622 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1013/1324 primitive instances ...
PHY-3001 : End packing;  0.048092s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 779 instances
RUN-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1895 nets
RUN-1001 : 1346 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 777 instances, 728 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54981, Over = 60
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1893, tinst num: 777, tnode num: 8426, tedge num: 11038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.295935s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (95.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.07751e-05
PHY-3002 : Step(132): len = 54334, overlap = 62.75
PHY-3002 : Step(133): len = 53850.6, overlap = 63.5
PHY-3002 : Step(134): len = 53181.9, overlap = 63.5
PHY-3002 : Step(135): len = 53057.2, overlap = 65.25
PHY-3002 : Step(136): len = 52760.5, overlap = 62.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.15503e-05
PHY-3002 : Step(137): len = 53071.2, overlap = 61.75
PHY-3002 : Step(138): len = 53627.4, overlap = 60.5
PHY-3002 : Step(139): len = 54450.1, overlap = 53
PHY-3002 : Step(140): len = 54732.2, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000163101
PHY-3002 : Step(141): len = 55050.3, overlap = 52.75
PHY-3002 : Step(142): len = 55981.9, overlap = 50.25
PHY-3002 : Step(143): len = 56225.2, overlap = 46.75
PHY-3002 : Step(144): len = 56109.3, overlap = 46.25
PHY-3001 : Before Legalized: Len = 56109.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077654s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (181.1%)

PHY-3001 : After Legalized: Len = 68178.8, Over = 0
PHY-3001 : Trial Legalized: Len = 68178.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051085s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00113834
PHY-3002 : Step(145): len = 64412.9, overlap = 4.5
PHY-3002 : Step(146): len = 62307, overlap = 9.5
PHY-3002 : Step(147): len = 60572.3, overlap = 16.5
PHY-3002 : Step(148): len = 59761.3, overlap = 19.5
PHY-3002 : Step(149): len = 59023.1, overlap = 22
PHY-3002 : Step(150): len = 58682.5, overlap = 23.75
PHY-3002 : Step(151): len = 58552, overlap = 24.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00227667
PHY-3002 : Step(152): len = 58676.5, overlap = 23.5
PHY-3002 : Step(153): len = 58705.2, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00455334
PHY-3002 : Step(154): len = 58870.1, overlap = 23.75
PHY-3002 : Step(155): len = 58898.8, overlap = 24.25
PHY-3001 : Before Legalized: Len = 58898.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005568s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (280.6%)

PHY-3001 : After Legalized: Len = 63406.1, Over = 0
PHY-3001 : Legalized: Len = 63406.1, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006105s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 7, deltaY = 11, maxDist = 3.
PHY-3001 : Final: Len = 63684.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1893, tinst num: 777, tnode num: 8426, tedge num: 11038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 58/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70808, over cnt = 150(0%), over = 243, worst = 6
PHY-1002 : len = 71816, over cnt = 72(0%), over = 98, worst = 4
PHY-1002 : len = 72760, over cnt = 18(0%), over = 24, worst = 2
PHY-1002 : len = 73048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115894s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (134.8%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.30, top10 = 18.24, top15 = 14.25.
PHY-1001 : End incremental global routing;  0.169916s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (128.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059742s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260155s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (120.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1671/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006809s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.30, top10 = 18.24, top15 = 14.25.
OPT-1001 : End congestion update;  0.054491s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050251s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 737 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 777 instances, 728 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63651, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005431s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 2, maxDist = 2.
PHY-3001 : Final: Len = 63715, Over = 0
PHY-3001 : End incremental legalization;  0.035233s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155042s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (120.9%)

OPT-1001 : Current memory(MB): used = 219, reserve = 186, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054419s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1667/1895.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009823s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.31, top10 = 18.23, top15 = 14.24.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060829s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.873538s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (109.1%)

RUN-1003 : finish command "place" in  5.334738s wall, 7.234375s user + 3.046875s system = 10.281250s CPU (192.7%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 219 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 779 instances
RUN-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1895 nets
RUN-1001 : 1346 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1893, tinst num: 777, tnode num: 8426, tedge num: 11038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 364 mslices, 364 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70080, over cnt = 159(0%), over = 256, worst = 6
PHY-1002 : len = 71032, over cnt = 80(0%), over = 113, worst = 4
PHY-1002 : len = 71824, over cnt = 36(0%), over = 51, worst = 4
PHY-1002 : len = 72488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118648s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (131.7%)

PHY-1001 : Congestion index: top1 = 31.85, top5 = 23.20, top10 = 18.20, top15 = 14.16.
PHY-1001 : End global routing;  0.172963s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (126.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.315337s wall, 3.234375s user + 0.062500s system = 3.296875s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.121456s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 525, reserve = 499, peak = 525.
PHY-1001 : End phase 1; 1.127514s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185128, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.595309s wall, 2.546875s user + 0.046875s system = 2.593750s CPU (162.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1680(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.288   |  -13.966  |  15   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.343359s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 1.938765s wall, 2.890625s user + 0.046875s system = 2.937500s CPU (151.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185128, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185048, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029929s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022461s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1680(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.288   |  -13.966  |  15   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.348728s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.179775s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.3%)

PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End phase 3; 0.725901s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.0%)

PHY-1003 : Routed, final wirelength = 185056
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010518s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (148.6%)

PHY-1001 : End detail routing;  7.307963s wall, 8.187500s user + 0.109375s system = 8.296875s CPU (113.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1893, tinst num: 777, tnode num: 8426, tedge num: 11038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.179738s wall, 9.109375s user + 0.109375s system = 9.218750s CPU (112.7%)

RUN-1004 : used memory is 497 MB, reserved memory is 471 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      787   out of  19600    4.02%
#reg                      986   out of  19600    5.03%
#le                      1409
  #lut only               423   out of   1409   30.02%
  #reg only               622   out of   1409   44.14%
  #lut&reg                364   out of   1409   25.83%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         444
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         103
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    46
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1409   |591     |196     |1019    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1040   |286     |134     |857     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |16      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |431    |91      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |42      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |13      |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |15      |0       |17      |0       |0       |
|    integ                   |Integration                                      |141    |28      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |99     |33      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |91      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |27      |7       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |140    |131     |7       |62      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |20     |18      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |84     |84      |0       |32      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |66      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1307  
    #2          2       300   
    #3          3       121   
    #4          4        14   
    #5        5-10       78   
    #6        11-50      30   
    #7       101-500     1    
  Average     2.01            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6240, tnet num: 1893, tinst num: 777, tnode num: 8426, tedge num: 11038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1893 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 777
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1895, pip num: 14570
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1291 valid insts, and 38041 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.387162s wall, 18.890625s user + 0.078125s system = 18.968750s CPU (560.0%)

RUN-1004 : used memory is 538 MB, reserved memory is 510 MB, peak memory is 661 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250709_162044.log"
